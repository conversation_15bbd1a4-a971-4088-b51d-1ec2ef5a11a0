{"pageProps": {"page": {"id": "ZAxBZRAAACkAXl5M", "uid": "google-arts-culture", "url": "/project/google-arts-culture", "type": "project", "href": "https://design-is-funny.cdn.prismic.io/api/v2/documents/search?ref=Z0hw5BEAAB8At7Hg&q=%5B%5B%3Ad+%3D+at%28document.id%2C+%22ZAxBZRAAACkAXl5M%22%29+%5D%5D", "tags": [], "first_publication_date": "2023-03-11T08:52:56+0000", "last_publication_date": "2023-05-04T07:15:54+0000", "slugs": ["google-ac", "google-arts--culture"], "linked_documents": [], "lang": "en-gb", "alternate_languages": [], "data": {"title": [{"type": "heading1", "text": "Google A&C", "spans": []}], "hyphenated_title": [], "type": "Digital Installation", "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=0,60,1920,960&w=3200&h=1600", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": 0, "y": -100, "zoom": 1.6666666666666667, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=0,35,1920,1008&w=1200&h=630", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": 0, "y": -22, "zoom": 0.625, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=420,0,1080,1080&w=3200&h=3200", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": -1244, "y": 0, "zoom": 2.962962962962963, "background": "transparent"}}}, "skills": [{"item": "UX"}, {"item": "Visual"}, {"item": "Motion"}], "slices": [{"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "The Project", "spans": []}], "first_column": [{"type": "paragraph", "text": "Draw to Art is a new experience from Google Arts & Culture that uses machine learning to match doodles to drawings, paintings and sculptures from museums around the world.", "spans": []}], "second_column": [{"type": "paragraph", "text": "Our approach to crafting the experience involved training a deep neural network to identify visual features within doodles and link them to corresponding features in artworks.", "spans": []}]}, "id": "paragraph$4efb92c4-11bc-43ef-b7b9-350e8d716f1b", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"video_1080p": {"link_type": "Media", "kind": "file", "id": "ZFNabxEAACMAmPOE", "url": "https://design-is-funny.cdn.prismic.io/design-is-funny/865d22ef-b91e-4fe3-a75a-2d2e8d4fc060_994917494.mp4", "name": "994917494.mp4", "size": "45222347"}, "video_720p": {"link_type": "Media"}, "video_540p": {"link_type": "Media"}, "video_360p": {"link_type": "Media"}}, "id": "video$184ffadb-5b96-4055-a448-7cec9b9709f2", "slice_type": "video", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ed632248-f5e8-4981-8e9d-08cd2a47101a_flower%2Beasel.jpeg?auto=compress,format", "id": "ZFNafxEAACMAmPPL", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$e8b1c561-5fbc-4f38-b7be-641f51aa2de2", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1079}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/36c77c96-4e63-42b4-9a2f-4a8f1c835d40_guitar.jpeg?auto=compress,format", "id": "ZFNaiREAACAAmPP6", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$b8d1f7f4-317d-45a9-9d54-3b67b01a1154", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "The Exhibitions", "spans": []}], "first_column": [{"type": "paragraph", "text": "We created a series of interactive easels to allow people to experience Draw to Art in a tangible way at cultural events and public spaces worldwide.", "spans": []}], "second_column": [{"type": "paragraph", "text": "Since 2018, these easels have been touring exhibitions in renowned locations such as the Grand Palais in Paris, the Long Museum in Shanghai, and events including MWC in Barcelona and Google I/O in San Francisco.", "spans": []}]}, "id": "paragraph$6337bd5e-dfa9-480e-9f6a-1e2e3f5d8bf1", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1079}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/c116e515-7ccf-4f8a-98aa-1d3f12a1152b_installation-view.jpeg?auto=compress,format", "id": "ZFNbjBEAACAAmPjH", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$062f9713-3d4a-432b-b675-37f72dfbe7b5", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"first_image": {"dimensions": {"width": 1920, "height": 1079}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/3427f64a-543f-4097-9a4f-b97f108bbd61_ML.jpeg?auto=compress,format", "id": "ZFNbnxEAACIAmPkm", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}, "second_image": {"dimensions": {"width": 1920, "height": 1079}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/a8a77a96-757d-41cf-baea-b4cc6c770653_mask.jpeg?auto=compress,format", "id": "ZFNbmxEAACMAmPkS", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image_columns$2a6c4242-0467-48da-8cb6-de57a9d7dbfc", "slice_type": "image_columns", "slice_label": null}], "meta_description": []}}, "nextProject": {"id": "ZsxTWBEAACYAuPvu", "type": "project", "tags": ["SynthTheatre"], "lang": "en-gb", "slug": "synthetic-theatre", "first_publication_date": "2024-08-28T11:47:18+0000", "last_publication_date": "2024-08-28T11:50:20+0000", "uid": "synththeatre", "url": "/project/synththeatre", "data": {"title": [{"type": "heading1", "text": "Synthetic Theatre", "spans": [], "direction": "ltr"}], "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=0,60,1920,960&w=3200&h=1600", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 0, "y": 60, "zoom": 1, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=0,36,1920,1008&w=1200&h=630", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 0, "y": 36, "zoom": 1, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=420,0,1080,1080&w=3200&h=3200", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 420, "y": 0, "zoom": 1, "background": "transparent"}}}}, "link_type": "Document", "isBroken": false}}, "__N_SSG": true}