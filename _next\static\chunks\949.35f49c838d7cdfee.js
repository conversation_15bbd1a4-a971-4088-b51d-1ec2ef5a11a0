"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[949],{9521:function(e,r,t){t.d(r,{S:function(){return i}});var n=t(9477),o=t(4671);let s=0,i=(0,o.Z)(e=>(n.DefaultLoadingManager.onStart=(r,t,n)=>{e({active:!0,item:r,loaded:t,total:n,progress:(t-s)/(n-s)*100})},n.DefaultLoadingManager.onLoad=()=>{e({active:!1})},n.DefaultLoadingManager.onError=r=>e(e=>({errors:[...e.errors,r]})),n.DefaultLoadingManager.onProgress=(r,t,n)=>{t===n&&(s=n),e({active:!0,item:r,loaded:t,total:n,progress:(t-s)/(n-s)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0}))},1103:function(e,r,t){t.r(r),t.d(r,{default:function(){return k}});var n=t(5893),o=t(7294),s=t(8626),i=t(9521),a=t(297),l=t(9477),c=t(230),u=t(7462);function d(e,r){let t=e+"Geometry";return o.forwardRef(({args:e,children:n,...s},i)=>{let a=o.useRef(null);return o.useImperativeHandle(i,()=>a.current),o.useLayoutEffect(()=>void(null==r||r(a.current))),o.createElement("mesh",(0,u.Z)({ref:a},s),o.createElement(t,{attach:"geometry",args:e}),n)})}let g=d("box");d("circle"),d("cone"),d("cylinder"),d("sphere"),d("plane"),d("tube"),d("torus"),d("torusKnot"),d("tetrahedron"),d("ring"),d("polyhedron"),d("icosahedron"),d("octahedron"),d("dodecahedron"),d("extrude"),d("lathe"),d("capsule"),d("shape",({geometry:e})=>{let r=e.attributes.position,t=new l.Box3().setFromBufferAttribute(r),n=new l.Vector3;t.getSize(n);let o=[],s=0,i=0,a=0,c=0;for(let e=0;e<r.count;e++)s=r.getX(e),i=r.getY(e),a=(s-t.min.x)/n.x,c=(i-t.min.y)/n.y,o.push(a,c);e.setAttribute("uv",new l.Float32BufferAttribute(o,2))});var h=t(1663),f=t(9656),m=t(9594),x=function(){return window.matchMedia("(pointer: coarse)").matches};function p(e,r,t){let n=new l.Matrix4;n.makeRotationAxis(r.normalize(),t),n.multiply(e.matrix),e.matrix=n,e.rotation.setFromRotationMatrix(e.matrix)}let b=e=>{let{id:r,initialPosition:t,initialRotation:s,distanceBetween:i,totalCount:a,...u}=e,d=(0,o.useRef)(),b=(0,o.useRef)(),j=(0,o.useRef)(),w=(0,o.useMemo)(()=>new l.Vector3(0,2*Math.random()-1,2*Math.random()-1),[]),y=(0,o.useRef)(null),v=(0,o.useRef)(null),D=(0,o.useRef)(null),R=(0,o.useRef)(.003),M=(0,o.useRef)(new l.Vector3(0,0,0)),S=(0,o.useRef)(new l.Vector3(0,0,0));(0,o.useEffect)(()=>{let e=e=>v.current=new l.Vector2(e.touches[0].clientX,e.touches[0].clientY),r=e=>{v.current&&(D.current=new l.Vector2(e.touches[0].clientX-v.current.x,e.touches[0].clientY-v.current.y),v.current=new l.Vector2(e.touches[0].clientX,e.touches[0].clientY))},t=()=>{m.Xr.isDragging=!1,m.Xr.isDragging=!1,m.Xr.draggedObjectId=null,D.current=null,v.current=null};return window.addEventListener("touchstart",e),window.addEventListener("touchmove",r),window.addEventListener("touchend",t),()=>{window.removeEventListener("touchstart",e),window.removeEventListener("touchmove",r),window.removeEventListener("touchend",t)}},[]),(0,c.A)((e,t)=>{let{mouse:n}=e;h.Ui.damp(R,"current",m.Xr.isDragging?0:3,.5,t),h.Ui.damp3(M.current,m.Xr.isDragging?new l.Vector3(0,0,0):w,.5,t),d.current.position.x+=.0012*R.current,j.current.rotation.x+=.001*M.current.x,j.current.rotation.y+=.001*M.current.y,j.current.rotation.z+=.001*M.current.z,d.current.position.x>.5*a*i&&(d.current.position.x-=a*i);let o=new l.Vector3(0,0,0);if(m.Xr.isDragging&&m.Xr.draggedObjectId===r){if(y.current){if(x())D.current&&(o=new l.Vector3(D.current.x,0,0));else{let e=n.clone().sub(y.current).multiplyScalar(100);o=new l.Vector3(e.x,e.y,0),y.current=n.clone()}}else y.current=n.clone()}else y.current=null;h.Ui.damp3(S.current,o,.5,t),p(b.current,new l.Vector3(0,1,0),.02*S.current.x),p(b.current,new l.Vector3(1,0,0),-(.02*S.current.y))}),(0,o.useEffect)(()=>{window.onmouseup=()=>m.Xr.isDragging=!1},[]);let k=(0,o.useCallback)(()=>{x()||(m.Xr.isDragging=!1,m.Xr.draggedObjectId=null)},[]);return(0,n.jsxs)("group",{ref:d,position:t,children:[(0,n.jsx)("group",{ref:b,children:(0,n.jsx)("group",{rotation:null!=s?s:[0,0,0],ref:j,children:(0,n.jsx)(f.Z,{...u})})}),(0,n.jsx)(g,{scale:6,onPointerDown:()=>{m.Xr.isDragging=!0,m.Xr.draggedObjectId=r||null},onPointerUp:k,children:(0,n.jsx)("meshBasicMaterial",{transparent:!0,opacity:0,side:l.DoubleSide})})]})};var j=t(1316),w=t(533),y=t(2149),v=t(5359),D=t(7078);let R=()=>{let e=(0,s.L)("/3d/smiley_logo.glb"),r=(0,o.useRef)(),t=(0,o.useMemo)(()=>[(0,n.jsx)(D.Z,{},"1"),(0,n.jsx)(j.Z,{},"2"),(0,n.jsx)(y.Z,{},"3"),(0,n.jsx)(v.Z,{},"4"),(0,n.jsx)(w.Z,{},"5")],[]);return(0,o.useEffect)(()=>{m.HS.threeLoadingProgress=100},[]),(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("group",{ref:r,children:t.map((r,o)=>(0,n.jsx)(b,{id:String(o),model:e,initialPosition:[6.3*o-12.6,0,0],initialRotation:[-.6,o%2==0?-.5:.5,0],totalCount:t.length,material:r,distanceBetween:6.3},o))})})},M=()=>((0,i.S)(e=>(0!==e.progress&&(m.HS.threeLoadingProgress=e.progress),e.progress)),(0,n.jsx)(o.Fragment,{})),S=()=>(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)(M,{}),children:(0,n.jsx)(a.Z,{runOnScrollTop:!0,className:"w-full h-full test",children:(0,n.jsx)(R,{})})});var k=(0,o.memo)(S)},297:function(e,r,t){t.d(r,{Z:function(){return x}});var n=t(5893),o=t(3520),s=t(3758),i=t(230),a=t(5029),l=t(9477),c=t(7294),u=t(8536),d=t(4503),g=t(2248);let h=()=>(0,n.jsx)(c.Suspense,{fallback:null,children:(0,n.jsxs)(d.xC,{children:[(0,n.jsx)(d.cy,{premultiply:!0,blendFunction:g.YQ.NORMAL,opacity:.4}),(0,n.jsx)(d.Ff,{brightness:0,contrast:.35})]})});var f=t(9827);(0,i.e)({Mesh:l.Mesh,Group:l.Group,Texture:l.Texture,ShaderMaterial:l.ShaderMaterial,Object3D:l.Object3D});let m=e=>{let{children:r,eventSource:t,className:i,runOnScrollTop:l}=e,d=(0,c.useRef)(null),g=(0,u.Z)(d,{root:null,rootMargin:"0px",threshold:0}),[m,x]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{x(0===window.scrollY);let e=()=>{l&&(0!==window.scrollY||m?m&&x(!1):x(!0))};return l&&window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[l,m]),(0,n.jsx)("div",{className:"w-full h-full",ref:d,children:(0,n.jsxs)(a.Xz,{dpr:[1,2],eventSource:t,className:i,gl:{antialias:!1,physicallyCorrectLights:!0},frameloop:l&&m||g&&g.intersectionRatio>0?"always":"demand",camera:{position:[0,0,10],fov:35},resize:{offsetSize:!0,debounce:{scroll:500,resize:500}},style:{touchAction:"auto"},children:[(0,n.jsx)(o.E,{}),(0,n.jsx)(f.Z,{}),(0,n.jsx)(h,{}),(0,n.jsx)(s.S,{pixelated:!0}),r]})})};var x=m},9827:function(e,r,t){var n=t(5893),o=t(2749);let s=()=>(0,n.jsx)(o.qA,{files:"/3d/lobby.hdr"});r.Z=s},9656:function(e,r,t){var n=t(5893);let o=e=>{let{model:{nodes:r},material:t,mouthRef:o,eyesRef:s,leftEyeRef:i,rightEyeRef:a}=e,l=r.eye_1,c=r.eye_2,u=r.smile,d=r.outer_ring;return l.geometry.center(),c.geometry.center(),(0,n.jsxs)("group",{rotation:[Math.PI/2,0,0],scale:2.55,children:[(0,n.jsxs)("group",{ref:s,children:[(0,n.jsx)("mesh",{position:[-.51,0,-.3],ref:i,geometry:l.geometry,children:t}),(0,n.jsx)("mesh",{position:[.51,0,-.3],ref:a,geometry:c.geometry,children:t})]}),(0,n.jsx)("mesh",{ref:o,geometry:u.geometry,children:t}),(0,n.jsx)("mesh",{geometry:d.geometry,children:t})]})};r.Z=o},5359:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),i=t(8197);let a=()=>{let e=(0,s.D)(i.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{color:"#C9A15C",clearcoat:.4,clearcoatRoughness:.1,metalness:.6,roughness:.3,thickness:.8,transmission:.2,envMapIntensity:.9,distortionScale:0,temporalDistortion:0,ior:2,buffer:e})};r.Z=a},7078:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),i=t(8197);let a=()=>{let e=(0,s.D)(i.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{distortionScale:1,temporalDistortion:0,distortion:0,transmission:.8,color:"#ddd",backside:!0,roughness:.25,thickness:.4,chromaticAberration:1,metalness:.2,clearcoat:0,clearcoatRoughness:.1,backsideResolution:32,resolution:512,samples:1,ior:1.8,buffer:e})};r.Z=a},1316:function(e,r,t){var n=t(5893),o=t(5769),s=t(3435);let i=()=>{let e=(0,o.m)("/3d/gradient_bg.jpg");return e.repeat.set(3,3),(0,n.jsx)(s.z,{background:e,distortionScale:1,distortion:0,temporalDistortion:0,transmission:1.2,color:"#fff",resolution:16,chromaticAberration:20,roughness:.2,thickness:100,metalness:.3,clearcoat:0,clearcoatRoughness:.1,envMapIntensity:.6,buffer:e})};r.Z=i},533:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),i=t(8197);let a=()=>{let e=(0,s.D)(i.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{distortionScale:1,temporalDistortion:0,distortion:0,transmission:.6,color:"#4a4",roughness:.22,thickness:.4,chromaticAberration:1,metalness:.1,clearcoat:.3,clearcoatRoughness:.1,ior:1.8,background:e,samples:1,envMapIntensity:1,backside:!0,buffer:e,backsideResolution:32,resolution:512})};r.Z=a},2149:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),i=t(8197);let a=()=>{let e=(0,s.D)(i.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{color:"#ddf",clearcoat:.4,clearcoatRoughness:.1,roughness:.2,envMapIntensity:1,ior:2,metalness:.1,transmission:.6,buffer:e,thickness:20,distortionScale:0,temporalDistortion:0})};r.Z=a}}]);