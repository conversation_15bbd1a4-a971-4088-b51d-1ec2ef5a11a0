(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[842],{172:function(e,t,n){"use strict";n.d(t,{p:function(){return s}});var o=n(5893),r=n(7294),i=n(2194);let l=e=>{let t=e.replace(/(?:-|_)(\w)/g,(e,t)=>t?t.toUpperCase():"");return t[0].toUpperCase()+t.slice(1)},a=i.N?()=>null:({slice:e})=>{let t="slice_type"in e?e.slice_type:e.type;return r.useEffect(()=>{console.warn(`[SliceZone] Could not find a component for Slice type "${t}"`,e)},[e,t]),(0,o.jsxs)("section",{"data-slice-zone-todo-component":"","data-slice-type":t,children:["Could not find a component for Slice type “",t,"”"]})},s=({slices:e=[],components:t={},resolver:n,defaultComponent:i=a,context:s={}})=>{let c=r.useMemo(()=>e.map((r,a)=>{let c="slice_type"in r?r.slice_type:r.type,p=t[c]||i;if(n){let e=n({slice:r,sliceName:l(c),i:a});e&&(p=e)}let u="id"in r&&r.id?r.id:`${a}-${JSON.stringify(r)}`;return(0,o.jsx)(p,{slice:r,index:a,slices:e,context:s},u)}),[t,s,i,e,n]);return(0,o.jsx)(o.Fragment,{children:c})}},3145:function(e,t){!function(e){"use strict";function t(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var n,o,r,i,l,a,s,c,p,u="transform",d=u+"Origin",f=function(e){var t=e.ownerDocument||e;for(!(u in e.style)&&("msTransform"in e.style)&&(d=(u="msTransform")+"Origin");t.parentNode&&(t=t.parentNode););if(o=window,s=new E,t){n=t,r=t.documentElement,i=t.body,(c=n.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var l=t.createElement("div"),a=t.createElement("div");i.appendChild(l),l.appendChild(a),l.style.position="static",l.style[u]="translate3d(0,0,1px)",p=a.offsetParent!==l,i.removeChild(l)}return t},h=function(e){for(var t,n;e&&e!==i;)(n=e._gsap)&&n.uncache&&n.get(e,"x"),n&&!n.scaleX&&!n.scaleY&&n.renderTransform&&(n.scaleX=n.scaleY=1e-4,n.renderTransform(1,n),t?t.push(n):t=[n]),e=e.parentNode;return t},g=[],x=[],m=function(e){return e.ownerSVGElement||("svg"===(e.tagName+"").toLowerCase()?e:null)},y=function e(t,o){if(t.parentNode&&(n||f(t))){var r=m(t),i=r?r.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",s=r?o?"rect":"g":"div",c=2!==o?0:100,p=3===o?100:0,u="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",d=n.createElementNS?n.createElementNS(i.replace(/^https/,"http"),s):n.createElement(s);return o&&(r?(a||(a=e(t)),d.setAttribute("width",.01),d.setAttribute("height",.01),d.setAttribute("transform","translate("+c+","+p+")"),a.appendChild(d)):(l||((l=e(t)).style.cssText=u),d.style.cssText=u+"width:0.1px;height:0.1px;top:"+p+"px;left:"+c+"px",l.appendChild(d))),d}throw"Need document and parent."},v=function(e){for(var t=new E,n=0;n<e.numberOfItems;n++)t.multiply(e.getItem(n).matrix);return t},w=function(e){var t,n=e.getCTM();return n||(t=e.style[u],e.style[u]="none",e.appendChild(c),n=c.getCTM(),e.removeChild(c),t?e.style[u]=t:e.style.removeProperty(u.replace(/([A-Z])/g,"-$1").toLowerCase())),n||s.clone()},b=function(e,t){var n,r,i,c,f,h,b=m(e),T=e===b,M=b?g:x,S=e.parentNode;if(e===o)return e;if(M.length||M.push(y(e,1),y(e,2),y(e,3)),n=b?a:l,b)T?(c=-(i=w(e)).e/i.a,f=-i.f/i.d,r=s):e.getBBox?(i=e.getBBox(),c=(r=(r=e.transform?e.transform.baseVal:{}).numberOfItems?r.numberOfItems>1?v(r):r.getItem(0).matrix:s).a*i.x+r.c*i.y,f=r.b*i.x+r.d*i.y):(r=new E,c=f=0),t&&"g"===e.tagName.toLowerCase()&&(c=f=0),(T?b:S).appendChild(n),n.setAttribute("transform","matrix("+r.a+","+r.b+","+r.c+","+r.d+","+(r.e+c)+","+(r.f+f)+")");else{if(c=f=0,p)for(r=e.offsetParent,i=e;i&&(i=i.parentNode)&&i!==r&&i.parentNode;)(o.getComputedStyle(i)[u]+"").length>4&&(c=i.offsetLeft,f=i.offsetTop,i=0);if("absolute"!==(h=o.getComputedStyle(e)).position&&"fixed"!==h.position)for(r=e.offsetParent;S&&S!==r;)c+=S.scrollLeft||0,f+=S.scrollTop||0,S=S.parentNode;(i=n.style).top=e.offsetTop-f+"px",i.left=e.offsetLeft-c+"px",i[u]=h[u],i[d]=h[d],i.position="fixed"===h.position?"fixed":"absolute",e.parentNode.appendChild(n)}return n},T=function(e,t,n,o,r,i,l){return e.a=t,e.b=n,e.c=o,e.d=r,e.e=i,e.f=l,e},E=function(){function e(e,t,n,o,r,i){void 0===e&&(e=1),void 0===t&&(t=0),void 0===n&&(n=0),void 0===o&&(o=1),void 0===r&&(r=0),void 0===i&&(i=0),T(this,e,t,n,o,r,i)}var t=e.prototype;return t.inverse=function(){var e=this.a,t=this.b,n=this.c,o=this.d,r=this.e,i=this.f,l=e*o-t*n||1e-10;return T(this,o/l,-t/l,-n/l,e/l,(n*i-o*r)/l,-(e*i-t*r)/l)},t.multiply=function(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,l=this.f,a=e.a,s=e.c,c=e.b,p=e.d,u=e.e,d=e.f;return T(this,a*t+c*o,a*n+c*r,s*t+p*o,s*n+p*r,i+u*t+d*o,l+u*n+d*r)},t.clone=function(){return new e(this.a,this.b,this.c,this.d,this.e,this.f)},t.equals=function(e){var t=this.a,n=this.b,o=this.c,r=this.d,i=this.e,l=this.f;return t===e.a&&n===e.b&&o===e.c&&r===e.d&&i===e.e&&l===e.f},t.apply=function(e,t){void 0===t&&(t={});var n=e.x,o=e.y,r=this.a,i=this.b,l=this.c,a=this.d,s=this.e,c=this.f;return t.x=n*r+o*l+s||0,t.y=n*i+o*a+c||0,t},e}();function M(e,t,l,a){if(!e||!e.parentNode||(n||f(e)).documentElement===e)return new E;var s=h(e),c=m(e)?g:x,p=b(e,l),u=c[0].getBoundingClientRect(),d=c[1].getBoundingClientRect(),y=c[2].getBoundingClientRect(),v=p.parentNode,w=!a&&function e(t){return"fixed"===o.getComputedStyle(t).position||((t=t.parentNode)&&1===t.nodeType?e(t):void 0)}(e),T=new E((d.left-u.left)/100,(d.top-u.top)/100,(y.left-u.left)/100,(y.top-u.top)/100,u.left+(w?0:o.pageXOffset||n.scrollLeft||r.scrollLeft||i.scrollLeft||0),u.top+(w?0:o.pageYOffset||n.scrollTop||r.scrollTop||i.scrollTop||0));if(v.removeChild(p),s)for(u=s.length;u--;)(d=s[u]).scaleX=d.scaleY=0,d.renderTransform(1,d);return t?T.inverse():T}var S,X,C,k,Y,D,N,L,P,_,O,R,A,B,I,H,F,W,z,V,j,K,U=0,$=function(){return"undefined"!=typeof window},G=function(){return S||$()&&(S=window.gsap)&&S.registerPlugin&&S},q=function(e){return"function"==typeof e},Z=function(e){return"object"==typeof e},J=function(e){return void 0===e},Q=function(){return!1},ee="transform",et="transformOrigin",en=function(e){return Math.round(1e4*e)/1e4},eo=Array.isArray,er=function(e,t){var n=C.createElementNS?C.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):C.createElement(e);return n.style?n:C.createElement(e)},ei=180/Math.PI,el=new E,ea=Date.now||function(){return new Date().getTime()},es=[],ec={},ep=0,eu=/^(?:a|input|textarea|button|select)$/i,ed=0,ef={},eh={},eg=function(e,t){var n,o={};for(n in e)o[n]=t?e[n]*t:e[n];return o},ex=function(e,t){for(var n in t)n in e||(e[n]=t[n]);return e},em=function e(t,n){for(var o,r=t.length;r--;)n?t[r].style.touchAction=n:t[r].style.removeProperty("touch-action"),(o=t[r].children)&&o.length&&e(o,n)},ey=function(){return es.forEach(function(e){return e()})},ev=function(e){es.push(e),1===es.length&&S.ticker.add(ey)},ew=function(){return!es.length&&S.ticker.remove(ey)},eb=function(e){for(var t=es.length;t--;)es[t]===e&&es.splice(t,1);S.to(ew,{overwrite:!0,delay:15,duration:0,onComplete:ew,data:"_draggable"})},eT=function(e,t,n,o){if(e.addEventListener){var r=A[t];o=o||(O?{passive:!1}:null),e.addEventListener(r||t,n,o),r&&t!==r&&e.addEventListener(t,n,o)}},eE=function(e,t,n,o){if(e.removeEventListener){var r=A[t];e.removeEventListener(r||t,n,o),r&&t!==r&&e.removeEventListener(t,n,o)}},eM=function(e){e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation()},eS=function(e,t){for(var n=e.length;n--;)if(e[n].identifier===t)return!0},eX=function e(t){B=t.touches&&U<t.touches.length,eE(t.target,"touchend",e)},eC=function(e){B=e.touches&&U<e.touches.length,eT(e.target,"touchend",eX)},ek=function(e){return X.pageYOffset||e.scrollTop||e.documentElement.scrollTop||e.body.scrollTop||0},eY=function(e){return X.pageXOffset||e.scrollLeft||e.documentElement.scrollLeft||e.body.scrollLeft||0},eD=function e(t,n){eT(t,"scroll",n),eL(t.parentNode)||e(t.parentNode,n)},eN=function e(t,n){eE(t,"scroll",n),eL(t.parentNode)||e(t.parentNode,n)},eL=function(e){return!!(!e||e===k||9===e.nodeType||e===C.body||e===X||!e.nodeType||!e.parentNode)},eP=function(e,t){var n="x"===t?"Width":"Height",o="scroll"+n,r="client"+n;return Math.max(0,eL(e)?Math.max(k[o],Y[o])-(X["inner"+n]||k[r]||Y[r]):e[o]-e[r])},e_=function e(t,n){var o=eP(t,"x"),r=eP(t,"y");eL(t)?t=eh:e(t.parentNode,n),t._gsMaxScrollX=o,t._gsMaxScrollY=r,n||(t._gsScrollX=t.scrollLeft||0,t._gsScrollY=t.scrollTop||0)},eO=function(e,t,n){var o=e.style;o&&(J(o[t])&&(t=P(t,e)||t),null==n?o.removeProperty&&o.removeProperty(t.replace(/([A-Z])/g,"-$1").toLowerCase()):o[t]=n)},eR=function(e){return X.getComputedStyle(e instanceof Element?e:e.host||(e.parentNode||{}).host||e)},eA={},eB=function(e){if(e===X)return eA.left=eA.top=0,eA.width=eA.right=k.clientWidth||e.innerWidth||Y.clientWidth||0,eA.height=eA.bottom=(e.innerHeight||0)-20<k.clientHeight?k.clientHeight:e.innerHeight||Y.clientHeight||0,eA;var t=e.ownerDocument||C,n=J(e.pageX)?e.nodeType||J(e.left)||J(e.top)?_(e)[0].getBoundingClientRect():e:{left:e.pageX-eY(t),top:e.pageY-ek(t),right:e.pageX-eY(t)+1,bottom:e.pageY-ek(t)+1};return J(n.right)&&!J(n.width)?(n.right=n.left+n.width,n.bottom=n.top+n.height):J(n.width)&&(n={width:n.right-n.left,height:n.bottom-n.top,right:n.right,left:n.left,bottom:n.bottom,top:n.top}),n},eI=function(e,t,n){var o,r=e.vars,i=r[n],l=e._listeners[t];return q(i)&&(o=i.apply(r.callbackScope||e,r[n+"Params"]||[e.pointerEvent])),l&&!1===e.dispatchEvent(t)&&(o=!1),o},eH=function(e,t){var n,o,r,i=_(e)[0];return i.nodeType||i===X?eW(i,t):J(e.left)?{left:o=e.min||e.minX||e.minRotation||0,top:n=e.min||e.minY||0,width:(e.max||e.maxX||e.maxRotation||0)-o,height:(e.max||e.maxY||0)-n}:(r={x:0,y:0},{left:e.left-r.x,top:e.top-r.y,width:e.width,height:e.height})},eF={},eW=function(e,t){t=_(t)[0];var n,o,r,i,l,a,s,c,p,u,d,f,h,g=e.getBBox&&e.ownerSVGElement,x=e.ownerDocument||C;if(e===X)r=ek(x),o=(n=eY(x))+(x.documentElement.clientWidth||e.innerWidth||x.body.clientWidth||0),i=r+((e.innerHeight||0)-20<x.documentElement.clientHeight?x.documentElement.clientHeight:e.innerHeight||x.body.clientHeight||0);else{if(t===X||J(t))return e.getBoundingClientRect();n=r=0,g?(d=(u=e.getBBox()).width,f=u.height):(e.viewBox&&(u=e.viewBox.baseVal)&&(n=u.x||0,r=u.y||0,d=u.width,f=u.height),d||(u="border-box"===(h=eR(e)).boxSizing,d=(parseFloat(h.width)||e.clientWidth||0)+(u?0:parseFloat(h.borderLeftWidth)+parseFloat(h.borderRightWidth)),f=(parseFloat(h.height)||e.clientHeight||0)+(u?0:parseFloat(h.borderTopWidth)+parseFloat(h.borderBottomWidth)))),o=d,i=f}return e===t?{left:n,top:r,width:o-n,height:i-r}:(a=(l=M(t,!0).multiply(M(e))).apply({x:n,y:r}),s=l.apply({x:o,y:r}),c=l.apply({x:o,y:i}),p=l.apply({x:n,y:i}),n=Math.min(a.x,s.x,c.x,p.x),r=Math.min(a.y,s.y,c.y,p.y),{left:n,top:r,width:Math.max(a.x,s.x,c.x,p.x)-n,height:Math.max(a.y,s.y,c.y,p.y)-r})},ez=function(e,t,n,o,r,i){var l,a,s,c={};if(t){if(1!==r&&t instanceof Array){if(c.end=l=[],s=t.length,Z(t[0]))for(a=0;a<s;a++)l[a]=eg(t[a],r);else for(a=0;a<s;a++)l[a]=t[a]*r;n+=1.1,o-=1.1}else q(t)?c.end=function(n){var o,i,l=t.call(e,n);if(1!==r){if(Z(l)){for(i in o={},l)o[i]=l[i]*r;l=o}else l*=r}return l}:c.end=t}return(n||0===n)&&(c.max=n),(o||0===o)&&(c.min=o),i&&(c.velocity=0),c},eV=function e(t){var n;return!!t&&!!t.getAttribute&&t!==Y&&(!!("true"===(n=t.getAttribute("data-clickable"))||"false"!==n&&(t.onclick||eu.test(t.nodeName+"")||"true"===t.getAttribute("contentEditable")))||e(t.parentNode))},ej=function(e,t){for(var n,o=e.length;o--;)(n=e[o]).ondragstart=n.onselectstart=t?null:Q,S.set(n,{lazy:!0,userSelect:t?"text":"none"})},eK=function(e,t){e=S.utils.toArray(e)[0],t=t||{};var n,o,r,i,l,a,s=document.createElement("div"),c=s.style,p=e.firstChild,u=0,d=0,f=e.scrollTop,h=e.scrollLeft,g=e.scrollWidth,x=e.scrollHeight,m=0,y=0,v=0;j&&!1!==t.force3D?(l="translate3d(",a="px,0px)"):ee&&(l="translate(",a="px)"),this.scrollTop=function(e,t){if(!arguments.length)return-this.top();this.top(-e,t)},this.scrollLeft=function(e,t){if(!arguments.length)return-this.left();this.left(-e,t)},this.left=function(n,o){if(!arguments.length)return-(e.scrollLeft+d);var r=e.scrollLeft-h,i=d;if((r>2||r<-2)&&!o){h=e.scrollLeft,S.killTweensOf(this,{left:1,scrollLeft:1}),this.left(-h),t.onKill&&t.onKill();return}(n=-n)<0?(d=n-.5|0,n=0):n>y?(d=n-y|0,n=y):d=0,(d||i)&&(this._skip||(c[ee]=l+-d+"px,"+-u+a),d+m>=0&&(c.paddingRight=d+m+"px")),e.scrollLeft=0|n,h=e.scrollLeft},this.top=function(n,o){if(!arguments.length)return-(e.scrollTop+u);var r=e.scrollTop-f,i=u;if((r>2||r<-2)&&!o){f=e.scrollTop,S.killTweensOf(this,{top:1,scrollTop:1}),this.top(-f),t.onKill&&t.onKill();return}(n=-n)<0?(u=n-.5|0,n=0):n>v?(u=n-v|0,n=v):u=0,(u||i)&&!this._skip&&(c[ee]=l+-d+"px,"+-u+a),e.scrollTop=0|n,f=e.scrollTop},this.maxScrollTop=function(){return v},this.maxScrollLeft=function(){return y},this.disable=function(){for(p=s.firstChild;p;)i=p.nextSibling,e.appendChild(p),p=i;e===s.parentNode&&e.removeChild(s)},this.enable=function(){if((p=e.firstChild)!==s){for(;p;)i=p.nextSibling,s.appendChild(p),p=i;e.appendChild(s),this.calibrate()}},this.calibrate=function(t){var i,l,a,p=e.clientWidth===n;f=e.scrollTop,h=e.scrollLeft,(!p||e.clientHeight!==o||s.offsetHeight!==r||g!==e.scrollWidth||x!==e.scrollHeight||t)&&((u||d)&&(l=this.left(),a=this.top(),this.left(-e.scrollLeft),this.top(-e.scrollTop)),i=eR(e),(!p||t)&&(c.display="block",c.width="auto",c.paddingRight="0px",(m=Math.max(0,e.scrollWidth-e.clientWidth))&&(m+=parseFloat(i.paddingLeft)+(K?parseFloat(i.paddingRight):0))),c.display="inline-block",c.position="relative",c.overflow="visible",c.verticalAlign="top",c.boxSizing="content-box",c.width="100%",c.paddingRight=m+"px",K&&(c.paddingBottom=i.paddingBottom),n=e.clientWidth,o=e.clientHeight,g=e.scrollWidth,x=e.scrollHeight,y=e.scrollWidth-n,v=e.scrollHeight-o,r=s.offsetHeight,c.display="block",(l||a)&&(this.left(l),this.top(a)))},this.content=s,this.element=e,this._skip=!1,this.enable()},eU=function(e){if($()&&document.body){var t,n,o,r,i,l=window&&window.navigator;X=window,k=(C=document).documentElement,Y=C.body,D=er("div"),W=!!window.PointerEvent,(N=er("div")).style.cssText="visibility:hidden;height:1px;top:-1px;pointer-events:none;position:relative;clear:both;cursor:grab",F="grab"===N.style.cursor?"grab":"move",I=l&&-1!==l.userAgent.toLowerCase().indexOf("android"),R="ontouchstart"in k&&"orientation"in X||l&&(l.MaxTouchPoints>0||l.msMaxTouchPoints>0),n=er("div"),r=(o=er("div")).style,i=Y,r.display="inline-block",r.position="relative",n.style.cssText="width:90px;height:40px;padding:10px;overflow:auto;visibility:hidden",n.appendChild(o),i.appendChild(n),t=o.offsetHeight+18>n.scrollHeight,i.removeChild(n),K=t,A=function(e){for(var t=e.split(","),n=(("onpointerdown"in D)?"pointerdown,pointermove,pointerup,pointercancel":("onmspointerdown"in D)?"MSPointerDown,MSPointerMove,MSPointerUp,MSPointerCancel":e).split(","),o={},r=4;--r>-1;)o[t[r]]=n[r],o[n[r]]=t[r];try{k.addEventListener("test",null,Object.defineProperty({},"passive",{get:function(){O=1}}))}catch(e){}return o}("touchstart,touchmove,touchend,touchcancel"),eT(C,"touchcancel",Q),eT(X,"touchmove",Q),Y&&Y.addEventListener("touchstart",Q),eT(C,"contextmenu",function(){for(var e in ec)ec[e].isPressed&&ec[e].endDrag()}),S=L=G()}S?(H=S.plugins.inertia,z=S.core.context||function(){},ee=(P=S.utils.checkPrefix)(ee),et=P(et),_=S.utils.toArray,V=S.core.getStyleSaver,j=!!P("perspective")):e&&console.warn("Please gsap.registerPlugin(Draggable)")},e$=function(e){var n;function o(n,r){i=e.call(this)||this,L||eU(1),n=_(n)[0],i.styles=V&&V(n,"transform,left,top"),H||(H=S.plugins.inertia),i.vars=r=eg(r||{}),i.target=n,i.x=i.y=i.rotation=0,i.dragResistance=parseFloat(r.dragResistance)||0,i.edgeResistance=isNaN(r.edgeResistance)?1:parseFloat(r.edgeResistance)||0,i.lockAxis=r.lockAxis,i.autoScroll=r.autoScroll||0,i.lockedAxis=null,i.allowEventDefault=!!r.allowEventDefault,S.getProperty(n,"x");var i,l,a,s,c,p,u,d,f,h,g,x,m,y,v,w,b,T,Y,D,P,O,j,K,$,G,Q,ee,er,es,eu,ey,ew,eX,eP=(r.type||"x,y").toLowerCase(),eA=~eP.indexOf("x")||~eP.indexOf("y"),eW=-1!==eP.indexOf("rotation"),e$=eW?"rotation":eA?"x":"left",eG=eA?"y":"top",eq=!!(~eP.indexOf("x")||~eP.indexOf("left")||"scroll"===eP),eZ=!!(~eP.indexOf("y")||~eP.indexOf("top")||"scroll"===eP),eJ=r.minimumMovement||2,eQ=t(i),e0=_(r.trigger||r.handle||n),e1={},e2=0,e3=!1,e4=r.autoScrollMarginTop||40,e9=r.autoScrollMarginRight||40,e5=r.autoScrollMarginBottom||40,e8=r.autoScrollMarginLeft||40,e6=r.clickableTest||eV,e7=0,te=n._gsap||S.core.getCache(n),tt=function e(t){return"fixed"===eR(t).position||((t=t.parentNode)&&1===t.nodeType?e(t):void 0)}(n),tn=function(e,t){return parseFloat(te.get(n,e,t))},to=n.ownerDocument||C,tr=function(e){return eM(e),e.stopImmediatePropagation&&e.stopImmediatePropagation(),!1},ti=function e(t){if(eQ.autoScroll&&eQ.isDragging&&(e3||T)){var o,r,i,l,s,c,p,u,d=n,h=15*eQ.autoScroll;for(e3=!1,eh.scrollTop=null!=X.pageYOffset?X.pageYOffset:null!=to.documentElement.scrollTop?to.documentElement.scrollTop:to.body.scrollTop,eh.scrollLeft=null!=X.pageXOffset?X.pageXOffset:null!=to.documentElement.scrollLeft?to.documentElement.scrollLeft:to.body.scrollLeft,l=eQ.pointerX-eh.scrollLeft,s=eQ.pointerY-eh.scrollTop;d&&!r;)o=(r=eL(d.parentNode))?eh:d.parentNode,i=r?{bottom:Math.max(k.clientHeight,X.innerHeight||0),right:Math.max(k.clientWidth,X.innerWidth||0),left:0,top:0}:o.getBoundingClientRect(),c=p=0,eZ&&((u=o._gsMaxScrollY-o.scrollTop)<0?p=u:s>i.bottom-e5&&u?(e3=!0,p=Math.min(u,h*(1-Math.max(0,i.bottom-s)/e5)|0)):s<i.top+e4&&o.scrollTop&&(e3=!0,p=-Math.min(o.scrollTop,h*(1-Math.max(0,s-i.top)/e4)|0)),p&&(o.scrollTop+=p)),eq&&((u=o._gsMaxScrollX-o.scrollLeft)<0?c=u:l>i.right-e9&&u?(e3=!0,c=Math.min(u,h*(1-Math.max(0,i.right-l)/e9)|0)):l<i.left+e8&&o.scrollLeft&&(e3=!0,c=-Math.min(o.scrollLeft,h*(1-Math.max(0,l-i.left)/e8)|0)),c&&(o.scrollLeft+=c)),r&&(c||p)&&(X.scrollTo(o.scrollLeft,o.scrollTop),ty(eQ.pointerX+c,eQ.pointerY+p)),d=o}if(T){var g=eQ.x,x=eQ.y;eW?(eQ.deltaX=g-parseFloat(te.rotation),eQ.rotation=g,te.rotation=g+"deg",te.renderTransform(1,te)):a?(eZ&&(eQ.deltaY=x-a.top(),a.top(x)),eq&&(eQ.deltaX=g-a.left(),a.left(g))):eA?(eZ&&(eQ.deltaY=x-parseFloat(te.y),te.y=x+"px"),eq&&(eQ.deltaX=g-parseFloat(te.x),te.x=g+"px"),te.renderTransform(1,te)):(eZ&&(eQ.deltaY=x-parseFloat(n.style.top||0),n.style.top=x+"px"),eq&&(eQ.deltaX=g-parseFloat(n.style.left||0),n.style.left=g+"px")),!f||t||er||(er=!0,!1===eI(eQ,"drag","onDrag")&&(eq&&(eQ.x-=eQ.deltaX),eZ&&(eQ.y-=eQ.deltaY),e(!0)),er=!1)}T=!1},tl=function(e,t){var o,r,i=eQ.x,l=eQ.y;n._gsap||(te=S.core.getCache(n)),te.uncache&&S.getProperty(n,"x"),eA?(eQ.x=parseFloat(te.x),eQ.y=parseFloat(te.y)):eW?eQ.x=eQ.rotation=parseFloat(te.rotation):a?(eQ.y=a.top(),eQ.x=a.left()):(eQ.y=parseFloat(n.style.top||(r=eR(n))&&r.top)||0,eQ.x=parseFloat(n.style.left||(r||{}).left)||0),(D||P||O)&&!t&&(eQ.isDragging||eQ.isThrowing)&&(O&&(ef.x=eQ.x,ef.y=eQ.y,(o=O(ef)).x!==eQ.x&&(eQ.x=o.x,T=!0),o.y!==eQ.y&&(eQ.y=o.y,T=!0)),D&&(o=D(eQ.x))!==eQ.x&&(eQ.x=o,eW&&(eQ.rotation=o),T=!0),P&&((o=P(eQ.y))!==eQ.y&&(eQ.y=o),T=!0)),T&&ti(!0),e||(eQ.deltaX=eQ.x-i,eQ.deltaY=eQ.y-l,eI(eQ,"throwupdate","onThrowUpdate"))},ta=function(e,t,n,o){return(null==t&&(t=-1e20),null==n&&(n=1e20),q(e))?function(r){var i=eQ.isPressed?1-eQ.edgeResistance:1;return e.call(eQ,(r>n?n+(r-n)*i:r<t?t+(r-t)*i:r)*o)*o}:eo(e)?function(o){for(var r,i,l=e.length,a=0,s=1e20;--l>-1;)(i=(r=e[l])-o)<0&&(i=-i),i<s&&r>=t&&r<=n&&(a=l,s=i);return e[a]}:isNaN(e)?function(e){return e}:function(){return e*o}},ts=function(){var e,t,o,i,l,s,c,p,u,f,h;d=!1,a?(a.calibrate(),eQ.minX=x=-a.maxScrollLeft(),eQ.minY=y=-a.maxScrollTop(),eQ.maxX=g=eQ.maxY=m=0,d=!0):r.bounds&&(e=eH(r.bounds,n.parentNode),eW?(eQ.minX=x=e.left,eQ.maxX=g=e.left+e.width,eQ.minY=y=eQ.maxY=m=0):J(r.bounds.maxX)&&J(r.bounds.maxY)?(t=eH(n,n.parentNode),eQ.minX=x=Math.round(tn(e$,"px")+e.left-t.left),eQ.minY=y=Math.round(tn(eG,"px")+e.top-t.top),eQ.maxX=g=Math.round(x+(e.width-t.width)),eQ.maxY=m=Math.round(y+(e.height-t.height))):(e=r.bounds,eQ.minX=x=e.minX,eQ.minY=y=e.minY,eQ.maxX=g=e.maxX,eQ.maxY=m=e.maxY),x>g&&(eQ.minX=g,eQ.maxX=g=x,x=eQ.minX),y>m&&(eQ.minY=m,eQ.maxY=m=y,y=eQ.minY),eW&&(eQ.minRotation=x,eQ.maxRotation=g),d=!0),r.liveSnap&&((i=eo(o=!0===r.liveSnap?r.snap||{}:r.liveSnap)||q(o),eW)?(D=ta(i?o:o.rotation,x,g,1),P=null):o.points?(l=i?o:o.points,s=x,c=g,p=y,u=m,f=o.radius,h=a?-1:1,f=f&&f<1e20?f*f:1e20,O=q(l)?function(e){var t,n,o,r=eQ.isPressed?1-eQ.edgeResistance:1,i=e.x,a=e.y;return e.x=i=i>c?c+(i-c)*r:i<s?s+(i-s)*r:i,e.y=a=a>u?u+(a-u)*r:a<p?p+(a-p)*r:a,(t=l.call(eQ,e))!==e&&(e.x=t.x,e.y=t.y),1!==h&&(e.x*=h,e.y*=h),f<1e20&&(n=e.x-i)*n+(o=e.y-a)*o>f&&(e.x=i,e.y=a),e}:eo(l)?function(e){for(var t,n,o,r,i=l.length,a=0,s=1e20;--i>-1;)(r=(t=(o=l[i]).x-e.x)*t+(n=o.y-e.y)*n)<s&&(a=i,s=r);return s<=f?l[a]:e}:function(e){return e}):(eq&&(D=ta(i?o:o.x||o.left||o.scrollLeft,x,g,a?-1:1)),eZ&&(P=ta(i?o:o.y||o.top||o.scrollTop,y,m,a?-1:1))))},tc=function(){eQ.isThrowing=!1,eI(eQ,"throwcomplete","onThrowComplete")},tp=function(){eQ.isThrowing=!1},tu=function(e,t){var o,i,l,s;e&&H?(!0===e&&(i=eo(o=r.snap||r.liveSnap||{})||q(o),e={resistance:(r.throwResistance||r.resistance||1e3)/(eW?10:1)},eW?e.rotation=ez(eQ,i?o:o.rotation,g,x,1,t):(eq&&(e[e$]=ez(eQ,i?o:o.points||o.x||o.left,g,x,a?-1:1,t||"x"===eQ.lockedAxis)),eZ&&(e[eG]=ez(eQ,i?o:o.points||o.y||o.top,m,y,a?-1:1,t||"y"===eQ.lockedAxis)),(o.points||eo(o)&&Z(o[0]))&&(e.linkedProps=e$+","+eG,e.radius=o.radius))),eQ.isThrowing=!0,s=isNaN(r.overshootTolerance)?1===r.edgeResistance?0:1-eQ.edgeResistance+.2:r.overshootTolerance,e.duration||(e.duration={max:Math.max(r.minDuration||0,"maxDuration"in r?r.maxDuration:2),min:isNaN(r.minDuration)?0===s||Z(e)&&e.resistance>1e3?0:.5:r.minDuration,overshoot:s}),eQ.tween=l=S.to(a||n,{inertia:e,data:"_draggable",onComplete:tc,onInterrupt:tp,onUpdate:r.fastMode?eI:tl,onUpdateParams:r.fastMode?[eQ,"onthrowupdate","onThrowUpdate"]:o&&o.radius?[!1,!0]:[]}),!r.fastMode&&(a&&(a._skip=!0),l.render(1e9,!0,!0),tl(!0,!0),eQ.endX=eQ.x,eQ.endY=eQ.y,eW&&(eQ.endRotation=eQ.x),l.play(0),tl(!0,!0),a&&(a._skip=!1))):d&&eQ.applyBounds()},td=function(e){var t,o=$;$=M(n.parentNode,!0),e&&eQ.isPressed&&!$.equals(o||new E)&&(t=o.inverse().apply({x:s,y:c}),$.apply(t,t),s=t.x,c=t.y),$.equals(el)&&($=null)},tf=function(){var e,t,o,r=1-eQ.edgeResistance,i=tt?eY(to):0,l=tt?ek(to):0;eA&&(te.x=tn(e$,"px")+"px",te.y=tn(eG,"px")+"px",te.renderTransform()),td(!1),eF.x=eQ.pointerX-i,eF.y=eQ.pointerY-l,$&&$.apply(eF,eF),s=eF.x,c=eF.y,T&&(ty(eQ.pointerX,eQ.pointerY),ti(!0)),ew=M(n),a?(ts(),u=a.top(),p=a.left()):(th()?(tl(!0,!0),ts()):eQ.applyBounds(),eW?(e=n.ownerSVGElement?[te.xOrigin-n.getBBox().x,te.yOrigin-n.getBBox().y]:(eR(n)[et]||"0 0").split(" "),b=eQ.rotationOrigin=M(n).apply({x:parseFloat(e[0])||0,y:parseFloat(e[1])||0}),tl(!0,!0),t=eQ.pointerX-b.x-i,o=b.y-eQ.pointerY+l,p=eQ.x,u=eQ.y=Math.atan2(o,t)*ei):(u=tn(eG,"px"),p=tn(e$,"px"))),d&&r&&(p>g?p=g+(p-g)/r:p<x&&(p=x-(x-p)/r),!eW&&(u>m?u=m+(u-m)/r:u<y&&(u=y-(y-u)/r))),eQ.startX=p=en(p),eQ.startY=u=en(u)},th=function(){return eQ.tween&&eQ.tween.isActive()},tg=function(){!N.parentNode||th()||eQ.isDragging||N.parentNode.removeChild(N)},tx=function(e,t){var i;if(!l||eQ.isPressed||!e||("mousedown"===e.type||"pointerdown"===e.type)&&!t&&ea()-e7<30&&A[eQ.pointerEvent.type]){ey&&e&&l&&eM(e);return}if(G=th(),eX=!1,eQ.pointerEvent=e,A[e.type]?(eT(K=~e.type.indexOf("touch")?e.currentTarget||e.target:to,"touchend",tv),eT(K,"touchmove",tm),eT(K,"touchcancel",tv),eT(to,"touchstart",eC)):(K=null,eT(to,"mousemove",tm)),ee=null,(!W||!K)&&(eT(to,"mouseup",tv),e&&e.target&&eT(e.target,"mouseup",tv)),j=e6.call(eQ,e.target)&&!1===r.dragClickables&&!t){eT(e.target,"change",tv),eI(eQ,"pressInit","onPressInit"),eI(eQ,"press","onPress"),ej(e0,!0),ey=!1;return}if((ey=!(Q=!!K&&eq!==eZ&&!1!==eQ.vars.allowNativeTouchScrolling&&(!eQ.vars.allowContextMenu||!e||!e.ctrlKey&&!(e.which>2))&&(eq?"y":"x"))&&!eQ.allowEventDefault)&&(eM(e),eT(X,"touchforcechange",eM)),e.changedTouches?w=(e=v=e.changedTouches[0]).identifier:e.pointerId?w=e.pointerId:v=w=null,U++,ev(ti),c=eQ.pointerY=e.pageY,s=eQ.pointerX=e.pageX,eI(eQ,"pressInit","onPressInit"),(Q||eQ.autoScroll)&&e_(n.parentNode),!n.parentNode||!eQ.autoScroll||a||eW||!n.parentNode._gsMaxScrollX||N.parentNode||n.getBBox||(N.style.width=n.parentNode.scrollWidth+"px",n.parentNode.appendChild(N)),tf(),eQ.tween&&eQ.tween.kill(),eQ.isThrowing=!1,S.killTweensOf(a||n,e1,!0),a&&S.killTweensOf(n,{scrollTo:1},!0),eQ.tween=eQ.lockedAxis=null,!r.zIndexBoost&&(eW||a||!1===r.zIndexBoost)||(n.style.zIndex=o.zIndex++),eQ.isPressed=!0,f=!!(r.onDrag||eQ._listeners.drag),h=!!(r.onMove||eQ._listeners.move),!1!==r.cursor||r.activeCursor)for(i=e0.length;--i>-1;)S.set(e0[i],{cursor:r.activeCursor||r.cursor||("grab"===F?"grabbing":F)});eI(eQ,"press","onPress")},tm=function(e){var t,o,r,i,a,p,u=e;if(!l||B||!eQ.isPressed||!e){ey&&e&&l&&eM(e);return}if(eQ.pointerEvent=e,t=e.changedTouches){if((e=t[0])!==v&&e.identifier!==w){for(i=t.length;--i>-1&&(e=t[i]).identifier!==w&&e.target!==n;);if(i<0)return}}else if(e.pointerId&&w&&e.pointerId!==w)return;if(K&&Q&&!ee&&(eF.x=e.pageX-(tt?eY(to):0),eF.y=e.pageY-(tt?ek(to):0),$&&$.apply(eF,eF),o=eF.x,r=eF.y,((a=Math.abs(o-s))!==(p=Math.abs(r-c))&&(a>eJ||p>eJ)||I&&Q===ee)&&(ee=a>p&&eq?"x":"y",Q&&ee!==Q&&eT(X,"touchforcechange",eM),!1!==eQ.vars.lockAxisOnTouchScroll&&eq&&eZ&&(eQ.lockedAxis="x"===ee?"y":"x",q(eQ.vars.onLockAxis)&&eQ.vars.onLockAxis.call(eQ,u)),I&&Q===ee))){tv(u);return}eQ.allowEventDefault||Q&&(!ee||Q===ee)||!1===u.cancelable?ey&&(ey=!1):(eM(u),ey=!0),eQ.autoScroll&&(e3=!0),ty(e.pageX,e.pageY,h)},ty=function(e,t,n){var o,r,i,l,a,f,h=1-eQ.dragResistance,v=1-eQ.edgeResistance,w=eQ.pointerX,E=eQ.pointerY,M=u,S=eQ.x,X=eQ.y,C=eQ.endX,k=eQ.endY,Y=eQ.endRotation,N=T;eQ.pointerX=e,eQ.pointerY=t,tt&&(e-=eY(to),t-=ek(to)),eW?(l=Math.atan2(b.y-t,e-b.x)*ei,(a=eQ.y-l)>180?(u-=360,eQ.y=l):a<-180&&(u+=360,eQ.y=l),eQ.x!==p||Math.abs(u-l)>eJ?(eQ.y=l,i=p+(u-l)*h):i=p):($&&(f=e*$.a+t*$.c+$.e,t=e*$.b+t*$.d+$.f,e=f),(r=t-c)<eJ&&r>-eJ&&(r=0),(o=e-s)<eJ&&o>-eJ&&(o=0),(eQ.lockAxis||eQ.lockedAxis)&&(o||r)&&(!(f=eQ.lockedAxis)&&(eQ.lockedAxis=f=eq&&Math.abs(o)>Math.abs(r)?"y":eZ?"x":null,f&&q(eQ.vars.onLockAxis)&&eQ.vars.onLockAxis.call(eQ,eQ.pointerEvent)),"y"===f?r=0:"x"===f&&(o=0)),i=en(p+o*h),l=en(u+r*h)),(D||P||O)&&(eQ.x!==i||eQ.y!==l&&!eW)&&(O&&(ef.x=i,ef.y=l,f=O(ef),i=en(f.x),l=en(f.y)),D&&(i=en(D(i))),P&&(l=en(P(l)))),d&&(i>g?i=g+Math.round((i-g)*v):i<x&&(i=x+Math.round((i-x)*v)),!eW&&(l>m?l=Math.round(m+(l-m)*v):l<y&&(l=Math.round(y+(l-y)*v)))),eQ.x===i&&(eQ.y===l||eW)||(eW?(eQ.endRotation=eQ.x=eQ.endX=i,T=!0):(eZ&&(eQ.y=eQ.endY=l,T=!0),eq&&(eQ.x=eQ.endX=i,T=!0)),n&&!1===eI(eQ,"move","onMove")?(eQ.pointerX=w,eQ.pointerY=E,u=M,eQ.x=S,eQ.y=X,eQ.endX=C,eQ.endY=k,eQ.endRotation=Y,T=N):!eQ.isDragging&&eQ.isPressed&&(eQ.isDragging=eX=!0,eI(eQ,"dragstart","onDragStart")))},tv=function e(t,o){if(!l||!eQ.isPressed||t&&null!=w&&!o&&(t.pointerId&&t.pointerId!==w&&t.target!==n||t.changedTouches&&!eS(t.changedTouches,w))){ey&&t&&l&&eM(t);return}eQ.isPressed=!1;var i,a,s,c,p=t,u=eQ.isDragging,d=eQ.vars.allowContextMenu&&t&&(t.ctrlKey||t.which>2),f=S.delayedCall(.001,tg);if(K?(eE(K,"touchend",e),eE(K,"touchmove",tm),eE(K,"touchcancel",e),eE(to,"touchstart",eC)):eE(to,"mousemove",tm),eE(X,"touchforcechange",eM),(!W||!K)&&(eE(to,"mouseup",e),t&&t.target&&eE(t.target,"mouseup",e)),T=!1,u&&(e2=ed=ea(),eQ.isDragging=!1),eb(ti),j&&!d){t&&(eE(t.target,"change",e),eQ.pointerEvent=p),ej(e0,!1),eI(eQ,"release","onRelease"),eI(eQ,"click","onClick"),j=!1;return}for(a=e0.length;--a>-1;)eO(e0[a],"cursor",r.cursor||(!1!==r.cursor?F:null));if(U--,t){if((i=t.changedTouches)&&(t=i[0])!==v&&t.identifier!==w){for(a=i.length;--a>-1&&(t=i[a]).identifier!==w&&t.target!==n;);if(a<0&&!o)return}eQ.pointerEvent=p,eQ.pointerX=t.pageX,eQ.pointerY=t.pageY}return d&&p?(eM(p),ey=!0,eI(eQ,"release","onRelease")):p&&!u?(ey=!1,G&&(r.snap||r.bounds)&&tu(r.inertia||r.throwProps),eI(eQ,"release","onRelease"),(!I||"touchmove"!==p.type)&&-1===p.type.indexOf("cancel")&&(eI(eQ,"click","onClick"),ea()-e7<300&&eI(eQ,"doubleclick","onDoubleClick"),c=p.target||n,e7=ea(),I||p.defaultPrevented||S.delayedCall(.05,function(){e7!==es&&eQ.enabled()&&!eQ.isPressed&&!p.defaultPrevented&&(c.click?c.click():to.createEvent&&((s=to.createEvent("MouseEvents")).initMouseEvent("click",!0,!0,X,1,eQ.pointerEvent.screenX,eQ.pointerEvent.screenY,eQ.pointerX,eQ.pointerY,!1,!1,!1,!1,0,null),c.dispatchEvent(s)))}))):(tu(r.inertia||r.throwProps),!eQ.allowEventDefault&&p&&(!1!==r.dragClickables||!e6.call(eQ,p.target))&&u&&(!Q||ee&&Q===ee)&&!1!==p.cancelable?(ey=!0,eM(p)):ey=!1,eI(eQ,"release","onRelease")),th()&&f.duration(eQ.tween.duration()),u&&eI(eQ,"dragend","onDragEnd"),!0},tw=function(e){if(e&&eQ.isDragging&&!a){var t=e.target||n.parentNode,o=t.scrollLeft-t._gsScrollX,r=t.scrollTop-t._gsScrollY;(o||r)&&($?(s-=o*$.a+r*$.c,c-=r*$.d+o*$.b):(s-=o,c-=r),t._gsScrollX+=o,t._gsScrollY+=r,ty(eQ.pointerX,eQ.pointerY))}},tb=function(e){var t=ea(),n=t-e7<100,o=t-e2<50,r=n&&es===e7,i=eQ.pointerEvent&&eQ.pointerEvent.defaultPrevented,l=n&&eu===e7,a=e.isTrusted||null==e.isTrusted&&n&&r;if((r||o&&!1!==eQ.vars.suppressClickOnDrag)&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),n&&!(eQ.pointerEvent&&eQ.pointerEvent.defaultPrevented)&&(!r||a&&!l)){a&&r&&(eu=e7),es=e7;return}(eQ.isPressed||o||n)&&(!a||!e.detail||!n||i)&&eM(e),n||o||eX||(e&&e.target&&(eQ.pointerEvent=e),eI(eQ,"click","onClick"))},tT=function(e){return $?{x:e.x*$.a+e.y*$.c+$.e,y:e.x*$.b+e.y*$.d+$.f}:{x:e.x,y:e.y}};return(Y=o.get(n))&&Y.kill(),i.startDrag=function(e,t){var o,r,i,l;tx(e||eQ.pointerEvent,!0),t&&!eQ.hitTest(e||eQ.pointerEvent)&&(o=eB(e||eQ.pointerEvent),r=eB(n),i=tT({x:o.left+o.width/2,y:o.top+o.height/2}),l=tT({x:r.left+r.width/2,y:r.top+r.height/2}),s-=i.x-l.x,c-=i.y-l.y),eQ.isDragging||(eQ.isDragging=eX=!0,eI(eQ,"dragstart","onDragStart"))},i.drag=tm,i.endDrag=function(e){return tv(e||eQ.pointerEvent,!0)},i.timeSinceDrag=function(){return eQ.isDragging?0:(ea()-e2)/1e3},i.timeSinceClick=function(){return(ea()-e7)/1e3},i.hitTest=function(e,t){return o.hitTest(eQ.target,e,t)},i.getDirection=function(e,t){var o,r,i,l,a,s,c="velocity"===e&&H?e:Z(e)&&!eW?"element":"start";return("element"===c&&(a=eB(eQ.target),s=eB(e)),o="start"===c?eQ.x-p:"velocity"===c?H.getVelocity(n,e$):a.left+a.width/2-(s.left+s.width/2),eW)?o<0?"counter-clockwise":"clockwise":(t=t||2,r="start"===c?eQ.y-u:"velocity"===c?H.getVelocity(n,eG):a.top+a.height/2-(s.top+s.height/2),l=(i=Math.abs(o/r))<1/t?"":o<0?"left":"right",i<t&&(""!==l&&(l+="-"),l+=r<0?"up":"down"),l)},i.applyBounds=function(e,t){var o,i,l,a,s,c;if(e&&r.bounds!==e)return r.bounds=e,eQ.update(!0,t);if(tl(!0),ts(),d&&!th()){if(o=eQ.x,i=eQ.y,o>g?o=g:o<x&&(o=x),i>m?i=m:i<y&&(i=y),(eQ.x!==o||eQ.y!==i)&&(l=!0,eQ.x=eQ.endX=o,eW?eQ.endRotation=o:eQ.y=eQ.endY=i,T=!0,ti(!0),eQ.autoScroll&&!eQ.isDragging))for(e_(n.parentNode),a=n,eh.scrollTop=null!=X.pageYOffset?X.pageYOffset:null!=to.documentElement.scrollTop?to.documentElement.scrollTop:to.body.scrollTop,eh.scrollLeft=null!=X.pageXOffset?X.pageXOffset:null!=to.documentElement.scrollLeft?to.documentElement.scrollLeft:to.body.scrollLeft;a&&!c;)s=(c=eL(a.parentNode))?eh:a.parentNode,eZ&&s.scrollTop>s._gsMaxScrollY&&(s.scrollTop=s._gsMaxScrollY),eq&&s.scrollLeft>s._gsMaxScrollX&&(s.scrollLeft=s._gsMaxScrollX),a=s;eQ.isThrowing&&(l||eQ.endX>g||eQ.endX<x||eQ.endY>m||eQ.endY<y)&&tu(r.inertia||r.throwProps,l)}return eQ},i.update=function(e,t,o){if(t&&eQ.isPressed){var r=M(n),i=ew.apply({x:eQ.x-p,y:eQ.y-u}),l=M(n.parentNode,!0);l.apply({x:r.e-i.x,y:r.f-i.y},i),eQ.x-=i.x-l.e,eQ.y-=i.y-l.f,ti(!0),tf()}var a=eQ.x,s=eQ.y;return td(!t),e?eQ.applyBounds():(T&&o&&ti(!0),tl(!0)),t&&(ty(eQ.pointerX,eQ.pointerY),T&&ti(!0)),eQ.isPressed&&!t&&(eq&&Math.abs(a-eQ.x)>.01||eZ&&Math.abs(s-eQ.y)>.01&&!eW)&&tf(),eQ.autoScroll&&(e_(n.parentNode,eQ.isDragging),e3=eQ.isDragging,ti(!0),eN(n,tw),eD(n,tw)),eQ},i.enable=function(e){var t,o,i,s={lazy:!0};if(!1!==r.cursor&&(s.cursor=r.cursor||F),S.utils.checkPrefix("touchCallout")&&(s.touchCallout="none"),"soft"!==e){for(em(e0,eq===eZ?"none":r.allowNativeTouchScrolling&&n.scrollHeight===n.clientHeight==(n.scrollWidth===n.clientHeight)||r.allowEventDefault?"manipulation":eq?"pan-y":"pan-x"),o=e0.length;--o>-1;)i=e0[o],W||eT(i,"mousedown",tx),eT(i,"touchstart",tx),eT(i,"click",tb,!0),S.set(i,s),i.getBBox&&i.ownerSVGElement&&eq!==eZ&&S.set(i.ownerSVGElement,{touchAction:r.allowNativeTouchScrolling||r.allowEventDefault?"manipulation":eq?"pan-y":"pan-x"}),r.allowContextMenu||eT(i,"contextmenu",tr);ej(e0,!1)}return eD(n,tw),l=!0,H&&"soft"!==e&&H.track(a||n,eA?"x,y":eW?"rotation":"top,left"),n._gsDragID=t="d"+ep++,ec[t]=eQ,a&&(a.enable(),a.element._gsDragID=t),(r.bounds||eW)&&tf(),r.bounds&&eQ.applyBounds(),eQ},i.disable=function(e){for(var t,o=eQ.isDragging,r=e0.length;--r>-1;)eO(e0[r],"cursor",null);if("soft"!==e){for(em(e0,null),r=e0.length;--r>-1;)eO(t=e0[r],"touchCallout",null),eE(t,"mousedown",tx),eE(t,"touchstart",tx),eE(t,"click",tb,!0),eE(t,"contextmenu",tr);ej(e0,!0),K&&(eE(K,"touchcancel",tv),eE(K,"touchend",tv),eE(K,"touchmove",tm)),eE(to,"mouseup",tv),eE(to,"mousemove",tm)}return eN(n,tw),l=!1,H&&"soft"!==e&&H.untrack(a||n,eA?"x,y":eW?"rotation":"top,left"),a&&a.disable(),eb(ti),eQ.isDragging=eQ.isPressed=j=!1,o&&eI(eQ,"dragend","onDragEnd"),eQ},i.enabled=function(e,t){return arguments.length?e?eQ.enable(t):eQ.disable(t):l},i.kill=function(){return eQ.isThrowing=!1,eQ.tween&&eQ.tween.kill(),eQ.disable(),S.set(e0,{clearProps:"userSelect"}),delete ec[n._gsDragID],eQ},i.revert=function(){this.kill(),this.styles&&this.styles.revert()},~eP.indexOf("scroll")&&(a=i.scrollProxy=new eK(n,ex({onKill:function(){eQ.isPressed&&tv(null)}},r)),n.style.overflowY=eZ&&!R?"auto":"hidden",n.style.overflowX=eq&&!R?"auto":"hidden",n=a.content),eW?e1.rotation=1:(eq&&(e1[e$]=1),eZ&&(e1[eG]=1)),te.force3D=!("force3D"in r)||r.force3D,z(t(i)),i.enable(),i}return(n=o).prototype=Object.create(e.prototype),n.prototype.constructor=n,n.__proto__=e,o.register=function(e){S=e,eU()},o.create=function(e,t){return L||eU(!0),_(e).map(function(e){return new o(e,t)})},o.get=function(e){return ec[(_(e)[0]||{})._gsDragID]},o.timeSinceDrag=function(){return(ea()-ed)/1e3},o.hitTest=function(e,t,n){if(e===t)return!1;var o,r,i,l=eB(e),a=eB(t),s=l.top,c=l.left,p=l.right,u=l.bottom,d=l.width,f=l.height,h=a.left>p||a.right<c||a.top>u||a.bottom<s;return h||!n?!h:(i=-1!==(n+"").indexOf("%"),n=parseFloat(n)||0,(o={left:Math.max(c,a.left),top:Math.max(s,a.top)}).width=Math.min(p,a.right)-o.left,o.height=Math.min(u,a.bottom)-o.top,!(o.width<0)&&!(o.height<0)&&(i?(n*=.01,(r=o.width*o.height)>=d*f*n||r>=a.width*a.height*n):o.width>n&&o.height>n))},o}(function(){function e(e){this._listeners={},this.target=e||this}var t=e.prototype;return t.addEventListener=function(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);~n.indexOf(t)||n.push(t)},t.removeEventListener=function(e,t){var n=this._listeners[e],o=n&&n.indexOf(t);o>=0&&n.splice(o,1)},t.dispatchEvent=function(e){var t,n=this;return(this._listeners[e]||[]).forEach(function(o){return!1===o.call(n,{type:e,target:n.target})&&(t=!1)}),t},e}());(function(e,t){for(var n in t)n in e||(e[n]=t[n])})(e$.prototype,{pointerX:0,pointerY:0,startX:0,startY:0,deltaX:0,deltaY:0,isDragging:!1,isPressed:!1}),e$.zIndex=1e3,e$.version="3.11.5",G()&&S.registerPlugin(e$),e.Draggable=e$,e.default=e$,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete window.default}(t)}}]);