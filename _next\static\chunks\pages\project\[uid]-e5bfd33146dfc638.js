(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[822],{2328:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/project/[uid]",function(){return r(7550)}])},7032:function(e,t,r){"use strict";var i=r(5893),n=r(2578),s=r(1321);let l=e=>{let{className:t,...r}=e;return(0,i.jsx)(n.Z,{imgixParams:{q:75,auto:["format"]},fallbackAlt:"",className:(0,s.Z)("w-full",t),...r})};t.Z=l},863:function(e,t,r){"use strict";var i=r(5893),n=r(7294),s=r(1321);let l=e=>{let{as:t="div",margins:r=!0,gap:l=!0,className:a,children:o}=e,c=(0,n.useMemo)(()=>(0,s.Z)("grid grid-cols-12 mx-auto w-full",r&&"px-20 md:px-40",l&&"gap-x-24",a),[r,l,a]);return(0,i.jsx)(t,{className:c,children:o})};t.Z=l},7628:function(e,t,r){"use strict";var i=r(5893),n=r(7294),s=r(6038),l=r(4235);let a=e=>{let{show:t,duration:r=.5,delay:a=.2}=e,o=(0,n.useRef)(null),c=(0,n.useRef)(t);return(0,n.useEffect)(()=>{if(c.current===t){s.p8.set(o.current,{xPercent:t?0:100,yPercent:t?0:-100});return}c.current=t;let e=s.p8.timeline();return t?e.set(o.current,{xPercent:-100,yPercent:100}).to(o.current,{xPercent:0,yPercent:0,ease:"expo.out",duration:r,delay:a}):e.set(o.current,{xPercent:0,yPercent:0}).to(o.current,{xPercent:100,yPercent:-100,ease:"expo.out",duration:r}),()=>{e.kill()}},[t,r,a]),(0,i.jsx)("div",{"aria-hidden":"true",className:"w-[0.85ch] h-[0.85ch] overflow-hidden",children:(0,i.jsx)("div",{ref:o,className:"w-full",children:(0,i.jsx)(l.Z,{})})})};t.Z=a},9565:function(e,t,r){"use strict";var i=r(5893),n=r(7294),s=r(2977),l=r(1042),a=r(6038),o=r(6546),c=r(7740),d=r(9542),u=r(9594),p=r(7492),h=r(1321);a.p8.registerPlugin(o.ScrollTrigger);let m=e=>{let{cover:t,optionalTargetContainerRef:r,className:m,children:g}=e,{isAppReady:f}=(0,c.R)(u.HS),x=(0,s.Z)(),v=(0,p.k)(),[y,j]=(0,n.useState)(!1),[w,b]=(0,n.useState)(!1),N=(0,n.useRef)({x:0,y:0}),k=(0,n.useRef)(null),_=(0,n.useRef)(null),Z=(0,n.useRef)(null),R=(0,n.useRef)({x:0,y:0}),P=(e,t,r)=>e*(t/r-.5)*2;(0,l.Z)(()=>{j(window.matchMedia("(hover: none)").matches)},[]),(0,n.useEffect)(()=>{let e=(null==r?void 0:r.current)||k.current;N.current={x:e.clientWidth*((1.03-1)/2),y:e.clientHeight*((1.03-1)/2)}},[r,x]);let T=(0,n.useCallback)(()=>{let e=(0,d.t7)(R.current.x,v.current.x,.1),t=(0,d.t7)(R.current.y,v.current.y,.1),r=P(N.current.x,e,x.width),i=P(N.current.y,t,x.height);a.p8.set(_.current,{x:r,y:i}),R.current={x:e,y:t}},[v,x]);return(0,l.Z)(()=>{if(f&&!y)return a.p8.set(_.current,{scale:1.03}),Z.current=o.ScrollTrigger.create({trigger:k.current,onToggle:e=>{let{isActive:t}=e,r=P(N.current.x,v.current.x,x.width),i=P(N.current.y,v.current.y,x.height);R.current={x:v.current.x,y:v.current.y},a.p8.set(_.current,{x:r,y:i}),b(t)}}),()=>{var e;null===(e=Z.current)||void 0===e||e.kill()}},[T,f,y]),(0,n.useEffect)(()=>{if(w)return a.p8.ticker.add(T),a.p8.ticker.fps(60),()=>{a.p8.ticker.remove(T)}},[T,w]),(0,i.jsx)("div",{ref:k,className:(0,h.Z)("relative overflow-hidden",t&&"w-full h-full",m),children:(0,i.jsx)("div",{ref:_,className:t&&"w-full h-full",children:g})})};t.Z=m},4235:function(e,t,r){"use strict";var i=r(5893);let n=()=>(0,i.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,i.jsx)("path",{d:"M3.2 23.3.7 20.8 17.5 4h-15V.5h21v21H20v-15L3.2 23.3Z"}),(0,i.jsx)("path",{d:"M3.2 24 0 20.8 16.3 4.5H2V0h22v22h-4.5V7.7L3.2 24ZM20 6.5v15h3.5V.5h-21V4h15L.7 20.8l2.5 2.5L20 6.5Z"})]});t.Z=n},7550:function(e,t,r){"use strict";r.r(t),r.d(t,{__N_SSG:function(){return eI},default:function(){return eL}});var i=r(5893),n=r(7294),s=r(172),l=r(9008),a=r.n(l),o=r(720),c=r(9594),d=r(4419),u=r(1042),p=r(6038),h=r(6546),m=r(9815),g=r(7032),f=r(2637),x=r(9565),v=r(7628),y=r(1321);p.p8.registerPlugin(h.ScrollTrigger);let j=e=>{let{title:t,image:r,type:s,skills:l}=e,[a,o]=(0,n.useState)(!1),p=(0,n.useRef)(null),j=(0,n.useRef)(null);(0,u.Z)(()=>(p.current=h.ScrollTrigger.create({trigger:j.current,start:"bottom bottom",once:!0,onEnter:()=>{o(!0),c.Xr.type="default"}}),()=>{var e;null===(e=p.current)||void 0===e||e.kill()}),[]);let w=(0,y.Z)("flex flex-col","min-h-svh pt-170 px-20 pb-20","md:h-auto md:mt-[var(--site-header-height)] md:px-40 md:pt-[calc(var(--site-header-height)*0.75)] md:pb-96");return(0,i.jsx)(f.Z,{type:a?"default":"scroll",children:(0,i.jsxs)("header",{ref:j,className:w,children:[(0,i.jsx)("div",{className:"md:hidden relative flex-1",children:(0,i.jsx)("div",{className:"absolute top-0 right-0 bottom-0 left-0",children:(0,i.jsx)(g.Z,{field:r,sizes:"100vw",priority:!0,className:"w-full h-full object-cover"})})}),(0,i.jsxs)("div",{className:"flex items-end justify-between flex-wrap mb-24 pt-12 md:pt-0 md:h-56",children:[t&&(0,i.jsx)("h1",{className:"type-subtitle flex-none pt-8 md:pt-0",children:(0,i.jsx)(m.Z,{appear:!0,children:(0,i.jsx)(d.K,{field:t})})}),s&&(0,i.jsx)("span",{className:"flex-none type-large text-20 md:text-24 mb-4 md:mb-0 pt-8 md:pt-0",children:(0,i.jsx)(m.Z,{appear:!0,delay:.25,children:s})})]}),(0,i.jsx)(x.Z,{className:"hidden md:block",children:(0,i.jsx)(g.Z,{field:r,sizes:"calc((100vw - 4rem) * 1.12)",priority:!0})}),l&&(0,i.jsxs)("div",{className:"relative flex flex-col md:flex-row justify-between mt-40 md:mt-64 type-body",children:[l.map((e,t)=>(0,i.jsx)("div",{className:"mb-12 md:mb-0",children:(0,i.jsx)(m.Z,{appear:!0,delay:.1*t+.5,stagger:.1,chars:!1,children:e.item})},t)),(0,i.jsx)("div",{className:"absolute right-0 bottom-20 md:hidden",children:(0,i.jsx)("div",{className:"rotate-180 type-subtitle",children:(0,i.jsx)(v.Z,{show:!0,duration:.75,delay:1})})})]})]})})};p.p8.registerPlugin(h.ScrollTrigger);let w=e=>{let{slice:t}=e,r=(0,n.useRef)(null),s=(0,n.useRef)(null),l=(0,n.useRef)(null);return(0,u.Z)(()=>(r.current=h.ScrollTrigger.create({trigger:s.current,toggleActions:"play none none reset",animation:p.p8.fromTo(l.current,{opacity:0,scale:.7},{opacity:1,scale:1,ease:"expo.out",duration:1})}),()=>{var e;null===(e=r.current)||void 0===e||e.kill()}),[]),(0,i.jsx)("section",{className:"py-32 md:py-96 px-20 md:px-40",children:(0,i.jsx)("div",{ref:s,children:(0,i.jsx)("div",{ref:l,children:(0,i.jsx)(x.Z,{children:(0,i.jsx)(g.Z,{field:t.primary.image,sizes:"calc((100vw - 4rem) * 1.12)"})})})})})};var b=r(863);p.p8.registerPlugin(h.ScrollTrigger);let N=e=>{let{slice:t}=e,r="(max-width: 992px) 90vw, calc(50vw - 4rem * 1.12)",s=(0,n.useRef)(null),l=(0,n.useRef)(null),a=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,u.Z)(()=>(s.current=h.ScrollTrigger.create({trigger:l.current,toggleActions:"play none none reset",animation:p.p8.fromTo([a.current,o.current],{opacity:0,scale:.7},{opacity:1,scale:1,ease:"expo.out",duration:1})}),()=>{var e;null===(e=s.current)||void 0===e||e.kill()}),[]),(0,i.jsxs)(b.Z,{className:"py-32 md:py-96 px-20 md:px-40 gap-x-12",children:[(0,i.jsx)("div",{ref:l,className:"col-span-full sm:col-span-6 my-32 md:my-12",children:(0,i.jsx)("div",{ref:a,children:(0,i.jsx)(x.Z,{children:(0,i.jsx)(g.Z,{field:t.primary.first_image,sizes:r})})})}),(0,i.jsx)("div",{className:"col-span-full sm:col-span-6 my-32 md:my-12",children:(0,i.jsx)("div",{ref:o,children:(0,i.jsx)(x.Z,{children:(0,i.jsx)(g.Z,{field:t.primary.second_image,sizes:r})})})})]})};var k,_,Z,R,P,T,S,F,E=r(2977),M=r(7740),C=r(3145),A=function(){return k||(k=window.gsap)},z={},O=function(e){return F(e).id},I=function(e){return z[O("string"==typeof e?Z(e)[0]:e)]},L=function(e){var t,r=P;if(e-S>=.05)for(S=e;r;)((t=r.g(r.t,r.p))!==r.v1||e-r.t1>.2)&&(r.v2=r.v1,r.v1=t,r.t2=r.t1,r.t1=e),r=r._next},D={deg:360,rad:2*Math.PI},H=function(){(k=A())&&(Z=k.utils.toArray,R=k.utils.getUnit,F=k.core.getCache,T=k.ticker,_=1)},X=function(e,t,r,i){this.t=e,this.p=t,this.g=e._gsap.get,this.rCap=D[r||R(this.g(e,t))],this.v1=this.v2=0,this.t1=this.t2=T.time,i&&(this._next=i,i._prev=this)},V=function(){function e(e,t){_||H(),this.target=Z(e)[0],z[O(this.target)]=this,this._props={},t&&this.add(t)}e.register=function(e){k=e,H()};var t=e.prototype;return t.get=function(e,t){var r,i,n=this._props[e]||console.warn("Not tracking "+e+" velocity.");return r=parseFloat(t?n.v1:n.g(n.t,n.p))-parseFloat(n.v2),(i=n.rCap)&&(r%=i)!=r%(i/2)&&(r=r<0?r+i:r-i),Math.round(1e4*(r/((t?n.t1:T.time)-n.t2)))/1e4},t.getAll=function(){var e,t={},r=this._props;for(e in r)t[e]=this.get(e);return t},t.isTracking=function(e){return e in this._props},t.add=function(e,t){e in this._props||(P||(T.add(L),S=T.time),P=this._props[e]=new X(this.target,e,t,P))},t.remove=function(e){var t,r,i=this._props[e];i&&(t=i._prev,r=i._next,t&&(t._next=r),r?r._prev=t:P===i&&(T.remove(L),P=0),delete this._props[e])},t.kill=function(e){for(var t in this._props)this.remove(t);e||delete z[O(this.target)]},e.track=function(t,r,i){_||H();for(var n,s,l=[],a=Z(t),o=r.split(","),c=(i||"").split(","),d=a.length;d--;){for(n=I(a[d])||new e(a[d]),s=o.length;s--;)n.add(o[s],c[s]||c[0]);l.push(n)}return l},e.untrack=function(e,t){var r=(t||"").split(",");Z(e).forEach(function(e){var t=I(e);t&&(r.length?r.forEach(function(e){return t.remove(e)}):t.kill(1))})},e.isTracking=function(e,t){var r=I(e);return r&&r.isTracking(t)},e.getVelocity=function(e,t){var r=I(e);return r&&r.isTracking(t)?r.get(t):console.warn("Not tracking velocity of "+t)},e}();V.getByTarget=I,A()&&k.registerPlugin(V);/*!
 * InertiaPlugin 3.10.2
 * https://greensock.com
 *
 * @license Copyright 2008-2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for
 * Club GreenSock members, the agreement issued with that membership.
 * @author: Jack Doyle, <EMAIL>
*/var B,U,q,K,W,G,Y,J,Q,$,ee,et,er=V.getByTarget,ei=function(){return B||(B=window.gsap)&&B.registerPlugin&&B},en=function(e){return"number"==typeof e},es=function(e){return"object"==typeof e},el=function(e){return"function"==typeof e},ea=Array.isArray,eo=function(e){return e},ec=function(e,t,r){for(var i in t)i in e||i===r||(e[i]=t[i]);return e},ed=function e(t){var r,i,n={};for(r in t)n[r]=es(i=t[r])&&!ea(i)?e(i):i;return n},eu=function(e,t,r,i,n){var s,l,a,o,c=t.length,d=0,u=1e10;if(es(e)){for(;c--;){for(a in s=t[c],l=0,e)l+=(o=s[a]-e[a])*o;l<u&&(d=c,u=l)}if(1e10>(n||1e10)&&n<Math.sqrt(u))return e}else for(;c--;)(l=(s=t[c])-e)<0&&(l=-l),l<u&&s>=i&&s<=r&&(d=c,u=l);return t[d]},ep=function(e,t,r,i,n,s,l){if("auto"===e.end)return e;var a,o,c=e.end;if(r=isNaN(r)?1e10:r,i=isNaN(i)?-1e10:i,es(t)){if(a=t.calculated?t:(el(c)?c(t,l):eu(t,c,r,i,s))||t,!t.calculated){for(o in a)t[o]=a[o];t.calculated=!0}a=a[n]}else a=el(c)?c(t,l):ea(c)?eu(t,c,r,i,s):parseFloat(c);return a>r?a=r:a<i&&(a=i),{max:a,min:a,unitFactor:e.unitFactor}},eh=function(e,t,r){return isNaN(e[t])?r:+e[t]},em=function(e,t){return .05*t*e/$},eg=function(e,t,r){return Math.abs((t-e)*$/r/.05)},ef={resistance:1,checkpoint:1,preventOvershoot:1,linkedProps:1,radius:1,duration:1},ex=function(e,t,r,i){if(t.linkedProps){var n,s,l,a,o,c,d=t.linkedProps.split(","),u={};for(n=0;n<d.length;n++)(l=t[s=d[n]])&&(c=Math.abs((a=en(l.velocity)?l.velocity:(o=o||er(e))&&o.isTracking(s)?o.get(s):0)/eh(l,"resistance",i)),u[s]=parseFloat(r(e,s))+em(a,c));return u}},ev=function(e,t,r,i,n,s){if(void 0===r&&(r=10),void 0===i&&(i=.2),void 0===n&&(n=1),void 0===s&&(s=0),"string"==typeof e&&(e=K(e)[0]),!e)return 0;var l,a,o,c,d,u,p,h,m,g,f=0,x=1e10,v=t.inertia||t,y=Q(e).get,j=eh(v,"resistance",G.resistance);for(l in g=ex(e,v,y,j),v)!ef[l]&&(es(a=v[l])||((h=h||er(e))&&h.isTracking(l)?a=en(a)?{velocity:a}:{velocity:h.get(l)}:o=Math.abs((c=+a||0)/j)),es(a)&&(c=en(a.velocity)?a.velocity:(h=h||er(e))&&h.isTracking(l)?h.get(l):0,o=ee(i,r,Math.abs(c/eh(a,"resistance",j))),u=(d=parseFloat(y(e,l))||0)+em(c,o),"end"in a&&(a=ep(a,g&&l in g?g:u,a.max,a.min,l,v.radius,c),s&&(et===t&&(et=v=ed(t)),v[l]=ec(a,v[l],"end"))),"max"in a&&u>+a.max+1e-10?(m=a.unitFactor||G.unitFactors[l]||1,(p=d>a.max&&a.min!==a.max||c*m>-15&&c*m<45?i+(r-i)*.1:eg(d,a.max,c))+n<x&&(x=p+n)):"min"in a&&u<+a.min-1e-10&&(m=a.unitFactor||G.unitFactors[l]||1,(p=d<a.min&&a.min!==a.max||c*m>-45&&c*m<15?i+(r-i)*.1:eg(d,a.min,c))+n<x&&(x=p+n)),p>f&&(f=p)),o>f&&(f=o));return f>x&&(f=x),f>r?r:f<i?i:f},ey=function(){(B=ei())&&(q=B.parseEase,K=B.utils.toArray,Y=B.utils.getUnit,Q=B.core.getCache,ee=B.utils.clamp,$=(W=q("power3"))(.05),J=B.core.PropTween,B.config({resistance:100,unitFactors:{time:1e3,totalTime:1e3,progress:1e3,totalProgress:1e3}}),G=B.config(),B.registerPlugin(V),U=1)},ej={version:"3.10.2",name:"inertia",register:function(e){B=e,ey()},init:function(e,t,r,i,n){U||ey();var s=er(e);if("auto"===t){if(!s){console.warn("No inertia tracking on "+e+". InertiaPlugin.track(target) first.");return}t=s.getAll()}this.target=e,this.tween=r,et=t;var l,a,o,c,d,u,p,h,m,g=e._gsap,f=g.get,x=t.duration,v=es(x),y=t.preventOvershoot||v&&0===x.overshoot,j=eh(t,"resistance",G.resistance),w=en(x)?x:ev(e,t,v&&x.max||10,v&&x.min||.2,v&&"overshoot"in x?+x.overshoot:y?0:1,!0);for(l in t=et,et=0,m=ex(e,t,f,j),t)ef[l]||(el(a=t[l])&&(a=a(i,e,n)),en(a)?d=a:es(a)&&!isNaN(a.velocity)?d=+a.velocity:s&&s.isTracking(l)?d=s.get(l):console.warn("ERROR: No velocity was defined for "+e+" property: "+l),u=em(d,w),h=0,c=Y(o=f(e,l)),o=parseFloat(o),es(a)&&(p=o+u,"end"in a&&(a=ep(a,m&&l in m?m:p,a.max,a.min,l,t.radius,d)),"max"in a&&+a.max<p?y||a.preventOvershoot?u=a.max-o:h=a.max-o-u:"min"in a&&+a.min>p&&(y||a.preventOvershoot?u=a.min-o:h=a.min-o-u)),this._props.push(l),this._pt=new J(this._pt,e,l,o,0,eo,0,g.set(e,l,this)),this._pt.u=c||0,this._pt.c1=u,this._pt.c2=h);return r.duration(w),1},render:function(e,t){var r=t._pt;for(e=W(t.tween._time/t.tween._dur);r;)r.set(r.t,r.p,Math.round(1e4*(r.s+r.c1*e+r.c2*e*e))/1e4+r.u,r.d,e),r=r._next}};"track,untrack,isTracking,getVelocity,getByTarget".split(",").forEach(function(e){return ej[e]=V[e]}),ei()&&B.registerPlugin(ej),p.p8.registerPlugin(C.Draggable,ej),p.p8.registerPlugin(h.ScrollTrigger);let ew=e=>{let{slice:t}=e,{isAppReady:r}=(0,M.R)(c.HS),{height:s}=(0,E.Z)(),[l,a]=(0,n.useState)(0),o=(0,n.useRef)(null),d=(0,n.useRef)(null),m=(0,n.useRef)(null),x=(0,n.useRef)([]);(0,u.Z)(()=>{let e=window.matchMedia("(max-width: 992px)").matches?.5:.75;return a(s*e),r&&(o.current=h.ScrollTrigger.create({trigger:d.current,once:!0,onEnter:()=>{m.current=function(e,t){let r,i,n;e=p.p8.utils.toArray(e),t=t||{};let s=t.onChange,l=0,a=p.p8.timeline({repeat:t.repeat,onUpdate:s&&function(){let t=a.closestIndex();l!==t&&(l=t,s(e[t],t))},paused:t.paused,defaults:{ease:"none"},onReverseComplete:()=>a.totalTime(a.rawTime()+100*a.duration())}),o=e.length,c=e[0].offsetLeft,d=[],u=[],h=[],m=[],g=0,f=t.center,x=100*(t.speed||1),v=!1===t.snap?e=>e:p.p8.utils.snap(t.snap||1),y=0,j=!0===f?e[0].parentNode:p.p8.utils.toArray(f)[0]||e[0].parentNode,w=()=>e[o-1].offsetLeft+m[o-1]/100*u[o-1]-c+h[0]+e[o-1].offsetWidth*p.p8.getProperty(e[o-1],"scaleX")+(parseFloat(t.paddingRight)||0),b=()=>{let t,i=j.getBoundingClientRect();e.forEach((e,r)=>{u[r]=parseFloat(p.p8.getProperty(e,"width","px")),m[r]=v(parseFloat(p.p8.getProperty(e,"x","px"))/u[r]*100+p.p8.getProperty(e,"xPercent")),t=e.getBoundingClientRect(),h[r]=t.left-(r?i.right:i.left),i=t}),p.p8.set(e,{xPercent:e=>m[e]}),r=w()},N=()=>{y=f?a.duration()*(j.offsetWidth/2)/r:0,f&&d.forEach((e,t)=>{d[t]=i(a.labels["label"+t]+a.duration()*u[t]/2/r-y)})},k=(e,t,r)=>{let i,n=e.length,s=1e10,l=0;for(;n--;)(i=Math.abs(e[n]-t))>r/2&&(i=r-i),i<s&&(s=i,l=n);return l},_=()=>{let t,n,s,l,g;for(a.clear(),t=0;t<o;t++)n=e[t],s=m[t]/100*u[t],g=(l=n.offsetLeft+s-c+h[0])+u[t]*p.p8.getProperty(n,"scaleX"),a.to(n,{xPercent:v((s-g)/u[t]*100),duration:g/x},0).fromTo(n,{xPercent:v((s-g+r)/u[t]*100)},{xPercent:m[t],duration:(s-g+r-s)/x,immediateRender:!1},g/x).add("label"+t,l/x),d[t]=l/x;i=p.p8.utils.wrap(0,a.duration())},Z=e=>{let t=a.progress();a.progress(0,!0),b(),e&&_(),N(),e&&a.draggable?a.time(d[g],!0):a.progress(t,!0)};function R(e,t){t=t||{},Math.abs(e-g)>o/2&&(e+=e>g?-o:o);let r=p.p8.utils.wrap(0,o,e),s=d[r];return s>a.time()!=e>g&&(s+=a.duration()*(e>g?1:-1)),(s<0||s>a.duration())&&(t.modifiers={time:i}),g=r,t.overwrite=!0,p.p8.killTweensOf(n),a.tweenTo(s,t)}if(p.p8.set(e,{x:0}),b(),_(),N(),window.addEventListener("resize",()=>Z(!0)),a.next=e=>R(g+1,e),a.previous=e=>R(g-1,e),a.current=()=>g,a.refresh=e=>Z(e),a.toIndex=(e,t)=>R(e,t),a.closestIndex=e=>{let t=k(d,a.time(),a.duration());return e&&(g=t),t},a.times=d,a.progress(1,!0).progress(0,!0),t.reversed&&(a.vars.onReverseComplete(),a.reverse()),t.draggable&&"function"==typeof C.Draggable){let s,l;n=document.createElement("div");let o=p.p8.utils.wrap(0,1),c=()=>a.progress(o(l+(h.startX-h.x)*s)),u=()=>a.closestIndex(!0);void 0===ej&&console.warn("InertiaPlugin required for momentum-based scrolling and snapping. https://greensock.com/club");let h=C.Draggable.create(n,{trigger:e[0].parentNode,type:"x",onPressInit(){p.p8.killTweensOf(a),l=a.progress(),Z(),s=1/r,p.p8.set(n,{x:-(l/s)})},onDrag:c,onThrowUpdate:c,inertia:!0,dragResistance:t.dragResistance,snap:e=>{if(t.snap){let t=-(e*s)*a.duration(),r=i(t),n=d[k(d,r,a.duration())],l=n-r;return Math.abs(l)>a.duration()/2&&(l+=l<0?a.duration():-a.duration()),-((t+l)/a.duration()/s)}},onRelease:u,onThrowComplete:u})[0];a.draggable=h}return a.closestIndex(!0),s&&s(e[g],g),a}(x.current,{speed:.2,repeat:!0,draggable:!0,dragResistance:.35,snap:!1})}})),()=>{var e,t;null===(e=o.current)||void 0===e||e.kill(),null==m||null===(t=m.current)||void 0===t||t.kill()}},[r,s]);let v=(e,t)=>"".concat(l*(e/t),"px");return(0,i.jsx)("section",{ref:d,className:"py-32 md:py-96 overflow-hidden",children:(0,i.jsx)(f.Z,{type:"slider",children:(0,i.jsx)("div",{className:"flex flex-nowrap w-full h-[50vh] md:h-[75vh]",children:t.items.map((e,t)=>(0,i.jsx)("div",{ref:e=>x.current[t]=e,className:"flex-none pr-12",children:(0,i.jsx)("div",{className:"relative h-full",style:{width:v(e.image.dimensions.width,e.image.dimensions.height)},children:(0,i.jsx)(g.Z,{field:e.image,sizes:v(e.image.dimensions.width,e.image.dimensions.height),className:"w-full h-full object-cover",draggable:"false"})})},t))})})})},eb=e=>{let{slice:t}=e;return t.items&&(0,i.jsx)("section",{className:"flex justify-between py-40 md:py-64 px-20 md:px-40 type-body",children:t.items.map((e,t)=>(0,i.jsx)("div",{children:e.text},t))})};var eN=r(9601);p.p8.registerPlugin(h.ScrollTrigger);let ek=e=>{let{slice:t}=e,r=t.primary.title,s=t.primary.first_column,l=t.primary.second_column,a=!!(null==s?void 0:s.length),o=!!(null==l?void 0:l.length),[c,p]=(0,n.useState)(!1),g=(0,n.useRef)(null),f=(0,n.useRef)(null);return(0,u.Z)(()=>(g.current=h.ScrollTrigger.create({trigger:f.current,start:"25% bottom",once:!0,onEnter:()=>p(!0)}),()=>{var e;null===(e=g.current)||void 0===e||e.kill()}),[]),(0,i.jsx)("section",{ref:f,className:"flex items-center md:min-h-[50vh] px-20 md:px-[7vw] py-32 md:py-64 text-14 md:text-20",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row md:items-start md:justify-around w-full",children:[(0,i.jsx)("div",{className:"col-span-3 min-w-[15ch] md:min-w-0 max-w-[17ch] mb-16",children:r&&(0,i.jsx)("div",{className:"type-subtitle",children:(0,i.jsx)(m.Z,{appear:c,stagger:.05,children:(0,i.jsx)(d.K,{field:r})})})}),(a||o)&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"col-span-3 md:w-[26ch]",children:a&&o&&(0,i.jsx)("div",{className:"mb-16 md:mb-32",children:(0,i.jsx)(m.Z,{appear:c,delay:.5,stagger:.015,chars:!1,children:(0,i.jsx)(eN.v,{field:s})})})}),(0,i.jsx)("div",{className:"col-span-3 md:w-[26ch]",children:o?(0,i.jsx)("div",{className:"mb-16 md:mb-32",children:(0,i.jsx)(m.Z,{appear:c,delay:.75,stagger:.015,chars:!1,children:(0,i.jsx)(eN.v,{field:l})})}):(0,i.jsx)("div",{className:"mb-16 md:mb-32",children:(0,i.jsx)(m.Z,{appear:c,delay:.5,stagger:.015,chars:!1,children:(0,i.jsx)(eN.v,{field:s})})})})]})]})})};p.p8.registerPlugin(h.ScrollTrigger);let e_=e=>{let{slice:t}=e,{isAppReady:r}=(0,M.R)(c.HS),s=(0,E.Z)(),l=(0,n.useRef)(null),a=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,u.Z)(()=>{if(void 0===s.width||!r)return;let e=t.primary.image.dimensions.height/t.primary.image.dimensions.width,i=s.width*e*.15,n=o.current.clientHeight-i;n>0&&p.p8.set(a.current,{height:n});let c=p.p8.fromTo(o.current,{y:-i,scale:1.15},{y:0,scale:1,ease:"none"});return l.current=h.ScrollTrigger.create({trigger:a.current,scrub:!0,animation:c,onEnter:e=>e.refresh()}),()=>{var e;null===(e=l.current)||void 0===e||e.kill()}},[s,r]),(0,i.jsx)("section",{ref:a,className:"overflow-hidden",children:(0,i.jsx)("div",{ref:o,children:(0,i.jsx)(g.Z,{field:t.primary.image,sizes:"100vw"})})})};var eZ=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3200,r="";navigator&&navigator.connection&&navigator.connection.type&&(r=navigator.connection.type),navigator&&navigator.connection&&navigator.connection.effectiveType&&(r=navigator.connection.effectiveType);let i=window.devicePixelRatio?window.devicePixelRatio:1;return r&&"wifi"!==r&&"4g"!==r&&(i=1),(e*=i)>t&&(e=t),e};p.p8.registerPlugin(h.ScrollTrigger);let eR=e=>{let{slice:t}=e,{width:r}=(0,E.Z)(),[s,l]=(0,n.useState)(null),a=(0,n.useRef)(null),o=(0,n.useRef)(null),c=(0,n.useRef)(null),d=(0,n.useRef)(null);return(0,u.Z)(()=>{if(void 0!==r){var e,i,n,s;let a=eZ(r);480>a&&(null===(e=t.primary.video_360p)||void 0===e?void 0:e.url)?l(t.primary.video_360p.url):720>a&&(null===(i=t.primary.video_540p)||void 0===i?void 0:i.url)?l(t.primary.video_540p.url):1080>a&&(null===(n=t.primary.video_720p)||void 0===n?void 0:n.url)?l(t.primary.video_720p.url):(null===(s=t.primary.video_1080p)||void 0===s?void 0:s.url)&&l(t.primary.video_1080p.url)}},[]),(0,u.Z)(()=>(a.current=h.ScrollTrigger.create({trigger:o.current,toggleActions:"play none none reset",animation:p.p8.fromTo(c.current,{opacity:0,scale:.7},{opacity:1,scale:1,ease:"expo.out",duration:1}),onToggle:e=>{let{isActive:t}=e,r=d.current.play();t||void 0===r?d.current.play():r.then(()=>{d.current.pause()}).catch(e=>console.warn(e))}}),()=>{var e;null===(e=a.current)||void 0===e||e.kill()}),[]),(0,i.jsx)("section",{className:"py-40 md:py-96 px-20 md:px-40",children:(0,i.jsx)("div",{ref:o,children:(0,i.jsx)("div",{ref:c,children:(0,i.jsx)("video",{ref:d,className:"w-full",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,children:(0,i.jsx)("source",{src:s,type:"video/mp4"})},s)})})})},eP={image:w,image_columns:N,infinite_image_carousel:ew,meta_info:eb,paragraph:ek,parallax_image:e_,video:eR};var eT=r(6465),eS=r.n(eT),eF=r(2578),eE=r(1163),eM=r(5341),eC=r(2016),eA=r(621);p.p8.registerPlugin(h.ScrollTrigger,eM.Flip);let ez=e=>{var t,r;let{project:s}=e,l=(0,eE.useRouter)(),a=(0,eA.L)(),[o,g]=(0,n.useState)(!0),[j,w]=(0,n.useState)(!1),b=(0,n.useRef)(null),N=(0,n.useRef)(null),k=(0,n.useRef)(null),_=(0,n.useRef)(null),Z=(0,n.useRef)(null),R=null===(t=s.data.hyphenated_title)||void 0===t?void 0:null===(r=t[0])||void 0===r?void 0:r.text;(0,u.Z)(()=>(g(window.matchMedia("(hover: none)").matches),b.current=h.ScrollTrigger.create({trigger:N.current,start:"33% bottom",once:!0,onEnter:()=>w(!0)}),()=>{var e;null===(e=b.current)||void 0===e||e.kill()}),[]);let P=e=>{let t=window.matchMedia("(max-width: 992px)").matches;o||t?S():e.preventDefault()},T=(0,n.useCallback)(()=>{null==a||a.stop();let e=eM.Flip.getState(k.current),t=_.current;t&&t.appendChild(k.current),eM.Flip.from(e,{duration:1.5,ease:"expo.inOut"}).to(["#next-project-heading","#next-project-arrow","#project-content"],{autoAlpha:0,duration:1,ease:"expo.inOut"},0).call(()=>{l.push(s.url,void 0,{scroll:!1}),a.start()},[])},[a,l,s.url]),S=(0,n.useCallback)(()=>{c.Xr.type="default",c.Du.to="/project/[uid]",window.matchMedia("(max-width: 992px)").matches||T()},[T]),F=(0,n.useCallback)(()=>(c.Xr.isPressHold=!0,Z.current=p.p8.delayedCall(2,S),()=>{var e;null===(e=Z.current)||void 0===e||e.kill()}),[S]),E=(0,n.useCallback)(()=>{var e;c.Xr.isPressHold=!1,null===(e=Z.current)||void 0===e||e.kill()},[]);return(0,i.jsxs)("section",{className:"jsx-3dd7d6eb7fd849b5 pt-64 md:pt-120",children:[(0,i.jsx)("div",{className:"jsx-3dd7d6eb7fd849b5 flex flex-col pt-[calc(var(--site-header-height)*1.75)]",children:(0,i.jsx)(f.Z,{type:"hold",children:(0,i.jsx)(eC.Z,{href:s.url,onClick:P,className:"block",children:(0,i.jsxs)("div",{ref:N,onMouseDown:o?null:F,onMouseUp:o?null:E,onMouseLeave:o?null:E,className:"jsx-3dd7d6eb7fd849b5 flex flex-col px-20 md:px-40 next-project-max-height overflow-hidden",children:[(0,i.jsx)("div",{id:"next-project-heading",className:"jsx-3dd7d6eb7fd849b5 w-full type-heading -mb-20 md:-mb-40",children:(0,i.jsxs)(m.Z,{appear:j,delay:.15,stagger:.05,children:["Next is",(0,i.jsx)("br",{className:"jsx-3dd7d6eb7fd849b5"}),(0,i.jsx)("em",{className:"jsx-3dd7d6eb7fd849b5 "+((0,y.Z)(R?"hidden sm:block":"block")||""),children:(0,i.jsx)(d.K,{field:s.data.title})}),R&&(0,i.jsx)("em",{className:"jsx-3dd7d6eb7fd849b5 block sm:hidden",children:(0,i.jsx)(d.K,{field:s.data.hyphenated_title})})]})}),(0,i.jsx)("div",{id:"next-project-arrow",className:"jsx-3dd7d6eb7fd849b5 ml-auto mb-40 md:mb-64",children:(0,i.jsx)("div",{className:"jsx-3dd7d6eb7fd849b5 rotate-180 type-subtitle",children:(0,i.jsx)(v.Z,{show:j,duration:.75,delay:1})})}),(0,i.jsx)("div",{className:"jsx-3dd7d6eb7fd849b5 aspect-2/1",children:(0,i.jsx)("div",{ref:k,className:"jsx-3dd7d6eb7fd849b5",children:(0,i.jsx)(x.Z,{children:(0,i.jsx)(eF.Z,{field:s.data.image,sizes:"100vw",imgixParams:{q:75,auto:["format"]},fallbackAlt:""})})})})]})})})}),(0,i.jsx)("div",{ref:_,className:"jsx-3dd7d6eb7fd849b5 transition-target fixed -z-10 left-20 right-20 md:left-40 md:right-40 pointer-events-none aspect-2/1"}),(0,i.jsx)(eS(),{id:"3dd7d6eb7fd849b5",children:".next-project-max-height.jsx-3dd7d6eb7fd849b5{max-height:-webkit-calc(100vh - var(--site-header-height)*1.75);max-height:-moz-calc(100vh - var(--site-header-height)*1.75);max-height:calc(100vh - var(--site-header-height)*1.75);max-height:-webkit-calc(100svh - var(--site-header-height)*1.75);max-height:-moz-calc(100svh - var(--site-header-height)*1.75);max-height:calc(100svh - var(--site-header-height)*1.75)}.transition-target.jsx-3dd7d6eb7fd849b5{top:-webkit-calc(var(--site-header-height)*1.75 + 8rem);top:-moz-calc(var(--site-header-height)*1.75 + 8rem);top:calc(var(--site-header-height)*1.75 + 8rem)}"})]})},eO=e=>{let{page:t,nextProject:r}=e;if((0,n.useEffect)(()=>{c.mj.id="work"},[]),!t)return null;let l=o.S(t.data.meta_description),d=t.data.image.social;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(a(),{children:[(0,i.jsxs)("title",{children:[o.S(t.data.title)," - Design is Funny"]}),(0,i.jsx)("meta",{property:"og:title",content:o.S(t.data.title)}),l&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{name:"description",content:l}),(0,i.jsx)("meta",{property:"og:description",content:l})]}),d&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{property:"og:image",content:d.url}),(0,i.jsx)("meta",{property:"og:image:width",content:d.dimensions.width.toString()}),(0,i.jsx)("meta",{property:"og:image:height",content:d.dimensions.height.toString()})]})]}),(0,i.jsxs)("div",{id:"project-content",className:"relative",children:[(0,i.jsxs)("div",{className:"absolute top-32 right-20 text-right type-body block md:hidden",children:["\xa9",new Date().getFullYear()]}),(0,i.jsx)(j,{title:t.data.title,image:t.data.image,type:t.data.type,skills:t.data.skills}),(0,i.jsx)(s.p,{slices:t.data.slices,components:eP})]}),(0,i.jsx)(ez,{project:r})]})};var eI=!0,eL=eO}},function(e){e.O(0,[914,842,774,888,179],function(){return e(e.s=2328)}),_N_E=e.O()}]);