(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[704],{3758:function(e,t,n){"use strict";n.d(t,{S:function(){return i}});var r=n(7294),a=n(230);function i({pixelated:e}){let t=(0,a.z)(e=>e.gl),n=(0,a.z)(e=>e.internal.active),i=(0,a.z)(e=>e.performance.current),l=(0,a.z)(e=>e.viewport.initialDpr),o=(0,a.z)(e=>e.setDpr);return r.useEffect(()=>{let r=t.domElement;return()=>{n&&o(l),e&&r&&(r.style.imageRendering="auto")}},[]),r.useEffect(()=>{o(i*l),e&&t.domElement&&(t.domElement.style.imageRendering=1===i?"auto":"pixelated")},[i]),null}},3520:function(e,t,n){"use strict";n.d(t,{E:function(){return i}});var r=n(7294),a=n(230);function i(){let e=(0,a.z)(e=>e.get),t=(0,a.z)(e=>e.setEvents),n=(0,a.z)(e=>e.performance.current);return r.useEffect(()=>{let n=e().events.enabled;return()=>t({enabled:n})},[]),r.useEffect(()=>t({enabled:1===n}),[n]),null}},2749:function(e,t,n){"use strict";n.d(t,{qA:function(){return C}});var r=n(7462),a=n(7294),i=n(230),l=n(9477);let o=e=>e&&e.isCubeTexture;class s extends l.Mesh{constructor(e,t){var n,r;let a=o(e),i=null!=(n=a?null===(r=e.image[0])||void 0===r?void 0:r.width:e.image.width)?n:1024,s=Math.floor(Math.log2(i/4)),u=Math.pow(2,s),c=[a?"#define ENVMAP_TYPE_CUBE":"",`#define CUBEUV_TEXEL_WIDTH ${1/(3*Math.max(u,112))}`,`#define CUBEUV_TEXEL_HEIGHT ${1/(4*u)}`,`#define CUBEUV_MAX_MIP ${s}.0`],f=`
        varying vec3 vWorldPosition;
        void main() 
        {
            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );
            vWorldPosition = worldPosition.xyz;
            
            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
        }
        `,d=c.join("\n")+`
        #define ENVMAP_TYPE_CUBE_UV
        varying vec3 vWorldPosition;
        uniform float radius;
        uniform float height;
        uniform float angle;
        #ifdef ENVMAP_TYPE_CUBE
            uniform samplerCube map;
        #else
            uniform sampler2D map;
        #endif
        // From: https://www.shadertoy.com/view/4tsBD7
        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) 
        {
            float d = dot ( rd, n );
            
            if( d > 0.0 ) { return 1e6; }
            
            vec3  o = ro - c;
            float t = - dot( n, o ) / d;
            vec3  q = o + rd * t;
            
            return ( dot( q, q ) < r * r ) ? t : 1e6;
        }
        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm
        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) 
        {
            vec3 oc = ro - ce;
            float b = dot( oc, rd );
            float c = dot( oc, oc ) - ra * ra;
            float h = b * b - c;
            
            if( h < 0.0 ) { return -1.0; }
            
            h = sqrt( h );
            
            return - b + h;
        }
        vec3 project() 
        {
            vec3 p = normalize( vWorldPosition );
            vec3 camPos = cameraPosition;
            camPos.y -= height;
            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );
            if( intersection > 0.0 ) {
                
                vec3 h = vec3( 0.0, - height, 0.0 );
                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );
                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;
            } else {
                p = vec3( 0.0, 1.0, 0.0 );
            }
            return p;
        }
        #include <common>
        #include <cube_uv_reflection_fragment>
        void main() 
        {
            vec3 projectedWorldPosition = project();
            
            #ifdef ENVMAP_TYPE_CUBE
                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;
            #else
                vec3 direction = normalize( projectedWorldPosition );
                vec2 uv = equirectUv( direction );
                vec3 outcolor = texture2D( map, uv ).rgb;
            #endif
            gl_FragColor = vec4( outcolor, 1.0 );
            #include <tonemapping_fragment>
            #include <encodings_fragment>
        }
        `,p={map:{value:e},height:{value:(null==t?void 0:t.height)||15},radius:{value:(null==t?void 0:t.radius)||100}},h=new l.IcosahedronGeometry(1,16),m=new l.ShaderMaterial({uniforms:p,fragmentShader:d,vertexShader:f,side:l.DoubleSide});super(h,m)}set radius(e){this.material.uniforms.radius.value=e}get radius(){return this.material.uniforms.radius.value}set height(e){this.material.uniforms.height.value=e}get height(){return this.material.uniforms.height.value}}var u=n(8197);let c={sunset:"venice/venice_sunset_1k.hdr",dawn:"kiara/kiara_1_dawn_1k.hdr",night:"dikhololo/dikhololo_night_1k.hdr",warehouse:"empty-wharehouse/empty_warehouse_01_1k.hdr",forest:"forrest-slope/forest_slope_1k.hdr",apartment:"lebombo/lebombo_1k.hdr",studio:"studio-small-3/studio_small_03_1k.hdr",city:"potsdamer-platz/potsdamer_platz_1k.hdr",park:"rooitou/rooitou_park_1k.hdr",lobby:"st-fagans/st_fagans_interior_1k.hdr"};function f({files:e=["/px.png","/nx.png","/py.png","/ny.png","/pz.png","/nz.png"],path:t="",preset:n,encoding:r,extensions:a}={}){if(n){if(!(n in c))throw Error("Preset must be one of: "+Object.keys(c).join(", "));e=c[n],t="https://market-assets.fra1.cdn.digitaloceanspaces.com/market-assets/hdris/"}let o=Array.isArray(e),s=o?l.CubeTextureLoader:u.x,f=(0,i.D)(s,o?[e]:e,e=>{e.setPath(t),a&&a(e)}),d=o?f[0]:f;return d.mapping=o?l.CubeReflectionMapping:l.EquirectangularReflectionMapping,d.encoding=(null!=r?r:o)?l.sRGBEncoding:l.LinearEncoding,d}let d=e=>e.current&&e.current.isScene,p=e=>d(e)?e.current:e;function h(e,t,n,r,a=0){let i=p(t||n),l=i.background,o=i.environment,s=i.backgroundBlurriness||0;return"only"!==e&&(i.environment=r),e&&(i.background=r),e&&void 0!==i.backgroundBlurriness&&(i.backgroundBlurriness=a),()=>{"only"!==e&&(i.environment=o),e&&(i.background=l),e&&void 0!==i.backgroundBlurriness&&(i.backgroundBlurriness=s)}}function m({scene:e,background:t=!1,blur:n,map:r}){let l=(0,i.z)(e=>e.scene);return a.useLayoutEffect(()=>{if(r)return h(t,e,l,r,n)},[l,e,r,t,n]),null}function A({background:e=!1,scene:t,blur:n,...r}){let l=f(r),o=(0,i.z)(e=>e.scene);return a.useLayoutEffect(()=>h(e,t,o,l,n),[l,e,t,o,n]),null}function B({children:e,near:t=1,far:n=1e3,resolution:r=256,frames:o=1,map:s,background:u=!1,blur:c,scene:f,files:d,path:p,preset:B,extensions:g}){let C=(0,i.z)(e=>e.gl),v=(0,i.z)(e=>e.scene),b=a.useRef(null),[y]=a.useState(()=>new l.Scene),E=a.useMemo(()=>{let e=new l.WebGLCubeRenderTarget(r);return e.texture.type=l.HalfFloatType,e},[r]);a.useLayoutEffect(()=>(1===o&&b.current.update(C,y),h(u,f,v,E.texture,c)),[e,y,E.texture,f,v,u,o,C]);let M=1;return(0,i.A)(()=>{(o===1/0||M<o)&&(b.current.update(C,y),M++)}),a.createElement(a.Fragment,null,(0,i.g)(a.createElement(a.Fragment,null,e,a.createElement("cubeCamera",{ref:b,args:[t,n,E]}),d||B?a.createElement(A,{background:!0,files:d,preset:B,path:p,extensions:g}):s?a.createElement(m,{background:!0,map:s,extensions:g}):null),y))}function g(e){var t,n,l,o;let u=f(e),c=e.map||u;a.useMemo(()=>(0,i.e)({GroundProjectedEnvImpl:s}),[]);let d=a.useMemo(()=>[c],[c]),p=null==(t=e.ground)?void 0:t.height,h=null==(n=e.ground)?void 0:n.radius,A=null!==(l=null==(o=e.ground)?void 0:o.scale)&&void 0!==l?l:1e3;return a.createElement(a.Fragment,null,a.createElement(m,(0,r.Z)({},e,{map:c})),a.createElement("groundProjectedEnvImpl",{args:d,scale:A,height:p,radius:h}))}function C(e){return e.ground?a.createElement(g,e):e.map?a.createElement(m,e):e.children?a.createElement(B,e):a.createElement(A,e)}},3435:function(e,t,n){"use strict";n.d(t,{z:function(){return c}});var r=n(7462),a=n(9477),i=n(7294),l=n(230),o=n(319);let s=function(e,t,n,r){let i=class extends a.ShaderMaterial{constructor(i={}){let l=Object.entries(e);super({uniforms:l.reduce((e,[t,n])=>{let r=a.UniformsUtils.clone({[t]:{value:n}});return{...e,...r}},{}),vertexShader:t,fragmentShader:n}),this.key="",l.forEach(([e])=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t})),Object.assign(this,i),r&&r(this)}};return i.key=a.MathUtils.generateUUID(),i}({},"void main() { }","void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }");class u extends a.MeshPhysicalMaterial{constructor(e=6,t=!1){super(),this.uniforms={chromaticAberration:{value:.05},transmission:{value:0},_transmission:{value:1},transmissionMap:{value:null},roughness:{value:0},thickness:{value:0},thicknessMap:{value:null},attenuationDistance:{value:1/0},attenuationColor:{value:new a.Color("white")},anisotropy:{value:.1},time:{value:0},distortion:{value:0},distortionScale:{value:.5},temporalDistortion:{value:0},buffer:{value:null}},this.onBeforeCompile=n=>{n.uniforms={...n.uniforms,...this.uniforms},t?n.defines.USE_SAMPLER="":n.defines.USE_TRANSMISSION="",n.fragmentShader=`
      uniform float chromaticAberration;         
      uniform float anisotropy;      
      uniform float time;
      uniform float distortion;
      uniform float distortionScale;
      uniform float temporalDistortion;
      uniform sampler2D buffer;

      vec3 random3(vec3 c) {
        float j = 4096.0*sin(dot(c,vec3(17.0, 59.4, 15.0)));
        vec3 r;
        r.z = fract(512.0*j);
        j *= .125;
        r.x = fract(512.0*j);
        j *= .125;
        r.y = fract(512.0*j);
        return r-0.5;
      }

      float seed = 0.0;
      uint hash( uint x ) {
        x += ( x << 10u );
        x ^= ( x >>  6u );
        x += ( x <<  3u );
        x ^= ( x >> 11u );
        x += ( x << 15u );
        return x;
      }

      // Compound versions of the hashing algorithm I whipped together.
      uint hash( uvec2 v ) { return hash( v.x ^ hash(v.y)                         ); }
      uint hash( uvec3 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z)             ); }
      uint hash( uvec4 v ) { return hash( v.x ^ hash(v.y) ^ hash(v.z) ^ hash(v.w) ); }

      // Construct a float with half-open range [0:1] using low 23 bits.
      // All zeroes yields 0.0, all ones yields the next smallest representable value below 1.0.
      float floatConstruct( uint m ) {
        const uint ieeeMantissa = 0x007FFFFFu; // binary32 mantissa bitmask
        const uint ieeeOne      = 0x3F800000u; // 1.0 in IEEE binary32
        m &= ieeeMantissa;                     // Keep only mantissa bits (fractional part)
        m |= ieeeOne;                          // Add fractional part to 1.0
        float  f = uintBitsToFloat( m );       // Range [1:2]
        return f - 1.0;                        // Range [0:1]
      }

      // Pseudo-random value in half-open range [0:1].
      float random( float x ) { return floatConstruct(hash(floatBitsToUint(x))); }
      float random( vec2  v ) { return floatConstruct(hash(floatBitsToUint(v))); }
      float random( vec3  v ) { return floatConstruct(hash(floatBitsToUint(v))); }
      float random( vec4  v ) { return floatConstruct(hash(floatBitsToUint(v))); }

      float rand() {
        float result = random(vec3(gl_FragCoord.xy, seed));
        seed += 1.0;
        return result;
      }

      const float F3 =  0.3333333;
      const float G3 =  0.1666667;

      float snoise(vec3 p) {
        vec3 s = floor(p + dot(p, vec3(F3)));
        vec3 x = p - s + dot(s, vec3(G3));
        vec3 e = step(vec3(0.0), x - x.yzx);
        vec3 i1 = e*(1.0 - e.zxy);
        vec3 i2 = 1.0 - e.zxy*(1.0 - e);
        vec3 x1 = x - i1 + G3;
        vec3 x2 = x - i2 + 2.0*G3;
        vec3 x3 = x - 1.0 + 3.0*G3;
        vec4 w, d;
        w.x = dot(x, x);
        w.y = dot(x1, x1);
        w.z = dot(x2, x2);
        w.w = dot(x3, x3);
        w = max(0.6 - w, 0.0);
        d.x = dot(random3(s), x);
        d.y = dot(random3(s + i1), x1);
        d.z = dot(random3(s + i2), x2);
        d.w = dot(random3(s + 1.0), x3);
        w *= w;
        w *= w;
        d *= w;
        return dot(d, vec4(52.0));
      }

      float snoiseFractal(vec3 m) {
        return 0.5333333* snoise(m)
              +0.2666667* snoise(2.0*m)
              +0.1333333* snoise(4.0*m)
              +0.0666667* snoise(8.0*m);
      }
`+n.fragmentShader,n.fragmentShader=n.fragmentShader.replace("#include <transmission_pars_fragment>",`
        #ifdef USE_TRANSMISSION
          // Transmission code is based on glTF-Sampler-Viewer
          // https://github.com/KhronosGroup/glTF-Sample-Viewer
          uniform float _transmission;
          uniform float thickness;
          uniform float attenuationDistance;
          uniform vec3 attenuationColor;
          #ifdef USE_TRANSMISSIONMAP
            uniform sampler2D transmissionMap;
          #endif
          #ifdef USE_THICKNESSMAP
            uniform sampler2D thicknessMap;
          #endif
          uniform vec2 transmissionSamplerSize;
          uniform sampler2D transmissionSamplerMap;
          uniform mat4 modelMatrix;
          uniform mat4 projectionMatrix;
          varying vec3 vWorldPosition;
          vec3 getVolumeTransmissionRay( const in vec3 n, const in vec3 v, const in float thickness, const in float ior, const in mat4 modelMatrix ) {
            // Direction of refracted light.
            vec3 refractionVector = refract( - v, normalize( n ), 1.0 / ior );
            // Compute rotation-independant scaling of the model matrix.
            vec3 modelScale;
            modelScale.x = length( vec3( modelMatrix[ 0 ].xyz ) );
            modelScale.y = length( vec3( modelMatrix[ 1 ].xyz ) );
            modelScale.z = length( vec3( modelMatrix[ 2 ].xyz ) );
            // The thickness is specified in local space.
            return normalize( refractionVector ) * thickness * modelScale;
          }
          float applyIorToRoughness( const in float roughness, const in float ior ) {
            // Scale roughness with IOR so that an IOR of 1.0 results in no microfacet refraction and
            // an IOR of 1.5 results in the default amount of microfacet refraction.
            return roughness * clamp( ior * 2.0 - 2.0, 0.0, 1.0 );
          }
          vec4 getTransmissionSample( const in vec2 fragCoord, const in float roughness, const in float ior ) {
            float framebufferLod = log2( transmissionSamplerSize.x ) * applyIorToRoughness( roughness, ior );            
            #ifdef USE_SAMPLER
              #ifdef texture2DLodEXT
                return texture2DLodEXT(transmissionSamplerMap, fragCoord.xy, framebufferLod);
              #else
                return texture2D(transmissionSamplerMap, fragCoord.xy, framebufferLod);
              #endif
            #else
              return texture2D(buffer, fragCoord.xy);
            #endif
          }
          vec3 applyVolumeAttenuation( const in vec3 radiance, const in float transmissionDistance, const in vec3 attenuationColor, const in float attenuationDistance ) {
            if ( isinf( attenuationDistance ) ) {
              // Attenuation distance is +∞, i.e. the transmitted color is not attenuated at all.
              return radiance;
            } else {
              // Compute light attenuation using Beer's law.
              vec3 attenuationCoefficient = -log( attenuationColor ) / attenuationDistance;
              vec3 transmittance = exp( - attenuationCoefficient * transmissionDistance ); // Beer's law
              return transmittance * radiance;
            }
          }
          vec4 getIBLVolumeRefraction( const in vec3 n, const in vec3 v, const in float roughness, const in vec3 diffuseColor,
            const in vec3 specularColor, const in float specularF90, const in vec3 position, const in mat4 modelMatrix,
            const in mat4 viewMatrix, const in mat4 projMatrix, const in float ior, const in float thickness,
            const in vec3 attenuationColor, const in float attenuationDistance ) {
            vec3 transmissionRay = getVolumeTransmissionRay( n, v, thickness, ior, modelMatrix );
            vec3 refractedRayExit = position + transmissionRay;
            // Project refracted vector on the framebuffer, while mapping to normalized device coordinates.
            vec4 ndcPos = projMatrix * viewMatrix * vec4( refractedRayExit, 1.0 );
            vec2 refractionCoords = ndcPos.xy / ndcPos.w;
            refractionCoords += 1.0;
            refractionCoords /= 2.0;
            // Sample framebuffer to get pixel the refracted ray hits.
            vec4 transmittedLight = getTransmissionSample( refractionCoords, roughness, ior );
            vec3 attenuatedColor = applyVolumeAttenuation( transmittedLight.rgb, length( transmissionRay ), attenuationColor, attenuationDistance );
            // Get the specular component.
            vec3 F = EnvironmentBRDF( n, v, specularColor, specularF90, roughness );
            return vec4( ( 1.0 - F ) * attenuatedColor * diffuseColor, transmittedLight.a );
          }
        #endif
`),n.fragmentShader=n.fragmentShader.replace("#include <transmission_fragment>",`  
        // Improve the refraction to use the world pos
        material.transmission = _transmission;
        material.transmissionAlpha = 1.0;
        material.thickness = thickness;
        material.attenuationDistance = attenuationDistance;
        material.attenuationColor = attenuationColor;
        #ifdef USE_TRANSMISSIONMAP
          material.transmission *= texture2D( transmissionMap, vUv ).r;
        #endif
        #ifdef USE_THICKNESSMAP
          material.thickness *= texture2D( thicknessMap, vUv ).g;
        #endif
        
        vec3 pos = vWorldPosition;
        vec3 v = normalize( cameraPosition - pos );
        vec3 n = inverseTransformDirection( normal, viewMatrix );
        vec3 transmission = vec3(0.0);
        float transmissionR, transmissionB, transmissionG;
        float randomCoords = rand();
        float thickness_smear = thickness * max(pow(roughnessFactor, 0.33), anisotropy);
        vec3 distortionNormal = vec3(0.0);
        vec3 temporalOffset = vec3(time, -time, -time) * temporalDistortion;
        if (distortion > 0.0) {
          distortionNormal = distortion * vec3(snoiseFractal(vec3((pos * distortionScale + temporalOffset))), snoiseFractal(vec3(pos.zxy * distortionScale - temporalOffset)), snoiseFractal(vec3(pos.yxz * distortionScale + temporalOffset)));
        }
        for (float i = 0.0; i < ${e}.0; i ++) {
          vec3 sampleNorm = normalize(n + roughnessFactor * roughnessFactor * 2.0 * normalize(vec3(rand() - 0.5, rand() - 0.5, rand() - 0.5)) * pow(rand(), 0.33) + distortionNormal);
          transmissionR = getIBLVolumeRefraction(
            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,
            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior, material.thickness  + thickness_smear * (i + randomCoords) / float(${e}),
            material.attenuationColor, material.attenuationDistance
          ).r;
          transmissionG = getIBLVolumeRefraction(
            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,
            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior  * (1.0 + chromaticAberration * (i + randomCoords) / float(${e})) , material.thickness + thickness_smear * (i + randomCoords) / float(${e}),
            material.attenuationColor, material.attenuationDistance
          ).g;
          transmissionB = getIBLVolumeRefraction(
            sampleNorm, v, material.roughness, material.diffuseColor, material.specularColor, material.specularF90,
            pos, modelMatrix, viewMatrix, projectionMatrix, material.ior * (1.0 + 2.0 * chromaticAberration * (i + randomCoords) / float(${e})), material.thickness + thickness_smear * (i + randomCoords) / float(${e}),
            material.attenuationColor, material.attenuationDistance
          ).b;
          transmission.r += transmissionR;
          transmission.g += transmissionG;
          transmission.b += transmissionB;
        }
        transmission /= ${e}.0;
        totalDiffuse = mix( totalDiffuse, transmission.rgb, material.transmission );
`)},Object.keys(this.uniforms).forEach(e=>Object.defineProperty(this,e,{get:()=>this.uniforms[e].value,set:t=>this.uniforms[e].value=t}))}}let c=i.forwardRef(({buffer:e,transmissionSampler:t=!1,backside:n=!1,side:c=a.FrontSide,transmission:f=1,thickness:d=0,backsideThickness:p=0,samples:h=10,resolution:m,backsideResolution:A,background:B,...g},C)=>{let v,b,y;(0,l.e)({MeshTransmissionMaterial:u});let E=i.useRef(null),[M]=i.useState(()=>new s),F=(0,o.R)(A||m),S=(0,o.R)(m);return(0,l.A)(e=>{E.current.time=e.clock.getElapsedTime(),E.current.buffer===S.texture&&!t&&(y=E.current.__r3f.parent)&&(b=e.gl.toneMapping,v=e.scene.background,e.gl.toneMapping=a.NoToneMapping,B&&(e.scene.background=B),y.material=M,n&&(e.gl.setRenderTarget(F),e.gl.render(e.scene,e.camera),y.material=E.current,y.material.buffer=F.texture,y.material.thickness=p,y.material.side=a.BackSide),e.gl.setRenderTarget(S),e.gl.render(e.scene,e.camera),y.material.thickness=d,y.material.side=c,y.material.buffer=S.texture,e.scene.background=v,e.gl.setRenderTarget(null),y.material=E.current,e.gl.toneMapping=b)}),i.useImperativeHandle(C,()=>E.current,[]),i.createElement("meshTransmissionMaterial",(0,r.Z)({args:[h,t],ref:E},g,{buffer:e||S.texture,_transmission:f,transmission:t?f:0,thickness:d,side:c}))})},319:function(e,t,n){"use strict";n.d(t,{R:function(){return l}});var r=n(7294),a=n(9477),i=n(230);function l(e,t,n){let l=(0,i.z)(e=>e.size),o=(0,i.z)(e=>e.viewport),s="number"==typeof e?e:l.width*o.dpr,u="number"==typeof t?t:l.height*o.dpr,{samples:c=0,depth:f,...d}=("number"==typeof e?n:e)||{},p=r.useMemo(()=>{let e=new a.WebGLRenderTarget(s,u,{minFilter:a.LinearFilter,magFilter:a.LinearFilter,type:a.HalfFloatType,...d});return f&&(e.depthTexture=new a.DepthTexture(s,u,a.FloatType)),e.samples=c,e},[]);return r.useLayoutEffect(()=>{p.setSize(s,u),c&&(p.samples=c)},[c,p,s,u]),r.useEffect(()=>()=>p.dispose(),[]),p}},8626:function(e,t,n){"use strict";let r;n.d(t,{L:function(){return et}});var a=n(9477);let i=new WeakMap;class l extends a.Loader{constructor(e){super(e),this.decoderPath="",this.decoderConfig={},this.decoderBinary=null,this.decoderPending=null,this.workerLimit=4,this.workerPool=[],this.workerNextTaskID=1,this.workerSourceURL="",this.defaultAttributeIDs={position:"POSITION",normal:"NORMAL",color:"COLOR",uv:"TEX_COORD"},this.defaultAttributeTypes={position:"Float32Array",normal:"Float32Array",color:"Float32Array",uv:"Float32Array"}}setDecoderPath(e){return this.decoderPath=e,this}setDecoderConfig(e){return this.decoderConfig=e,this}setWorkerLimit(e){return this.workerLimit=e,this}load(e,t,n,r){let i=new a.FileLoader(this.manager);i.setPath(this.path),i.setResponseType("arraybuffer"),i.setRequestHeader(this.requestHeader),i.setWithCredentials(this.withCredentials),i.load(e,e=>{let n={attributeIDs:this.defaultAttributeIDs,attributeTypes:this.defaultAttributeTypes,useUniqueIDs:!1};this.decodeGeometry(e,n).then(t).catch(r)},n,r)}decodeDracoFile(e,t,n,r){let a={attributeIDs:n||this.defaultAttributeIDs,attributeTypes:r||this.defaultAttributeTypes,useUniqueIDs:!!n};this.decodeGeometry(e,a).then(t)}decodeGeometry(e,t){let n;for(let e in t.attributeTypes){let n=t.attributeTypes[e];void 0!==n.BYTES_PER_ELEMENT&&(t.attributeTypes[e]=n.name)}let r=JSON.stringify(t);if(i.has(e)){let t=i.get(e);if(t.key===r)return t.promise;if(0===e.byteLength)throw Error("THREE.DRACOLoader: Unable to re-decode a buffer with different settings. Buffer has already been transferred.")}let a=this.workerNextTaskID++,l=e.byteLength,o=this._getWorker(a,l).then(r=>(n=r,new Promise((r,i)=>{n._callbacks[a]={resolve:r,reject:i},n.postMessage({type:"decode",id:a,taskConfig:t,buffer:e},[e])}))).then(e=>this._createGeometry(e.geometry));return o.catch(()=>!0).then(()=>{n&&a&&this._releaseTask(n,a)}),i.set(e,{key:r,promise:o}),o}_createGeometry(e){let t=new a.BufferGeometry;e.index&&t.setIndex(new a.BufferAttribute(e.index.array,1));for(let n=0;n<e.attributes.length;n++){let r=e.attributes[n],i=r.name,l=r.array,o=r.itemSize;t.setAttribute(i,new a.BufferAttribute(l,o))}return t}_loadLibrary(e,t){let n=new a.FileLoader(this.manager);return n.setPath(this.decoderPath),n.setResponseType(t),n.setWithCredentials(this.withCredentials),new Promise((t,r)=>{n.load(e,t,void 0,r)})}preload(){return this._initDecoder(),this}_initDecoder(){if(this.decoderPending)return this.decoderPending;let e="object"!=typeof WebAssembly||"js"===this.decoderConfig.type,t=[];return e?t.push(this._loadLibrary("draco_decoder.js","text")):(t.push(this._loadLibrary("draco_wasm_wrapper.js","text")),t.push(this._loadLibrary("draco_decoder.wasm","arraybuffer"))),this.decoderPending=Promise.all(t).then(t=>{let n=t[0];e||(this.decoderConfig.wasmBinary=t[1]);let r=o.toString(),a=["/* draco decoder */",n,"","/* worker */",r.substring(r.indexOf("{")+1,r.lastIndexOf("}"))].join("\n");this.workerSourceURL=URL.createObjectURL(new Blob([a]))}),this.decoderPending}_getWorker(e,t){return this._initDecoder().then(()=>{if(this.workerPool.length<this.workerLimit){let e=new Worker(this.workerSourceURL);e._callbacks={},e._taskCosts={},e._taskLoad=0,e.postMessage({type:"init",decoderConfig:this.decoderConfig}),e.onmessage=function(t){let n=t.data;switch(n.type){case"decode":e._callbacks[n.id].resolve(n);break;case"error":e._callbacks[n.id].reject(n);break;default:console.error('THREE.DRACOLoader: Unexpected message, "'+n.type+'"')}},this.workerPool.push(e)}else this.workerPool.sort(function(e,t){return e._taskLoad>t._taskLoad?-1:1});let n=this.workerPool[this.workerPool.length-1];return n._taskCosts[e]=t,n._taskLoad+=t,n})}_releaseTask(e,t){e._taskLoad-=e._taskCosts[t],delete e._callbacks[t],delete e._taskCosts[t]}debug(){console.log("Task load: ",this.workerPool.map(e=>e._taskLoad))}dispose(){for(let e=0;e<this.workerPool.length;++e)this.workerPool[e].terminate();return this.workerPool.length=0,this}}function o(){let e,t;onmessage=function(n){let r=n.data;switch(r.type){case"init":e=r.decoderConfig,t=new Promise(function(t){e.onModuleLoaded=function(e){t({draco:e})},DracoDecoderModule(e)});break;case"decode":let a=r.buffer,i=r.taskConfig;t.then(e=>{let t=e.draco,n=new t.Decoder,l=new t.DecoderBuffer;l.Init(new Int8Array(a),a.byteLength);try{let e=function(e,t,n,r){let a,i;let l=r.attributeIDs,o=r.attributeTypes,s=t.GetEncodedGeometryType(n);if(s===e.TRIANGULAR_MESH)a=new e.Mesh,i=t.DecodeBufferToMesh(n,a);else if(s===e.POINT_CLOUD)a=new e.PointCloud,i=t.DecodeBufferToPointCloud(n,a);else throw Error("THREE.DRACOLoader: Unexpected geometry type.");if(!i.ok()||0===a.ptr)throw Error("THREE.DRACOLoader: Decoding failed: "+i.error_msg());let u={index:null,attributes:[]};for(let n in l){let i,s;let c=self[o[n]];if(r.useUniqueIDs)s=l[n],i=t.GetAttributeByUniqueId(a,s);else{if(-1===(s=t.GetAttributeId(a,e[l[n]])))continue;i=t.GetAttribute(a,s)}u.attributes.push(function(e,t,n,r,a,i){let l=i.num_components(),o=n.num_points(),s=o*l,u=s*a.BYTES_PER_ELEMENT,c=function(e,t){switch(t){case Float32Array:return e.DT_FLOAT32;case Int8Array:return e.DT_INT8;case Int16Array:return e.DT_INT16;case Int32Array:return e.DT_INT32;case Uint8Array:return e.DT_UINT8;case Uint16Array:return e.DT_UINT16;case Uint32Array:return e.DT_UINT32}}(e,a),f=e._malloc(u);t.GetAttributeDataArrayForAllPoints(n,i,c,u,f);let d=new a(e.HEAPF32.buffer,f,s).slice();return e._free(f),{name:r,array:d,itemSize:l}}(e,t,a,n,c,i))}return s===e.TRIANGULAR_MESH&&(u.index=function(e,t,n){let r=n.num_faces(),a=3*r,i=4*a,l=e._malloc(i);t.GetTrianglesUInt32Array(n,i,l);let o=new Uint32Array(e.HEAPF32.buffer,l,a).slice();return e._free(l),{array:o,itemSize:1}}(e,t,a)),e.destroy(a),u}(t,n,l,i),a=e.attributes.map(e=>e.array.buffer);e.index&&a.push(e.index.array.buffer),self.postMessage({type:"decode",id:r.id,geometry:e},a)}catch(e){console.error(e),self.postMessage({type:"error",id:r.id,error:e.message})}finally{t.destroy(l),t.destroy(n)}})}}}let s=()=>{let e;if(r)return r;let t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),n=new Uint8Array([32,0,65,253,3,1,2,34,4,106,6,5,11,8,7,20,13,33,12,16,128,9,116,64,19,113,127,15,10,21,22,14,255,66,24,54,136,107,18,23,192,26,114,118,132,17,77,101,130,144,27,87,131,44,45,74,156,154,70,167]);if("object"!=typeof WebAssembly)return{supported:!1};let a="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";WebAssembly.validate(t)&&(a="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");let i=WebAssembly.instantiate(function(e){let t=new Uint8Array(e.length);for(let n=0;n<e.length;++n){let r=e.charCodeAt(n);t[n]=r>96?r-71:r>64?r-65:r>47?r+4:r>46?63:62}let r=0;for(let a=0;a<e.length;++a)t[r++]=t[a]<60?n[t[a]]:(t[a]-60)*64+t[++a];return t.buffer.slice(0,r)}(a),{}).then(t=>{(e=t.instance).exports.__wasm_call_ctors()});function l(t,n,r,a,i,l){let o=e.exports.sbrk,s=r+3&-4,u=o(s*a),c=o(i.length),f=new Uint8Array(e.exports.memory.buffer);f.set(i,c);let d=t(u,r,a,c,i.length);if(0===d&&l&&l(u,s,a),n.set(f.subarray(u,u+r*a)),o(u-o(0)),0!==d)throw Error(`Malformed buffer data: ${d}`)}let o={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},s={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return r={ready:i,supported:!0,decodeVertexBuffer(t,n,r,a,i){l(e.exports.meshopt_decodeVertexBuffer,t,n,r,a,e.exports[o[i]])},decodeIndexBuffer(t,n,r,a){l(e.exports.meshopt_decodeIndexBuffer,t,n,r,a)},decodeIndexSequence(t,n,r,a){l(e.exports.meshopt_decodeIndexSequence,t,n,r,a)},decodeGltfBuffer(t,n,r,a,i,u){l(e.exports[s[i]],t,n,r,a,e.exports[o[u]])}}};class u extends a.Loader{constructor(e){super(e),this.dracoLoader=null,this.ktx2Loader=null,this.meshoptDecoder=null,this.pluginCallbacks=[],this.register(function(e){return new m(e)}),this.register(function(e){return new y(e)}),this.register(function(e){return new E(e)}),this.register(function(e){return new B(e)}),this.register(function(e){return new g(e)}),this.register(function(e){return new C(e)}),this.register(function(e){return new v(e)}),this.register(function(e){return new h(e)}),this.register(function(e){return new b(e)}),this.register(function(e){return new A(e)}),this.register(function(e){return new d(e)}),this.register(function(e){return new M(e)})}load(e,t,n,r){let i;let l=this;i=""!==this.resourcePath?this.resourcePath:""!==this.path?this.path:a.LoaderUtils.extractUrlBase(e),this.manager.itemStart(e);let o=function(t){r?r(t):console.error(t),l.manager.itemError(e),l.manager.itemEnd(e)},s=new a.FileLoader(this.manager);s.setPath(this.path),s.setResponseType("arraybuffer"),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials),s.load(e,function(n){try{l.parse(n,i,function(n){t(n),l.manager.itemEnd(e)},o)}catch(e){o(e)}},n,o)}setDRACOLoader(e){return this.dracoLoader=e,this}setDDSLoader(){throw Error('THREE.GLTFLoader: "MSFT_texture_dds" no longer supported. Please update to "KHR_texture_basisu".')}setKTX2Loader(e){return this.ktx2Loader=e,this}setMeshoptDecoder(e){return this.meshoptDecoder=e,this}register(e){return -1===this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.push(e),this}unregister(e){return -1!==this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(e),1),this}parse(e,t,n,r){let i;let l={},o={};if("string"==typeof e)i=e;else{let t=a.LoaderUtils.decodeText(new Uint8Array(e,0,4));if(t===F){try{l[f.KHR_BINARY_GLTF]=new I(e)}catch(e){r&&r(e);return}i=l[f.KHR_BINARY_GLTF].content}else i=a.LoaderUtils.decodeText(new Uint8Array(e))}let s=JSON.parse(i);if(void 0===s.asset||s.asset.version[0]<2){r&&r(Error("THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported."));return}let u=new W(s,{path:t||this.resourcePath||"",crossOrigin:this.crossOrigin,requestHeader:this.requestHeader,manager:this.manager,ktx2Loader:this.ktx2Loader,meshoptDecoder:this.meshoptDecoder});u.fileLoader.setRequestHeader(this.requestHeader);for(let e=0;e<this.pluginCallbacks.length;e++){let t=this.pluginCallbacks[e](u);o[t.name]=t,l[t.name]=!0}if(s.extensionsUsed)for(let e=0;e<s.extensionsUsed.length;++e){let t=s.extensionsUsed[e],n=s.extensionsRequired||[];switch(t){case f.KHR_MATERIALS_UNLIT:l[t]=new p;break;case f.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS:l[t]=new D;break;case f.KHR_DRACO_MESH_COMPRESSION:l[t]=new R(s,this.dracoLoader);break;case f.KHR_TEXTURE_TRANSFORM:l[t]=new x;break;case f.KHR_MESH_QUANTIZATION:l[t]=new G;break;default:n.indexOf(t)>=0&&void 0===o[t]&&console.warn('THREE.GLTFLoader: Unknown extension "'+t+'".')}}u.setExtensions(l),u.setPlugins(o),u.parse(n,r)}parseAsync(e,t){let n=this;return new Promise(function(r,a){n.parse(e,t,r,a)})}}function c(){let e={};return{get:function(t){return e[t]},add:function(t,n){e[t]=n},remove:function(t){delete e[t]},removeAll:function(){e={}}}}let f={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_CLEARCOAT:"KHR_materials_clearcoat",KHR_MATERIALS_IOR:"KHR_materials_ior",KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS:"KHR_materials_pbrSpecularGlossiness",KHR_MATERIALS_SHEEN:"KHR_materials_sheen",KHR_MATERIALS_SPECULAR:"KHR_materials_specular",KHR_MATERIALS_TRANSMISSION:"KHR_materials_transmission",KHR_MATERIALS_IRIDESCENCE:"KHR_materials_iridescence",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_MATERIALS_VOLUME:"KHR_materials_volume",KHR_TEXTURE_BASISU:"KHR_texture_basisu",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",KHR_MESH_QUANTIZATION:"KHR_mesh_quantization",KHR_MATERIALS_EMISSIVE_STRENGTH:"KHR_materials_emissive_strength",EXT_TEXTURE_WEBP:"EXT_texture_webp",EXT_MESHOPT_COMPRESSION:"EXT_meshopt_compression"};class d{constructor(e){this.parser=e,this.name=f.KHR_LIGHTS_PUNCTUAL,this.cache={refs:{},uses:{}}}_markDefs(){let e=this.parser,t=this.parser.json.nodes||[];for(let n=0,r=t.length;n<r;n++){let r=t[n];r.extensions&&r.extensions[this.name]&&void 0!==r.extensions[this.name].light&&e._addNodeRef(this.cache,r.extensions[this.name].light)}}_loadLight(e){let t;let n=this.parser,r="light:"+e,i=n.cache.get(r);if(i)return i;let l=n.json,o=l.extensions&&l.extensions[this.name]||{},s=o.lights||[],u=s[e],c=new a.Color(16777215);void 0!==u.color&&c.fromArray(u.color);let f=void 0!==u.range?u.range:0;switch(u.type){case"directional":(t=new a.DirectionalLight(c)).target.position.set(0,0,-1),t.add(t.target);break;case"point":(t=new a.PointLight(c)).distance=f;break;case"spot":(t=new a.SpotLight(c)).distance=f,u.spot=u.spot||{},u.spot.innerConeAngle=void 0!==u.spot.innerConeAngle?u.spot.innerConeAngle:0,u.spot.outerConeAngle=void 0!==u.spot.outerConeAngle?u.spot.outerConeAngle:Math.PI/4,t.angle=u.spot.outerConeAngle,t.penumbra=1-u.spot.innerConeAngle/u.spot.outerConeAngle,t.target.position.set(0,0,-1),t.add(t.target);break;default:throw Error("THREE.GLTFLoader: Unexpected light type: "+u.type)}return t.position.set(0,0,0),t.decay=2,void 0!==u.intensity&&(t.intensity=u.intensity),t.name=n.createUniqueName(u.name||"light_"+e),i=Promise.resolve(t),n.cache.add(r,i),i}createNodeAttachment(e){let t=this,n=this.parser,r=n.json,a=r.nodes[e],i=a.extensions&&a.extensions[this.name]||{},l=i.light;return void 0===l?null:this._loadLight(l).then(function(e){return n._getNodeRef(t.cache,l,e)})}}class p{constructor(){this.name=f.KHR_MATERIALS_UNLIT}getMaterialType(){return a.MeshBasicMaterial}extendParams(e,t,n){let r=[];e.color=new a.Color(1,1,1),e.opacity=1;let i=t.pbrMetallicRoughness;if(i){if(Array.isArray(i.baseColorFactor)){let t=i.baseColorFactor;e.color.fromArray(t),e.opacity=t[3]}void 0!==i.baseColorTexture&&r.push(n.assignTexture(e,"map",i.baseColorTexture,a.sRGBEncoding))}return Promise.all(r)}}class h{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_EMISSIVE_STRENGTH}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let a=r.extensions[this.name].emissiveStrength;return void 0!==a&&(t.emissiveIntensity=a),Promise.resolve()}}class m{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_CLEARCOAT}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let i=[],l=r.extensions[this.name];if(void 0!==l.clearcoatFactor&&(t.clearcoat=l.clearcoatFactor),void 0!==l.clearcoatTexture&&i.push(n.assignTexture(t,"clearcoatMap",l.clearcoatTexture)),void 0!==l.clearcoatRoughnessFactor&&(t.clearcoatRoughness=l.clearcoatRoughnessFactor),void 0!==l.clearcoatRoughnessTexture&&i.push(n.assignTexture(t,"clearcoatRoughnessMap",l.clearcoatRoughnessTexture)),void 0!==l.clearcoatNormalTexture&&(i.push(n.assignTexture(t,"clearcoatNormalMap",l.clearcoatNormalTexture)),void 0!==l.clearcoatNormalTexture.scale)){let e=l.clearcoatNormalTexture.scale;t.clearcoatNormalScale=new a.Vector2(e,e)}return Promise.all(i)}}class A{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_IRIDESCENCE}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let a=[],i=r.extensions[this.name];return void 0!==i.iridescenceFactor&&(t.iridescence=i.iridescenceFactor),void 0!==i.iridescenceTexture&&a.push(n.assignTexture(t,"iridescenceMap",i.iridescenceTexture)),void 0!==i.iridescenceIor&&(t.iridescenceIOR=i.iridescenceIor),void 0===t.iridescenceThicknessRange&&(t.iridescenceThicknessRange=[100,400]),void 0!==i.iridescenceThicknessMinimum&&(t.iridescenceThicknessRange[0]=i.iridescenceThicknessMinimum),void 0!==i.iridescenceThicknessMaximum&&(t.iridescenceThicknessRange[1]=i.iridescenceThicknessMaximum),void 0!==i.iridescenceThicknessTexture&&a.push(n.assignTexture(t,"iridescenceThicknessMap",i.iridescenceThicknessTexture)),Promise.all(a)}}class B{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_SHEEN}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let i=[];t.sheenColor=new a.Color(0,0,0),t.sheenRoughness=0,t.sheen=1;let l=r.extensions[this.name];return void 0!==l.sheenColorFactor&&t.sheenColor.fromArray(l.sheenColorFactor),void 0!==l.sheenRoughnessFactor&&(t.sheenRoughness=l.sheenRoughnessFactor),void 0!==l.sheenColorTexture&&i.push(n.assignTexture(t,"sheenColorMap",l.sheenColorTexture,a.sRGBEncoding)),void 0!==l.sheenRoughnessTexture&&i.push(n.assignTexture(t,"sheenRoughnessMap",l.sheenRoughnessTexture)),Promise.all(i)}}class g{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_TRANSMISSION}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let a=[],i=r.extensions[this.name];return void 0!==i.transmissionFactor&&(t.transmission=i.transmissionFactor),void 0!==i.transmissionTexture&&a.push(n.assignTexture(t,"transmissionMap",i.transmissionTexture)),Promise.all(a)}}class C{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_VOLUME}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let i=[],l=r.extensions[this.name];t.thickness=void 0!==l.thicknessFactor?l.thicknessFactor:0,void 0!==l.thicknessTexture&&i.push(n.assignTexture(t,"thicknessMap",l.thicknessTexture)),t.attenuationDistance=l.attenuationDistance||1/0;let o=l.attenuationColor||[1,1,1];return t.attenuationColor=new a.Color(o[0],o[1],o[2]),Promise.all(i)}}class v{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_IOR}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let a=r.extensions[this.name];return t.ior=void 0!==a.ior?a.ior:1.5,Promise.resolve()}}class b{constructor(e){this.parser=e,this.name=f.KHR_MATERIALS_SPECULAR}getMaterialType(e){let t=this.parser,n=t.json.materials[e];return n.extensions&&n.extensions[this.name]?a.MeshPhysicalMaterial:null}extendMaterialParams(e,t){let n=this.parser,r=n.json.materials[e];if(!r.extensions||!r.extensions[this.name])return Promise.resolve();let i=[],l=r.extensions[this.name];t.specularIntensity=void 0!==l.specularFactor?l.specularFactor:1,void 0!==l.specularTexture&&i.push(n.assignTexture(t,"specularIntensityMap",l.specularTexture));let o=l.specularColorFactor||[1,1,1];return t.specularColor=new a.Color(o[0],o[1],o[2]),void 0!==l.specularColorTexture&&i.push(n.assignTexture(t,"specularColorMap",l.specularColorTexture,a.sRGBEncoding)),Promise.all(i)}}class y{constructor(e){this.parser=e,this.name=f.KHR_TEXTURE_BASISU}loadTexture(e){let t=this.parser,n=t.json,r=n.textures[e];if(!r.extensions||!r.extensions[this.name])return null;let a=r.extensions[this.name],i=t.options.ktx2Loader;if(!i){if(!(n.extensionsRequired&&n.extensionsRequired.indexOf(this.name)>=0))return null;throw Error("THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures")}return t.loadTextureImage(e,a.source,i)}}class E{constructor(e){this.parser=e,this.name=f.EXT_TEXTURE_WEBP,this.isSupported=null}loadTexture(e){let t=this.name,n=this.parser,r=n.json,a=r.textures[e];if(!a.extensions||!a.extensions[t])return null;let i=a.extensions[t],l=r.images[i.source],o=n.textureLoader;if(l.uri){let e=n.options.manager.getHandler(l.uri);null!==e&&(o=e)}return this.detectSupport().then(function(a){if(a)return n.loadTextureImage(e,i.source,o);if(r.extensionsRequired&&r.extensionsRequired.indexOf(t)>=0)throw Error("THREE.GLTFLoader: WebP required by asset but unsupported.");return n.loadTexture(e)})}detectSupport(){return this.isSupported||(this.isSupported=new Promise(function(e){let t=new Image;t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",t.onload=t.onerror=function(){e(1===t.height)}})),this.isSupported}}class M{constructor(e){this.name=f.EXT_MESHOPT_COMPRESSION,this.parser=e}loadBufferView(e){let t=this.parser.json,n=t.bufferViews[e];if(!n.extensions||!n.extensions[this.name])return null;{let e=n.extensions[this.name],r=this.parser.getDependency("buffer",e.buffer),a=this.parser.options.meshoptDecoder;if(!a||!a.supported){if(!(t.extensionsRequired&&t.extensionsRequired.indexOf(this.name)>=0))return null;throw Error("THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files")}return r.then(function(t){let n=e.byteOffset||0,r=e.byteLength||0,i=e.count,l=e.byteStride,o=new Uint8Array(t,n,r);return a.decodeGltfBufferAsync?a.decodeGltfBufferAsync(i,l,o,e.mode,e.filter).then(function(e){return e.buffer}):a.ready.then(function(){let t=new ArrayBuffer(i*l);return a.decodeGltfBuffer(new Uint8Array(t),i,l,o,e.mode,e.filter),t})})}}}let F="glTF",S={JSON:1313821514,BIN:5130562};class I{constructor(e){this.name=f.KHR_BINARY_GLTF,this.content=null,this.body=null;let t=new DataView(e,0,12);if(this.header={magic:a.LoaderUtils.decodeText(new Uint8Array(e.slice(0,4))),version:t.getUint32(4,!0),length:t.getUint32(8,!0)},this.header.magic!==F)throw Error("THREE.GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw Error("THREE.GLTFLoader: Legacy binary file detected.");let n=this.header.length-12,r=new DataView(e,12),i=0;for(;i<n;){let t=r.getUint32(i,!0);i+=4;let n=r.getUint32(i,!0);if(i+=4,n===S.JSON){let n=new Uint8Array(e,12+i,t);this.content=a.LoaderUtils.decodeText(n)}else if(n===S.BIN){let n=12+i;this.body=e.slice(n,n+t)}i+=t}if(null===this.content)throw Error("THREE.GLTFLoader: JSON content not found.")}}class R{constructor(e,t){if(!t)throw Error("THREE.GLTFLoader: No DRACOLoader instance provided.");this.name=f.KHR_DRACO_MESH_COMPRESSION,this.json=e,this.dracoLoader=t,this.dracoLoader.preload()}decodePrimitive(e,t){let n=this.json,r=this.dracoLoader,a=e.extensions[this.name].bufferView,i=e.extensions[this.name].attributes,l={},o={},s={};for(let e in i){let t=U[e]||e.toLowerCase();l[t]=i[e]}for(let t in e.attributes){let r=U[t]||t.toLowerCase();if(void 0!==i[t]){let a=n.accessors[e.attributes[t]],i=L[a.componentType];s[r]=i.name,o[r]=!0===a.normalized}}return t.getDependency("bufferView",a).then(function(e){return new Promise(function(t){r.decodeDracoFile(e,function(e){for(let t in e.attributes){let n=e.attributes[t],r=o[t];void 0!==r&&(n.normalized=r)}t(e)},l,s)})})}}class x{constructor(){this.name=f.KHR_TEXTURE_TRANSFORM}extendTexture(e,t){return void 0!==t.texCoord&&console.warn('THREE.GLTFLoader: Custom UV sets in "'+this.name+'" extension not yet supported.'),void 0===t.offset&&void 0===t.rotation&&void 0===t.scale||(e=e.clone(),void 0!==t.offset&&e.offset.fromArray(t.offset),void 0!==t.rotation&&(e.rotation=t.rotation),void 0!==t.scale&&e.repeat.fromArray(t.scale),e.needsUpdate=!0),e}}class w extends a.MeshStandardMaterial{constructor(e){super(),this.isGLTFSpecularGlossinessMaterial=!0;let t={specular:{value:new a.Color().setHex(16777215)},glossiness:{value:1},specularMap:{value:null},glossinessMap:{value:null}};this._extraUniforms=t,this.onBeforeCompile=function(e){for(let n in t)e.uniforms[n]=t[n];e.fragmentShader=e.fragmentShader.replace("uniform float roughness;","uniform vec3 specular;").replace("uniform float metalness;","uniform float glossiness;").replace("#include <roughnessmap_pars_fragment>","#ifdef USE_SPECULARMAP\n	uniform sampler2D specularMap;\n#endif").replace("#include <metalnessmap_pars_fragment>","#ifdef USE_GLOSSINESSMAP\n	uniform sampler2D glossinessMap;\n#endif").replace("#include <roughnessmap_fragment>","vec3 specularFactor = specular;\n#ifdef USE_SPECULARMAP\n	vec4 texelSpecular = texture2D( specularMap, vUv );\n	// reads channel RGB, compatible with a glTF Specular-Glossiness (RGBA) texture\n	specularFactor *= texelSpecular.rgb;\n#endif").replace("#include <metalnessmap_fragment>","float glossinessFactor = glossiness;\n#ifdef USE_GLOSSINESSMAP\n	vec4 texelGlossiness = texture2D( glossinessMap, vUv );\n	// reads channel A, compatible with a glTF Specular-Glossiness (RGBA) texture\n	glossinessFactor *= texelGlossiness.a;\n#endif").replace("#include <lights_physical_fragment>","PhysicalMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb * ( 1. - max( specularFactor.r, max( specularFactor.g, specularFactor.b ) ) );\nvec3 dxy = max( abs( dFdx( geometryNormal ) ), abs( dFdy( geometryNormal ) ) );\nfloat geometryRoughness = max( max( dxy.x, dxy.y ), dxy.z );\nmaterial.roughness = max( 1.0 - glossinessFactor, 0.0525 ); // 0.0525 corresponds to the base mip of a 256 cubemap.\nmaterial.roughness += geometryRoughness;\nmaterial.roughness = min( material.roughness, 1.0 );\nmaterial.specularColor = specularFactor;")},Object.defineProperties(this,{specular:{get:function(){return t.specular.value},set:function(e){t.specular.value=e}},specularMap:{get:function(){return t.specularMap.value},set:function(e){t.specularMap.value=e,e?this.defines.USE_SPECULARMAP="":delete this.defines.USE_SPECULARMAP}},glossiness:{get:function(){return t.glossiness.value},set:function(e){t.glossiness.value=e}},glossinessMap:{get:function(){return t.glossinessMap.value},set:function(e){t.glossinessMap.value=e,e?(this.defines.USE_GLOSSINESSMAP="",this.defines.USE_UV=""):(delete this.defines.USE_GLOSSINESSMAP,delete this.defines.USE_UV)}}}),delete this.metalness,delete this.roughness,delete this.metalnessMap,delete this.roughnessMap,this.setValues(e)}copy(e){return super.copy(e),this.specularMap=e.specularMap,this.specular.copy(e.specular),this.glossinessMap=e.glossinessMap,this.glossiness=e.glossiness,delete this.metalness,delete this.roughness,delete this.metalnessMap,delete this.roughnessMap,this}}class D{constructor(){this.name=f.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS,this.specularGlossinessParams=["color","map","lightMap","lightMapIntensity","aoMap","aoMapIntensity","emissive","emissiveIntensity","emissiveMap","bumpMap","bumpScale","normalMap","normalMapType","displacementMap","displacementScale","displacementBias","specularMap","specular","glossinessMap","glossiness","alphaMap","envMap","envMapIntensity","refractionRatio"]}getMaterialType(){return w}extendParams(e,t,n){let r=t.extensions[this.name];e.color=new a.Color(1,1,1),e.opacity=1;let i=[];if(Array.isArray(r.diffuseFactor)){let t=r.diffuseFactor;e.color.fromArray(t),e.opacity=t[3]}if(void 0!==r.diffuseTexture&&i.push(n.assignTexture(e,"map",r.diffuseTexture,a.sRGBEncoding)),e.emissive=new a.Color(0,0,0),e.glossiness=void 0!==r.glossinessFactor?r.glossinessFactor:1,e.specular=new a.Color(1,1,1),Array.isArray(r.specularFactor)&&e.specular.fromArray(r.specularFactor),void 0!==r.specularGlossinessTexture){let t=r.specularGlossinessTexture;i.push(n.assignTexture(e,"glossinessMap",t)),i.push(n.assignTexture(e,"specularMap",t,a.sRGBEncoding))}return Promise.all(i)}createMaterial(e){let t=new w(e);return t.fog=!0,t.color=e.color,t.map=void 0===e.map?null:e.map,t.lightMap=null,t.lightMapIntensity=1,t.aoMap=void 0===e.aoMap?null:e.aoMap,t.aoMapIntensity=1,t.emissive=e.emissive,t.emissiveIntensity=void 0===e.emissiveIntensity?1:e.emissiveIntensity,t.emissiveMap=void 0===e.emissiveMap?null:e.emissiveMap,t.bumpMap=void 0===e.bumpMap?null:e.bumpMap,t.bumpScale=1,t.normalMap=void 0===e.normalMap?null:e.normalMap,t.normalMapType=a.TangentSpaceNormalMap,e.normalScale&&(t.normalScale=e.normalScale),t.displacementMap=null,t.displacementScale=1,t.displacementBias=0,t.specularMap=void 0===e.specularMap?null:e.specularMap,t.specular=e.specular,t.glossinessMap=void 0===e.glossinessMap?null:e.glossinessMap,t.glossiness=e.glossiness,t.alphaMap=null,t.envMap=void 0===e.envMap?null:e.envMap,t.envMapIntensity=1,t.refractionRatio=.98,t}}class G{constructor(){this.name=f.KHR_MESH_QUANTIZATION}}class T extends a.Interpolant{constructor(e,t,n,r){super(e,t,n,r)}copySampleValue_(e){let t=this.resultBuffer,n=this.sampleValues,r=this.valueSize,a=e*r*3+r;for(let e=0;e!==r;e++)t[e]=n[a+e];return t}interpolate_(e,t,n,r){let a=this.resultBuffer,i=this.sampleValues,l=this.valueSize,o=2*l,s=3*l,u=r-t,c=(n-t)/u,f=c*c,d=f*c,p=e*s,h=p-s,m=-2*d+3*f,A=d-f,B=1-m,g=A-f+c;for(let e=0;e!==l;e++){let t=i[h+e+l],n=i[h+e+o]*u,r=i[p+e+l],s=i[p+e]*u;a[e]=B*t+g*n+m*r+A*s}return a}}let _=new a.Quaternion;class H extends T{interpolate_(e,t,n,r){let a=super.interpolate_(e,t,n,r);return _.fromArray(a).normalize().toArray(a),a}}let P={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123},L={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},k={9728:a.NearestFilter,9729:a.LinearFilter,9984:a.NearestMipmapNearestFilter,9985:a.LinearMipmapNearestFilter,9986:a.NearestMipmapLinearFilter,9987:a.LinearMipmapLinearFilter},J={33071:a.ClampToEdgeWrapping,33648:a.MirroredRepeatWrapping,10497:a.RepeatWrapping},O={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},U={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",TEXCOORD_0:"uv",TEXCOORD_1:"uv2",COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},N={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},K={CUBICSPLINE:void 0,LINEAR:a.InterpolateLinear,STEP:a.InterpolateDiscrete},Q={OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"};function z(e,t,n){for(let r in n.extensions)void 0===e[r]&&(t.userData.gltfExtensions=t.userData.gltfExtensions||{},t.userData.gltfExtensions[r]=n.extensions[r])}function j(e,t){void 0!==t.extras&&("object"==typeof t.extras?Object.assign(e.userData,t.extras):console.warn("THREE.GLTFLoader: Ignoring primitive type .extras, "+t.extras))}function X(e){let t="",n=Object.keys(e).sort();for(let r=0,a=n.length;r<a;r++)t+=n[r]+":"+e[n[r]]+";";return t}function Y(e){switch(e){case Int8Array:return 1/127;case Uint8Array:return 1/255;case Int16Array:return 1/32767;case Uint16Array:return 1/65535;default:throw Error("THREE.GLTFLoader: Unsupported normalized accessor component type.")}}class W{constructor(e={},t={}){var n,r;this.json=e,this.extensions={},this.plugins={},this.options=t,this.cache=new c,this.associations=new Map,this.primitiveCache={},this.meshCache={refs:{},uses:{}},this.cameraCache={refs:{},uses:{}},this.lightCache={refs:{},uses:{}},this.sourceCache={},this.textureCache={},this.nodeNamesUsed={};let i="undefined"!=typeof navigator&&!0===/^((?!chrome|android).)*safari/i.test(navigator.userAgent),l="undefined"!=typeof navigator&&(null===(n=navigator.userAgent)||void 0===n?void 0:n.indexOf("Firefox"))>-1,o="undefined"!=typeof navigator&&l?null===(r=navigator.userAgent)||void 0===r?void 0:r.match(/Firefox\/([0-9]+)\./)[1]:-1;"undefined"==typeof createImageBitmap||i||l&&o<98?this.textureLoader=new a.TextureLoader(this.options.manager):this.textureLoader=new a.ImageBitmapLoader(this.options.manager),this.textureLoader.setCrossOrigin(this.options.crossOrigin),this.textureLoader.setRequestHeader(this.options.requestHeader),this.fileLoader=new a.FileLoader(this.options.manager),this.fileLoader.setResponseType("arraybuffer"),"use-credentials"===this.options.crossOrigin&&this.fileLoader.setWithCredentials(!0)}setExtensions(e){this.extensions=e}setPlugins(e){this.plugins=e}parse(e,t){let n=this,r=this.json,a=this.extensions;this.cache.removeAll(),this._invokeAll(function(e){return e._markDefs&&e._markDefs()}),Promise.all(this._invokeAll(function(e){return e.beforeRoot&&e.beforeRoot()})).then(function(){return Promise.all([n.getDependencies("scene"),n.getDependencies("animation"),n.getDependencies("camera")])}).then(function(t){let i={scene:t[0][r.scene||0],scenes:t[0],animations:t[1],cameras:t[2],asset:r.asset,parser:n,userData:{}};z(a,i,r),j(i,r),Promise.all(n._invokeAll(function(e){return e.afterRoot&&e.afterRoot(i)})).then(function(){e(i)})}).catch(t)}_markDefs(){let e=this.json.nodes||[],t=this.json.skins||[],n=this.json.meshes||[];for(let n=0,r=t.length;n<r;n++){let r=t[n].joints;for(let t=0,n=r.length;t<n;t++)e[r[t]].isBone=!0}for(let t=0,r=e.length;t<r;t++){let r=e[t];void 0!==r.mesh&&(this._addNodeRef(this.meshCache,r.mesh),void 0!==r.skin&&(n[r.mesh].isSkinnedMesh=!0)),void 0!==r.camera&&this._addNodeRef(this.cameraCache,r.camera)}}_addNodeRef(e,t){void 0!==t&&(void 0===e.refs[t]&&(e.refs[t]=e.uses[t]=0),e.refs[t]++)}_getNodeRef(e,t,n){if(e.refs[t]<=1)return n;let r=n.clone(),a=(e,t)=>{let n=this.associations.get(e);for(let[r,i]of(null!=n&&this.associations.set(t,n),e.children.entries()))a(i,t.children[r])};return a(n,r),r.name+="_instance_"+e.uses[t]++,r}_invokeOne(e){let t=Object.values(this.plugins);t.push(this);for(let n=0;n<t.length;n++){let r=e(t[n]);if(r)return r}return null}_invokeAll(e){let t=Object.values(this.plugins);t.unshift(this);let n=[];for(let r=0;r<t.length;r++){let a=e(t[r]);a&&n.push(a)}return n}getDependency(e,t){let n=e+":"+t,r=this.cache.get(n);if(!r){switch(e){case"scene":r=this.loadScene(t);break;case"node":r=this.loadNode(t);break;case"mesh":r=this._invokeOne(function(e){return e.loadMesh&&e.loadMesh(t)});break;case"accessor":r=this.loadAccessor(t);break;case"bufferView":r=this._invokeOne(function(e){return e.loadBufferView&&e.loadBufferView(t)});break;case"buffer":r=this.loadBuffer(t);break;case"material":r=this._invokeOne(function(e){return e.loadMaterial&&e.loadMaterial(t)});break;case"texture":r=this._invokeOne(function(e){return e.loadTexture&&e.loadTexture(t)});break;case"skin":r=this.loadSkin(t);break;case"animation":r=this._invokeOne(function(e){return e.loadAnimation&&e.loadAnimation(t)});break;case"camera":r=this.loadCamera(t);break;default:throw Error("Unknown type: "+e)}this.cache.add(n,r)}return r}getDependencies(e){let t=this.cache.get(e);if(!t){let n=this,r=this.json[e+("mesh"===e?"es":"s")]||[];t=Promise.all(r.map(function(t,r){return n.getDependency(e,r)})),this.cache.add(e,t)}return t}loadBuffer(e){let t=this.json.buffers[e],n=this.fileLoader;if(t.type&&"arraybuffer"!==t.type)throw Error("THREE.GLTFLoader: "+t.type+" buffer type is not supported.");if(void 0===t.uri&&0===e)return Promise.resolve(this.extensions[f.KHR_BINARY_GLTF].body);let r=this.options;return new Promise(function(e,i){n.load(a.LoaderUtils.resolveURL(t.uri,r.path),e,void 0,function(){i(Error('THREE.GLTFLoader: Failed to load buffer "'+t.uri+'".'))})})}loadBufferView(e){let t=this.json.bufferViews[e];return this.getDependency("buffer",t.buffer).then(function(e){let n=t.byteLength||0,r=t.byteOffset||0;return e.slice(r,r+n)})}loadAccessor(e){let t=this,n=this.json,r=this.json.accessors[e];if(void 0===r.bufferView&&void 0===r.sparse)return Promise.resolve(null);let i=[];return void 0!==r.bufferView?i.push(this.getDependency("bufferView",r.bufferView)):i.push(null),void 0!==r.sparse&&(i.push(this.getDependency("bufferView",r.sparse.indices.bufferView)),i.push(this.getDependency("bufferView",r.sparse.values.bufferView))),Promise.all(i).then(function(e){let i,l;let o=e[0],s=O[r.type],u=L[r.componentType],c=u.BYTES_PER_ELEMENT,f=r.byteOffset||0,d=void 0!==r.bufferView?n.bufferViews[r.bufferView].byteStride:void 0,p=!0===r.normalized;if(d&&d!==c*s){let e=Math.floor(f/d),n="InterleavedBuffer:"+r.bufferView+":"+r.componentType+":"+e+":"+r.count,h=t.cache.get(n);h||(i=new u(o,e*d,r.count*d/c),h=new a.InterleavedBuffer(i,d/c),t.cache.add(n,h)),l=new a.InterleavedBufferAttribute(h,s,f%d/c,p)}else i=null===o?new u(r.count*s):new u(o,f,r.count*s),l=new a.BufferAttribute(i,s,p);if(void 0!==r.sparse){let t=O.SCALAR,n=L[r.sparse.indices.componentType],i=r.sparse.indices.byteOffset||0,c=r.sparse.values.byteOffset||0,f=new n(e[1],i,r.sparse.count*t),d=new u(e[2],c,r.sparse.count*s);null!==o&&(l=new a.BufferAttribute(l.array.slice(),l.itemSize,l.normalized));for(let e=0,t=f.length;e<t;e++){let t=f[e];if(l.setX(t,d[e*s]),s>=2&&l.setY(t,d[e*s+1]),s>=3&&l.setZ(t,d[e*s+2]),s>=4&&l.setW(t,d[e*s+3]),s>=5)throw Error("THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}}return l})}loadTexture(e){let t=this.json,n=this.options,r=t.textures[e],a=r.source,i=t.images[a],l=this.textureLoader;if(i.uri){let e=n.manager.getHandler(i.uri);null!==e&&(l=e)}return this.loadTextureImage(e,a,l)}loadTextureImage(e,t,n){let r=this,i=this.json,l=i.textures[e],o=i.images[t],s=(o.uri||o.bufferView)+":"+l.sampler;if(this.textureCache[s])return this.textureCache[s];let u=this.loadImageSource(t,n).then(function(t){t.flipY=!1,l.name&&(t.name=l.name);let n=i.samplers||{},o=n[l.sampler]||{};return t.magFilter=k[o.magFilter]||a.LinearFilter,t.minFilter=k[o.minFilter]||a.LinearMipmapLinearFilter,t.wrapS=J[o.wrapS]||a.RepeatWrapping,t.wrapT=J[o.wrapT]||a.RepeatWrapping,r.associations.set(t,{textures:e}),t}).catch(function(){return null});return this.textureCache[s]=u,u}loadImageSource(e,t){let n=this.json,r=this.options;if(void 0!==this.sourceCache[e])return this.sourceCache[e].then(e=>e.clone());let i=n.images[e],l=self.URL||self.webkitURL,o=i.uri||"",s=!1;if(void 0!==i.bufferView)o=this.getDependency("bufferView",i.bufferView).then(function(e){s=!0;let t=new Blob([e],{type:i.mimeType});return o=l.createObjectURL(t)});else if(void 0===i.uri)throw Error("THREE.GLTFLoader: Image "+e+" is missing URI and bufferView");let u=Promise.resolve(o).then(function(e){return new Promise(function(n,i){let l=n;!0===t.isImageBitmapLoader&&(l=function(e){let t=new a.Texture(e);t.needsUpdate=!0,n(t)}),t.load(a.LoaderUtils.resolveURL(e,r.path),l,void 0,i)})}).then(function(e){var t;return!0===s&&l.revokeObjectURL(o),e.userData.mimeType=i.mimeType||((t=i.uri).search(/\.jpe?g($|\?)/i)>0||0===t.search(/^data\:image\/jpeg/)?"image/jpeg":t.search(/\.webp($|\?)/i)>0||0===t.search(/^data\:image\/webp/)?"image/webp":"image/png"),e}).catch(function(e){throw console.error("THREE.GLTFLoader: Couldn't load texture",o),e});return this.sourceCache[e]=u,u}assignTexture(e,t,n,r){let a=this;return this.getDependency("texture",n.index).then(function(i){if(void 0===n.texCoord||0==n.texCoord||"aoMap"===t&&1==n.texCoord||console.warn("THREE.GLTFLoader: Custom UV set "+n.texCoord+" for texture "+t+" not yet supported."),a.extensions[f.KHR_TEXTURE_TRANSFORM]){let e=void 0!==n.extensions?n.extensions[f.KHR_TEXTURE_TRANSFORM]:void 0;if(e){let t=a.associations.get(i);i=a.extensions[f.KHR_TEXTURE_TRANSFORM].extendTexture(i,e),a.associations.set(i,t)}}return void 0!==r&&(i.encoding=r),e[t]=i,i})}assignFinalMaterial(e){let t=e.geometry,n=e.material,r=void 0===t.attributes.tangent,i=void 0!==t.attributes.color,l=void 0===t.attributes.normal;if(e.isPoints){let e="PointsMaterial:"+n.uuid,t=this.cache.get(e);t||(t=new a.PointsMaterial,a.Material.prototype.copy.call(t,n),t.color.copy(n.color),t.map=n.map,t.sizeAttenuation=!1,this.cache.add(e,t)),n=t}else if(e.isLine){let e="LineBasicMaterial:"+n.uuid,t=this.cache.get(e);t||(t=new a.LineBasicMaterial,a.Material.prototype.copy.call(t,n),t.color.copy(n.color),this.cache.add(e,t)),n=t}if(r||i||l){let e="ClonedMaterial:"+n.uuid+":";n.isGLTFSpecularGlossinessMaterial&&(e+="specular-glossiness:"),r&&(e+="derivative-tangents:"),i&&(e+="vertex-colors:"),l&&(e+="flat-shading:");let t=this.cache.get(e);t||(t=n.clone(),i&&(t.vertexColors=!0),l&&(t.flatShading=!0),r&&(t.normalScale&&(t.normalScale.y*=-1),t.clearcoatNormalScale&&(t.clearcoatNormalScale.y*=-1)),this.cache.add(e,t),this.associations.set(t,this.associations.get(n))),n=t}n.aoMap&&void 0===t.attributes.uv2&&void 0!==t.attributes.uv&&t.setAttribute("uv2",t.attributes.uv),e.material=n}getMaterialType(){return a.MeshStandardMaterial}loadMaterial(e){let t;let n=this,r=this.json,i=this.extensions,l=r.materials[e],o={},s=l.extensions||{},u=[];if(s[f.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS]){let e=i[f.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS];t=e.getMaterialType(),u.push(e.extendParams(o,l,n))}else if(s[f.KHR_MATERIALS_UNLIT]){let e=i[f.KHR_MATERIALS_UNLIT];t=e.getMaterialType(),u.push(e.extendParams(o,l,n))}else{let r=l.pbrMetallicRoughness||{};if(o.color=new a.Color(1,1,1),o.opacity=1,Array.isArray(r.baseColorFactor)){let e=r.baseColorFactor;o.color.fromArray(e),o.opacity=e[3]}void 0!==r.baseColorTexture&&u.push(n.assignTexture(o,"map",r.baseColorTexture,a.sRGBEncoding)),o.metalness=void 0!==r.metallicFactor?r.metallicFactor:1,o.roughness=void 0!==r.roughnessFactor?r.roughnessFactor:1,void 0!==r.metallicRoughnessTexture&&(u.push(n.assignTexture(o,"metalnessMap",r.metallicRoughnessTexture)),u.push(n.assignTexture(o,"roughnessMap",r.metallicRoughnessTexture))),t=this._invokeOne(function(t){return t.getMaterialType&&t.getMaterialType(e)}),u.push(Promise.all(this._invokeAll(function(t){return t.extendMaterialParams&&t.extendMaterialParams(e,o)})))}!0===l.doubleSided&&(o.side=a.DoubleSide);let c=l.alphaMode||Q.OPAQUE;if(c===Q.BLEND?(o.transparent=!0,o.depthWrite=!1):(o.transparent=!1,c===Q.MASK&&(o.alphaTest=void 0!==l.alphaCutoff?l.alphaCutoff:.5)),void 0!==l.normalTexture&&t!==a.MeshBasicMaterial&&(u.push(n.assignTexture(o,"normalMap",l.normalTexture)),o.normalScale=new a.Vector2(1,1),void 0!==l.normalTexture.scale)){let e=l.normalTexture.scale;o.normalScale.set(e,e)}return void 0!==l.occlusionTexture&&t!==a.MeshBasicMaterial&&(u.push(n.assignTexture(o,"aoMap",l.occlusionTexture)),void 0!==l.occlusionTexture.strength&&(o.aoMapIntensity=l.occlusionTexture.strength)),void 0!==l.emissiveFactor&&t!==a.MeshBasicMaterial&&(o.emissive=new a.Color().fromArray(l.emissiveFactor)),void 0!==l.emissiveTexture&&t!==a.MeshBasicMaterial&&u.push(n.assignTexture(o,"emissiveMap",l.emissiveTexture,a.sRGBEncoding)),Promise.all(u).then(function(){let r;return r=t===w?i[f.KHR_MATERIALS_PBR_SPECULAR_GLOSSINESS].createMaterial(o):new t(o),l.name&&(r.name=l.name),j(r,l),n.associations.set(r,{materials:e}),l.extensions&&z(i,r,l),r})}createUniqueName(e){let t=a.PropertyBinding.sanitizeNodeName(e||""),n=t;for(let e=1;this.nodeNamesUsed[n];++e)n=t+"_"+e;return this.nodeNamesUsed[n]=!0,n}loadGeometries(e){let t=this,n=this.extensions,r=this.primitiveCache,i=[];for(let l=0,o=e.length;l<o;l++){let o=e[l],s=function(e){let t=e.extensions&&e.extensions[f.KHR_DRACO_MESH_COMPRESSION];return t?"draco:"+t.bufferView+":"+t.indices+":"+X(t.attributes):e.indices+":"+X(e.attributes)+":"+e.mode}(o),u=r[s];if(u)i.push(u.promise);else{let e;e=o.extensions&&o.extensions[f.KHR_DRACO_MESH_COMPRESSION]?function(e){return n[f.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(e,t).then(function(n){return V(n,e,t)})}(o):V(new a.BufferGeometry,o,t),r[s]={primitive:o,promise:e},i.push(e)}}return Promise.all(i)}loadMesh(e){let t=this,n=this.json,r=this.extensions,i=n.meshes[e],l=i.primitives,o=[];for(let e=0,t=l.length;e<t;e++){var s;let t=void 0===l[e].material?(void 0===(s=this.cache).DefaultMaterial&&(s.DefaultMaterial=new a.MeshStandardMaterial({color:16777215,emissive:0,metalness:1,roughness:1,transparent:!1,depthTest:!0,side:a.FrontSide})),s.DefaultMaterial):this.getDependency("material",l[e].material);o.push(t)}return o.push(t.loadGeometries(l)),Promise.all(o).then(function(n){let o=n.slice(0,n.length-1),s=n[n.length-1],u=[];for(let n=0,c=s.length;n<c;n++){let c;let f=s[n],d=l[n],p=o[n];if(d.mode===P.TRIANGLES||d.mode===P.TRIANGLE_STRIP||d.mode===P.TRIANGLE_FAN||void 0===d.mode)!0!==(c=!0===i.isSkinnedMesh?new a.SkinnedMesh(f,p):new a.Mesh(f,p)).isSkinnedMesh||c.geometry.attributes.skinWeight.normalized||c.normalizeSkinWeights(),d.mode===P.TRIANGLE_STRIP?c.geometry=Z(c.geometry,a.TriangleStripDrawMode):d.mode===P.TRIANGLE_FAN&&(c.geometry=Z(c.geometry,a.TriangleFanDrawMode));else if(d.mode===P.LINES)c=new a.LineSegments(f,p);else if(d.mode===P.LINE_STRIP)c=new a.Line(f,p);else if(d.mode===P.LINE_LOOP)c=new a.LineLoop(f,p);else if(d.mode===P.POINTS)c=new a.Points(f,p);else throw Error("THREE.GLTFLoader: Primitive mode unsupported: "+d.mode);Object.keys(c.geometry.morphAttributes).length>0&&function(e,t){if(e.updateMorphTargets(),void 0!==t.weights)for(let n=0,r=t.weights.length;n<r;n++)e.morphTargetInfluences[n]=t.weights[n];if(t.extras&&Array.isArray(t.extras.targetNames)){let n=t.extras.targetNames;if(e.morphTargetInfluences.length===n.length){e.morphTargetDictionary={};for(let t=0,r=n.length;t<r;t++)e.morphTargetDictionary[n[t]]=t}else console.warn("THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.")}}(c,i),c.name=t.createUniqueName(i.name||"mesh_"+e),j(c,i),d.extensions&&z(r,c,d),t.assignFinalMaterial(c),u.push(c)}for(let n=0,r=u.length;n<r;n++)t.associations.set(u[n],{meshes:e,primitives:n});if(1===u.length)return u[0];let c=new a.Group;t.associations.set(c,{meshes:e});for(let e=0,t=u.length;e<t;e++)c.add(u[e]);return c})}loadCamera(e){let t;let n=this.json.cameras[e],r=n[n.type];if(!r){console.warn("THREE.GLTFLoader: Missing camera parameters.");return}return"perspective"===n.type?t=new a.PerspectiveCamera(a.MathUtils.radToDeg(r.yfov),r.aspectRatio||1,r.znear||1,r.zfar||2e6):"orthographic"===n.type&&(t=new a.OrthographicCamera(-r.xmag,r.xmag,r.ymag,-r.ymag,r.znear,r.zfar)),n.name&&(t.name=this.createUniqueName(n.name)),j(t,n),Promise.resolve(t)}loadSkin(e){let t=this.json.skins[e],n={joints:t.joints};return void 0===t.inverseBindMatrices?Promise.resolve(n):this.getDependency("accessor",t.inverseBindMatrices).then(function(e){return n.inverseBindMatrices=e,n})}loadAnimation(e){let t=this.json,n=t.animations[e],r=[],i=[],l=[],o=[],s=[];for(let e=0,t=n.channels.length;e<t;e++){let t=n.channels[e],a=n.samplers[t.sampler],u=t.target,c=u.node,f=void 0!==n.parameters?n.parameters[a.input]:a.input,d=void 0!==n.parameters?n.parameters[a.output]:a.output;r.push(this.getDependency("node",c)),i.push(this.getDependency("accessor",f)),l.push(this.getDependency("accessor",d)),o.push(a),s.push(u)}return Promise.all([Promise.all(r),Promise.all(i),Promise.all(l),Promise.all(o),Promise.all(s)]).then(function(t){let r=t[0],i=t[1],l=t[2],o=t[3],s=t[4],u=[];for(let e=0,t=r.length;e<t;e++){let t;let n=r[e],c=i[e],f=l[e],d=o[e],p=s[e];if(void 0===n)continue;switch(n.updateMatrix(),N[p.path]){case N.weights:t=a.NumberKeyframeTrack;break;case N.rotation:t=a.QuaternionKeyframeTrack;break;case N.position:case N.scale:default:t=a.VectorKeyframeTrack}let h=n.name?n.name:n.uuid,m=void 0!==d.interpolation?K[d.interpolation]:a.InterpolateLinear,A=[];N[p.path]===N.weights?n.traverse(function(e){e.morphTargetInfluences&&A.push(e.name?e.name:e.uuid)}):A.push(h);let B=f.array;if(f.normalized){let e=Y(B.constructor),t=new Float32Array(B.length);for(let n=0,r=B.length;n<r;n++)t[n]=B[n]*e;B=t}for(let e=0,n=A.length;e<n;e++){let n=new t(A[e]+"."+N[p.path],c.array,B,m);"CUBICSPLINE"===d.interpolation&&(n.createInterpolant=function(e){let t=this instanceof a.QuaternionKeyframeTrack?H:T;return new t(this.times,this.values,this.getValueSize()/3,e)},n.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline=!0),u.push(n)}}let c=n.name?n.name:"animation_"+e;return new a.AnimationClip(c,void 0,u)})}createNodeMesh(e){let t=this.json,n=this,r=t.nodes[e];return void 0===r.mesh?null:n.getDependency("mesh",r.mesh).then(function(e){let t=n._getNodeRef(n.meshCache,r.mesh,e);return void 0!==r.weights&&t.traverse(function(e){if(e.isMesh)for(let t=0,n=r.weights.length;t<n;t++)e.morphTargetInfluences[t]=r.weights[t]}),t})}loadNode(e){let t=this.json,n=this.extensions,r=this,i=t.nodes[e],l=i.name?r.createUniqueName(i.name):"";return(function(){let t=[],n=r._invokeOne(function(t){return t.createNodeMesh&&t.createNodeMesh(e)});return n&&t.push(n),void 0!==i.camera&&t.push(r.getDependency("camera",i.camera).then(function(e){return r._getNodeRef(r.cameraCache,i.camera,e)})),r._invokeAll(function(t){return t.createNodeAttachment&&t.createNodeAttachment(e)}).forEach(function(e){t.push(e)}),Promise.all(t)})().then(function(t){let o;if((o=!0===i.isBone?new a.Bone:t.length>1?new a.Group:1===t.length?t[0]:new a.Object3D)!==t[0])for(let e=0,n=t.length;e<n;e++)o.add(t[e]);if(i.name&&(o.userData.name=i.name,o.name=l),j(o,i),i.extensions&&z(n,o,i),void 0!==i.matrix){let e=new a.Matrix4;e.fromArray(i.matrix),o.applyMatrix4(e)}else void 0!==i.translation&&o.position.fromArray(i.translation),void 0!==i.rotation&&o.quaternion.fromArray(i.rotation),void 0!==i.scale&&o.scale.fromArray(i.scale);return r.associations.has(o)||r.associations.set(o,{}),r.associations.get(o).nodes=e,o})}loadScene(e){let t=this.json,n=this.extensions,r=this.json.scenes[e],i=this,l=new a.Group;r.name&&(l.name=i.createUniqueName(r.name)),j(l,r),r.extensions&&z(n,l,r);let o=r.nodes||[],s=[];for(let e=0,n=o.length;e<n;e++)s.push(function e(t,n,r,i){let l=r.nodes[t];return i.getDependency("node",t).then(function(e){let t;return void 0===l.skin?e:i.getDependency("skin",l.skin).then(function(e){t=e;let n=[];for(let e=0,r=t.joints.length;e<r;e++)n.push(i.getDependency("node",t.joints[e]));return Promise.all(n)}).then(function(n){return e.traverse(function(e){if(!e.isMesh)return;let r=[],i=[];for(let e=0,l=n.length;e<l;e++){let l=n[e];if(l){r.push(l);let n=new a.Matrix4;void 0!==t.inverseBindMatrices&&n.fromArray(t.inverseBindMatrices.array,16*e),i.push(n)}else console.warn('THREE.GLTFLoader: Joint "%s" could not be found.',t.joints[e])}e.bind(new a.Skeleton(r,i),e.matrixWorld)}),e})}).then(function(t){n.add(t);let a=[];if(l.children){let n=l.children;for(let l=0,o=n.length;l<o;l++){let o=n[l];a.push(e(o,t,r,i))}}return Promise.all(a)})}(o[e],l,t,i));return Promise.all(s).then(function(){return i.associations=(e=>{let t=new Map;for(let[e,n]of i.associations)(e instanceof a.Material||e instanceof a.Texture)&&t.set(e,n);return e.traverse(e=>{let n=i.associations.get(e);null!=n&&t.set(e,n)}),t})(l),l})}}function V(e,t,n){let r=t.attributes,i=[];for(let t in r){let a=U[t]||t.toLowerCase();a in e.attributes||i.push(function(t,r){return n.getDependency("accessor",t).then(function(t){e.setAttribute(r,t)})}(r[t],a))}if(void 0!==t.indices&&!e.index){let r=n.getDependency("accessor",t.indices).then(function(t){e.setIndex(t)});i.push(r)}return j(e,t),!function(e,t,n){let r=t.attributes,i=new a.Box3;if(void 0===r.POSITION)return;{let e=n.json.accessors[r.POSITION],t=e.min,l=e.max;if(void 0!==t&&void 0!==l){if(i.set(new a.Vector3(t[0],t[1],t[2]),new a.Vector3(l[0],l[1],l[2])),e.normalized){let t=Y(L[e.componentType]);i.min.multiplyScalar(t),i.max.multiplyScalar(t)}}else{console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.");return}}let l=t.targets;if(void 0!==l){let e=new a.Vector3,t=new a.Vector3;for(let r=0,a=l.length;r<a;r++){let a=l[r];if(void 0!==a.POSITION){let r=n.json.accessors[a.POSITION],i=r.min,l=r.max;if(void 0!==i&&void 0!==l){if(t.setX(Math.max(Math.abs(i[0]),Math.abs(l[0]))),t.setY(Math.max(Math.abs(i[1]),Math.abs(l[1]))),t.setZ(Math.max(Math.abs(i[2]),Math.abs(l[2]))),r.normalized){let e=Y(L[r.componentType]);t.multiplyScalar(e)}e.max(t)}else console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.")}}i.expandByVector(e)}e.boundingBox=i;let o=new a.Sphere;i.getCenter(o.center),o.radius=i.min.distanceTo(i.max)/2,e.boundingSphere=o}(e,t,n),Promise.all(i).then(function(){return void 0!==t.targets?function(e,t,n){let r=!1,a=!1,i=!1;for(let e=0,n=t.length;e<n;e++){let n=t[e];if(void 0!==n.POSITION&&(r=!0),void 0!==n.NORMAL&&(a=!0),void 0!==n.COLOR_0&&(i=!0),r&&a&&i)break}if(!r&&!a&&!i)return Promise.resolve(e);let l=[],o=[],s=[];for(let u=0,c=t.length;u<c;u++){let c=t[u];if(r){let t=void 0!==c.POSITION?n.getDependency("accessor",c.POSITION):e.attributes.position;l.push(t)}if(a){let t=void 0!==c.NORMAL?n.getDependency("accessor",c.NORMAL):e.attributes.normal;o.push(t)}if(i){let t=void 0!==c.COLOR_0?n.getDependency("accessor",c.COLOR_0):e.attributes.color;s.push(t)}}return Promise.all([Promise.all(l),Promise.all(o),Promise.all(s)]).then(function(t){let n=t[0],l=t[1],o=t[2];return r&&(e.morphAttributes.position=n),a&&(e.morphAttributes.normal=l),i&&(e.morphAttributes.color=o),e.morphTargetsRelative=!0,e})}(e,t.targets,n):e})}function Z(e,t){let n=e.getIndex();if(null===n){let t=[],r=e.getAttribute("position");if(void 0===r)return console.error("THREE.GLTFLoader.toTrianglesDrawMode(): Undefined position attribute. Processing not possible."),e;for(let e=0;e<r.count;e++)t.push(e);e.setIndex(t),n=e.getIndex()}let r=n.count-2,i=[];if(t===a.TriangleFanDrawMode)for(let e=1;e<=r;e++)i.push(n.getX(0)),i.push(n.getX(e)),i.push(n.getX(e+1));else for(let e=0;e<r;e++)e%2==0?(i.push(n.getX(e)),i.push(n.getX(e+1)),i.push(n.getX(e+2))):(i.push(n.getX(e+2)),i.push(n.getX(e+1)),i.push(n.getX(e)));i.length/3!==r&&console.error("THREE.GLTFLoader.toTrianglesDrawMode(): Unable to generate correct amount of triangles.");let l=e.clone();return l.setIndex(i),l}var q=n(230);let $=null;function ee(e,t,n){return r=>{n&&n(r),e&&($||($=new l),$.setDecoderPath("string"==typeof e?e:"https://www.gstatic.com/draco/versioned/decoders/1.5.5/"),r.setDRACOLoader($)),t&&r.setMeshoptDecoder("function"==typeof s?s():s)}}function et(e,t=!0,n=!0,r){let a=(0,q.D)(u,e,ee(t,n,r));return a}et.preload=(e,t=!0,n=!0,r)=>q.D.preload(u,e,ee(t,n,r)),et.clear=e=>q.D.clear(u,e)},5769:function(e,t,n){"use strict";n.d(t,{m:function(){return o}});var r=n(9477),a=n(230),i=n(7294);let l=e=>e===Object(e)&&!Array.isArray(e)&&"function"!=typeof e;function o(e,t){let n=(0,a.z)(e=>e.gl),o=(0,a.D)(r.TextureLoader,l(e)?Object.values(e):e);if((0,i.useLayoutEffect)(()=>{null==t||t(o)},[t]),(0,i.useEffect)(()=>{let e=Array.isArray(o)?o:[o];e.forEach(n.initTexture)},[n,o]),!l(e))return o;{let t=Object.keys(e),n={};return t.forEach(e=>Object.assign(n,{[e]:o[t.indexOf(e)]})),n}}o.preload=e=>a.D.preload(r.TextureLoader,e),o.clear=e=>a.D.clear(r.TextureLoader,e)},230:function(e,t,n){"use strict";let r,a,i;n.d(t,{A:function(){return es},B:function(){return x},D:function(){return ec},E:function(){return w},a:function(){return I},b:function(){return eg},c:function(){return W},d:function(){return ev},e:function(){return y},g:function(){return eb},i:function(){return S},u:function(){return R},z:function(){return eo}});var l,o,s=n(9477),u=n(7294),c=n(2576),f=n(4671),d=n(6525),p=n.n(d),h=n(3840);function m(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let a=0;a<r;a++)if(!n(e[a],t[a]))return!1;return!0}let A=[];function B(e,t,n=!1,r={}){for(let e of A)if(m(t,e.keys,e.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(e,"error"))throw e.error;if(Object.prototype.hasOwnProperty.call(e,"response"))return e.response;if(!n)throw e.promise}let a={keys:t,equal:r.equal,promise:e(...t).then(e=>a.response=e).then(()=>{r.lifespan&&r.lifespan>0&&setTimeout(()=>{let e=A.indexOf(a);-1!==e&&A.splice(e,1)},r.lifespan)}).catch(e=>a.error=e)};if(A.push(a),!n)throw a.promise}let g=(e,t,n)=>B(e,t,!1,n),C=(e,t,n)=>void B(e,t,!0,n),v=e=>{if(void 0===e||0===e.length)A.splice(0,A.length);else{let t=A.find(t=>m(e,t.keys,t.equal));if(t){let e=A.indexOf(t);-1!==e&&A.splice(e,1)}}},b={},y=e=>void Object.assign(b,e),E=e=>"colorSpace"in e||"outputColorSpace"in e,M=()=>{var e;return null!=(e=b.ColorManagement)?e:null},F=e=>e&&e.isOrthographicCamera,S=e=>e&&e.hasOwnProperty("current"),I="undefined"!=typeof window&&(null!=(l=window.document)&&l.createElement||(null==(o=window.navigator)?void 0:o.product)==="ReactNative")?u.useLayoutEffect:u.useEffect;function R(e){let t=u.useRef(e);return I(()=>void(t.current=e),[e]),t}function x({set:e}){return I(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}class w extends u.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}w.getDerivedStateFromError=()=>({error:!0});let D="__default",G=new Map,T=e=>e&&!!e.memoized&&!!e.changes;function _(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}let H=e=>{var t;return null==(t=e.__r3f)?void 0:t.root.getState()},P={obj:e=>e===Object(e)&&!P.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:a=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(P.str(e)||P.num(e))return e===t;let l=P.obj(e);if(l&&"reference"===r)return e===t;let o=P.arr(e);if(o&&"reference"===n)return e===t;if((o||l)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(l&&"shallow"===n&&"shallow"===r){for(i in a?t:e)if(!P.equ(e[i],t[i],{strict:a,objects:"reference"}))return!1}else for(i in a?t:e)if(e[i]!==t[i])return!1;if(P.und(i)){if(o&&0===e.length&&0===t.length||l&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}};function L(e,t){let n=e;return(null!=t&&t.primitive||!n.__r3f)&&(n.__r3f={type:"",root:null,previousAttach:null,memoizedProps:{},eventCount:0,handlers:{},objects:[],parent:null,...t}),e}function k(e,t){let n=e;if(!t.includes("-"))return{target:n,key:t};{let r=t.split("-"),a=r.pop();return{target:n=r.reduce((e,t)=>e[t],e),key:a}}}let J=/-\d+$/;function O(e,t,n){if(P.str(n)){if(J.test(n)){let t=n.replace(J,""),{target:r,key:a}=k(e,t);Array.isArray(r[a])||(r[a]=[])}let{target:r,key:a}=k(e,n);t.__r3f.previousAttach=r[a],r[a]=t}else t.__r3f.previousAttach=n(e,t)}function U(e,t,n){var r,a;if(P.str(n)){let{target:r,key:a}=k(e,n),i=t.__r3f.previousAttach;void 0===i?delete r[a]:r[a]=i}else null==(r=t.__r3f)||null==r.previousAttach||r.previousAttach(e,t);null==(a=t.__r3f)||delete a.previousAttach}function N(e,{children:t,key:n,ref:r,...a},{children:i,key:l,ref:o,...s}={},u=!1){var c;let f=null!=(c=null==e?void 0:e.__r3f)?c:{},d=Object.entries(a),p=[];if(u){let e=Object.keys(s);for(let t=0;t<e.length;t++)a.hasOwnProperty(e[t])||d.unshift([e[t],D+"remove"])}d.forEach(([t,n])=>{var r;if(null!=(r=e.__r3f)&&r.primitive&&"object"===t||P.equ(n,s[t]))return;if(/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(t))return p.push([t,n,!0,[]]);let i=[];for(let e in t.includes("-")&&(i=t.split("-")),p.push([t,n,!1,i]),a){let n=a[e];e.startsWith(`${t}-`)&&p.push([e,n,!1,e.split("-")])}});let h={...a};return f.memoizedProps&&f.memoizedProps.args&&(h.args=f.memoizedProps.args),f.memoizedProps&&f.memoizedProps.attach&&(h.attach=f.memoizedProps.attach),{memoized:h,changes:p}}function K(e,t){var n,r,a;let i=null!=(n=e.__r3f)?n:{},l=i.root,o=null!=(r=null==l?void 0:null==l.getState?void 0:l.getState())?r:{},{memoized:u,changes:c}=T(t)?t:N(e,t),f=i.eventCount;e.__r3f&&(e.__r3f.memoizedProps=u);for(let t=0;t<c.length;t++){let[n,r,a,l]=c[t];if(E(e)){let e="srgb",t="srgb-linear";"encoding"===n?(n="colorSpace",r=3001===r?e:t):"outputEncoding"===n&&(n="outputColorSpace",r=3001===r?e:t)}let u=e,f=u[n];if(l.length&&!((f=l.reduce((e,t)=>e[t],e))&&f.set)){let[t,...r]=l.reverse();u=r.reverse().reduce((e,t)=>e[t],e),n=t}if(r===D+"remove"){if(u.constructor){let e=G.get(u.constructor);e||(e=new u.constructor,G.set(u.constructor,e)),r=e[n]}else r=0}if(a)r?i.handlers[n]=r:delete i.handlers[n],i.eventCount=Object.keys(i.handlers).length;else if(f&&f.set&&(f.copy||f instanceof s.Layers)){if(Array.isArray(r))f.fromArray?f.fromArray(r):f.set(...r);else if(f.copy&&r&&r.constructor&&f.constructor===r.constructor)f.copy(r);else if(void 0!==r){let e=f instanceof s.Color;!e&&f.setScalar?f.setScalar(r):f instanceof s.Layers&&r instanceof s.Layers?f.mask=r.mask:f.set(r),M()||o.linear||!e||f.convertSRGBToLinear()}}else if(u[n]=r,u[n]instanceof s.Texture&&u[n].format===s.RGBAFormat&&u[n].type===s.UnsignedByteType){let e=u[n];E(e)&&E(o.gl)?e.colorSpace=o.gl.outputColorSpace:e.encoding=o.gl.outputEncoding}Q(e)}if(i.parent&&o.internal&&e.raycast&&f!==i.eventCount){let t=o.internal.interaction.indexOf(e);t>-1&&o.internal.interaction.splice(t,1),i.eventCount&&o.internal.interaction.push(e)}let d=1===c.length&&"onUpdate"===c[0][0];return!d&&c.length&&null!=(a=e.__r3f)&&a.parent&&z(e),e}function Q(e){var t,n;let r=null==(t=e.__r3f)?void 0:null==(n=t.root)?void 0:null==n.getState?void 0:n.getState();r&&0===r.internal.frames&&r.invalidate()}function z(e){null==e.onUpdate||e.onUpdate(e)}function j(e,t){e.manual||(F(e)?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix(),e.updateMatrixWorld())}function X(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function Y(e,t,n,r){let a=n.get(t);a&&(n.delete(t),0===n.size&&(e.delete(r),a.target.releasePointerCapture(r)))}function W(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject,a=r.__r3f,i=null==a?void 0:a.handlers;if(n.hovered.delete(X(e)),null!=a&&a.eventCount){let n={...e,intersections:t};null==i.onPointerOut||i.onPointerOut(n),null==i.onPointerLeave||i.onPointerLeave(n)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(a){switch(a){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:l,internal:o}=e.getState();o.lastEvent.current=i;let u="onPointerMove"===a,c="onClick"===a||"onContextMenu"===a||"onDoubleClick"===a,f=function(t,n){let r=e.getState(),a=new Set,i=[],l=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<l.length;e++){let t=H(l[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let o=l.flatMap(function(e){let n=H(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=H(e.object),r=H(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=X(e);return!a.has(t)&&(a.add(t),!0)});for(let e of(r.events.filter&&(o=r.events.filter(o,r)),o)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())a.has(X(e.intersection))||i.push(e.intersection);return i}(i,u?t:void 0),d=c?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],a=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+a*a))}(i):0;"onPointerDown"===a&&(o.initialClick=[i.offsetX,i.offsetY],o.initialHits=f.map(e=>e.eventObject)),c&&!f.length&&d<=2&&(r(i,o.interaction),l&&l(i)),u&&n(f),function(t,r,a,i){let l=e.getState();if(t.length){let e={stopped:!1};for(let o of t){let u=H(o.object)||l,{raycaster:c,pointer:f,camera:d,internal:p}=u,h=new s.Vector3(f.x,f.y,0).unproject(d),m=e=>{var t,n;return null!=(t=null==(n=p.capturedMap.get(e))?void 0:n.has(o.eventObject))&&t},A=e=>{let t={intersection:o,target:r.target};p.capturedMap.has(e)?p.capturedMap.get(e).set(o.eventObject,t):p.capturedMap.set(e,new Map([[o.eventObject,t]])),r.target.setPointerCapture(e)},B=e=>{let t=p.capturedMap.get(e);t&&Y(p.capturedMap,o.eventObject,t,e)},g={};for(let e in r){let t=r[e];"function"!=typeof t&&(g[e]=t)}let C={...o,...g,pointer:f,intersections:t,stopped:e.stopped,delta:a,unprojectedPoint:h,ray:c.ray,camera:d,stopPropagation(){let a="pointerId"in r&&p.capturedMap.get(r.pointerId);if((!a||a.has(o.eventObject))&&(C.stopped=e.stopped=!0,p.hovered.size&&Array.from(p.hovered.values()).find(e=>e.eventObject===o.eventObject))){let e=t.slice(0,t.indexOf(o));n([...e,o])}},target:{hasPointerCapture:m,setPointerCapture:A,releasePointerCapture:B},currentTarget:{hasPointerCapture:m,setPointerCapture:A,releasePointerCapture:B},nativeEvent:r};if(i(C),!0===e.stopped)break}}}(f,i,d,function(e){let t=e.eventObject,n=t.__r3f,l=null==n?void 0:n.handlers;if(null!=n&&n.eventCount){if(u){if(l.onPointerOver||l.onPointerEnter||l.onPointerOut||l.onPointerLeave){let t=X(e),n=o.hovered.get(t);n?n.stopped&&e.stopPropagation():(o.hovered.set(t,e),null==l.onPointerOver||l.onPointerOver(e),null==l.onPointerEnter||l.onPointerEnter(e))}null==l.onPointerMove||l.onPointerMove(e)}else{let n=l[a];n?(!c||o.initialHits.includes(t))&&(r(i,o.interaction.filter(e=>!o.initialHits.includes(e))),n(e)):c&&o.initialHits.includes(t)&&r(i,o.interaction.filter(e=>!o.initialHits.includes(e)))}}})}}}}let V=["set","get","setSize","setFrameloop","setDpr","events","invalidate","advance","size","viewport"],Z=e=>!!(null!=e&&e.render),q=u.createContext(null),$=(e,t)=>{let n=(0,f.Z)((n,r)=>{let a;let i=new s.Vector3,l=new s.Vector3,o=new s.Vector3;function c(e=r().camera,t=l,n=r().size){let{width:a,height:u,top:c,left:f}=n,d=a/u;t instanceof s.Vector3?o.copy(t):o.set(...t);let p=e.getWorldPosition(i).distanceTo(o);if(F(e))return{width:a/e.zoom,height:u/e.zoom,top:c,left:f,factor:1,distance:p,aspect:d};{let t=e.fov*Math.PI/180,n=2*Math.tan(t/2)*p,r=n*(a/u);return{width:r,height:n,top:c,left:f,factor:a/r,distance:p,aspect:d}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),d=new s.Vector2,p={set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},xr:null,scene:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.Clock,pointer:d,mouse:d,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();a&&clearTimeout(a),e.performance.current!==e.performance.min&&f(e.performance.min),a=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0,updateStyle:!1},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:c},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,a,i,o)=>{let s=r().camera,u={width:e,height:t,top:i||0,left:o||0,updateStyle:a};n(e=>({size:u,viewport:{...e.viewport,...c(s,l,u)}}))},setDpr:e=>n(t=>{let n=_(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{active:!1,priority:0,frames:0,lastEvent:u.createRef(),interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,subscribe:(e,t,n)=>{let a=r().internal;return a.priority=a.priority+(t>0?1:0),a.subscribers.push({ref:e,priority:t,store:n}),a.subscribers=a.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0?1:0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}};return p}),r=n.getState(),a=r.size,i=r.viewport.dpr,l=r.camera;return n.subscribe(()=>{let{camera:e,size:t,viewport:r,gl:o,set:s}=n.getState();if(t!==a||r.dpr!==i){var u;a=t,i=r.dpr,j(e,t),o.setPixelRatio(r.dpr);let n=null!=(u=t.updateStyle)?u:"undefined"!=typeof HTMLCanvasElement&&o.domElement instanceof HTMLCanvasElement;o.setSize(t.width,t.height,n)}e!==l&&(l=e,s(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),n.subscribe(t=>e(t)),n},ee=new Set,et=new Set,en=new Set;function er(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function ea(e,t){switch(e){case"before":return er(ee,t);case"after":return er(et,t);case"tail":return er(en,t)}}function ei(e,t,n){let l=t.clock.getDelta();for("never"===t.frameloop&&"number"==typeof e&&(l=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),a=t.internal.subscribers,r=0;r<a.length;r++)(i=a[r]).ref.current(i.store.getState(),l,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}function el(){let e=u.useContext(q);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function eo(e=e=>e,t){return el()(e,t)}function es(e,t=0){let n=el(),r=n.getState().internal.subscribe,a=R(e);return I(()=>r(a,t,n),[t,r,n]),null}function eu(e,t){return function(n,...r){let a=new n;return e&&e(a),Promise.all(r.map(e=>new Promise((n,r)=>a.load(e,e=>{e.scene&&Object.assign(e,function(e){let t={nodes:{},materials:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material)}),t}(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${t.message})`))))))}}function ec(e,t,n,r){let a=Array.isArray(t)?t:[t],i=g(eu(n,r),[e,...a],{equal:P.equ});return Array.isArray(t)?i:i[0]}ec.preload=function(e,t,n){let r=Array.isArray(t)?t:[t];return C(eu(n),[e,...r])},ec.clear=function(e,t){let n=Array.isArray(t)?t:[t];return v([e,...n])};let ef=new Map,{invalidate:ed,advance:ep}=function(e){let t,n,r,a=!1;function i(l){for(let s of(n=requestAnimationFrame(i),a=!0,t=0,ea("before",l),e.values())){var o;(r=s.store.getState()).internal.active&&("always"===r.frameloop||r.internal.frames>0)&&!(null!=(o=r.gl.xr)&&o.isPresenting)&&(t+=ei(l,r))}if(ea("after",l),0===t)return ea("tail",l),a=!1,cancelAnimationFrame(n)}return{loop:i,invalidate:function t(n,r=1){var l;if(!n)return e.forEach(e=>t(e.store.getState()),r);null!=(l=n.gl.xr)&&l.isPresenting||!n.internal.active||"never"===n.frameloop||(n.internal.frames=Math.min(60,n.internal.frames+r),a||(a=!0,requestAnimationFrame(i)))},advance:function(t,n=!0,r,a){if(n&&ea("before",t),r)ei(t,r,a);else for(let n of e.values())ei(t,n.store.getState());n&&ea("after",t)}}}(ef),{reconciler:eh,applyProps:em}=function(e,t){function n(e,{args:t=[],attach:n,...r},a){let i,l=`${e[0].toUpperCase()}${e.slice(1)}`;if("primitive"===e){if(void 0===r.object)throw Error("R3F: Primitives without 'object' are invalid!");let t=r.object;i=L(t,{type:e,root:a,attach:n,primitive:!0})}else{let r=b[l];if(!r)throw Error(`R3F: ${l} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if(!Array.isArray(t))throw Error("R3F: The args prop must be an array!");i=L(new r(...t),{type:e,root:a,attach:n,memoizedProps:{args:t}})}return void 0===i.__r3f.attach&&(i instanceof s.BufferGeometry?i.__r3f.attach="geometry":i instanceof s.Material&&(i.__r3f.attach="material")),"inject"!==l&&K(i,r),i}function r(e,t){let n=!1;if(t){var r,a;null!=(r=t.__r3f)&&r.attach?O(e,t,t.__r3f.attach):t.isObject3D&&e.isObject3D&&(e.add(t),n=!0),n||null==(a=e.__r3f)||a.objects.push(t),t.__r3f||L(t,{}),t.__r3f.parent=e,z(t),Q(t)}}function a(e,t,n){let r=!1;if(t){var a,i;if(null!=(a=t.__r3f)&&a.attach)O(e,t,t.__r3f.attach);else if(t.isObject3D&&e.isObject3D){t.parent=e,t.dispatchEvent({type:"added"});let a=e.children.filter(e=>e!==t),i=a.indexOf(n);e.children=[...a.slice(0,i),t,...a.slice(i)],r=!0}r||null==(i=e.__r3f)||i.objects.push(t),t.__r3f||L(t,{}),t.__r3f.parent=e,z(t),Q(t)}}function i(e,t,n=!1){e&&[...e].forEach(e=>l(t,e,n))}function l(e,t,n){if(t){var r,a,l,o,s;t.__r3f&&(t.__r3f.parent=null),null!=(r=e.__r3f)&&r.objects&&(e.__r3f.objects=e.__r3f.objects.filter(e=>e!==t)),null!=(a=t.__r3f)&&a.attach?U(e,t,t.__r3f.attach):t.isObject3D&&e.isObject3D&&(e.remove(t),null!=(o=t.__r3f)&&o.root&&function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{Y(n.capturedMap,t,e,r)})}(t.__r3f.root,t));let u=null==(l=t.__r3f)?void 0:l.primitive,c=void 0===n?null!==t.dispose&&!u:n;u||(i(null==(s=t.__r3f)?void 0:s.objects,t,c),i(t.children,t,c)),t.__r3f&&(delete t.__r3f.root,delete t.__r3f.objects,delete t.__r3f.handlers,delete t.__r3f.memoizedProps,u||delete t.__r3f),c&&t.dispose&&"Scene"!==t.type&&(0,h.unstable_scheduleCallback)(h.unstable_IdlePriority,()=>{try{t.dispose()}catch(e){}}),Q(e)}}let o=()=>console.warn("Text is not allowed in the R3F tree! This could be stray whitespace or characters."),u=p()({createInstance:n,removeChild:l,appendChild:r,appendInitialChild:r,insertBefore:a,supportsMutation:!0,isPrimaryRenderer:!1,supportsPersistence:!1,supportsHydration:!1,noTimeout:-1,appendChildToContainer:(e,t)=>{if(!t)return;let n=e.getState().scene;n.__r3f&&(n.__r3f.root=e,r(n,t))},removeChildFromContainer:(e,t)=>{t&&l(e.getState().scene,t)},insertInContainerBefore:(e,t,n)=>{if(!t||!n)return;let r=e.getState().scene;r.__r3f&&a(r,t,n)},getRootHostContext:()=>null,getChildHostContext:e=>e,finalizeInitialChildren(e){var t;let n=null!=(t=null==e?void 0:e.__r3f)?t:{};return Boolean(n.handlers)},prepareUpdate(e,t,n,r){if(e.__r3f.primitive&&r.object&&r.object!==e)return[!0];{let{args:t=[],children:a,...i}=r,{args:l=[],children:o,...s}=n;if(!Array.isArray(t))throw Error("R3F: the args prop must be an array!");if(t.some((e,t)=>e!==l[t]))return[!0];let u=N(e,i,s,!0);return u.changes.length?[!1,u]:null}},commitUpdate(e,[t,a],i,o,s,u){t?function(e,t,a,i){var o;let s=null==(o=e.__r3f)?void 0:o.parent;if(!s)return;let u=n(t,a,e.__r3f.root);if(e.children){for(let t of e.children)t.__r3f&&r(u,t);e.children=e.children.filter(e=>!e.__r3f)}if(e.__r3f.objects.forEach(e=>r(u,e)),e.__r3f.objects=[],e.__r3f.autoRemovedBeforeAppend||l(s,e),u.parent&&(u.__r3f.autoRemovedBeforeAppend=!0),r(s,u),u.raycast&&u.__r3f.eventCount){let e=u.__r3f.root.getState();e.internal.interaction.push(u)}[i,i.alternate].forEach(e=>{null!==e&&(e.stateNode=u,e.ref&&("function"==typeof e.ref?e.ref(u):e.ref.current=u))})}(e,i,s,u):K(e,a)},commitMount(e,t,n,r){var a;let i=null!=(a=e.__r3f)?a:{};e.raycast&&i.handlers&&i.eventCount&&e.__r3f.root.getState().internal.interaction.push(e)},getPublicInstance:e=>e,prepareForCommit:()=>null,preparePortalMount:e=>L(e.getState().scene),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance(e){var t;let{attach:n,parent:r}=null!=(t=e.__r3f)?t:{};n&&r&&U(r,e,n),e.isObject3D&&(e.visible=!1),Q(e)},unhideInstance(e,t){var n;let{attach:r,parent:a}=null!=(n=e.__r3f)?n:{};r&&a&&O(a,e,r),(e.isObject3D&&null==t.visible||t.visible)&&(e.visible=!0),Q(e)},createTextInstance:o,hideTextInstance:o,unhideTextInstance:o,getCurrentEventPriority:()=>t?t():c.DefaultEventPriority,beforeActiveInstanceBlur:()=>{},afterActiveInstanceBlur:()=>{},detachDeletedInstance:()=>{},now:"undefined"!=typeof performance&&P.fun(performance.now)?performance.now:P.fun(Date.now)?Date.now:()=>0,scheduleTimeout:P.fun(setTimeout)?setTimeout:void 0,cancelTimeout:P.fun(clearTimeout)?clearTimeout:void 0});return{reconciler:u,applyProps:K}}(0,function(){var e;let t="undefined"!=typeof self&&self||"undefined"!=typeof window&&window;if(!t)return c.DefaultEventPriority;let n=null==(e=t.event)?void 0:e.type;switch(n){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return c.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return c.ContinuousEventPriority;default:return c.DefaultEventPriority}}),eA={objects:"shallow",strict:!1},eB=(e,t)=>{let n="function"==typeof e?e(t):e;return Z(n)?n:new s.WebGLRenderer({powerPreference:"high-performance",canvas:t,antialias:!0,alpha:!0,...e})};function eg(e){let t,n;let r=ef.get(e),a=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let l="function"==typeof reportError?reportError:console.error,o=i||$(ed,ep),f=a||eh.createContainer(o,c.ConcurrentRoot,null,!1,null,"",l,null);r||ef.set(e,{fiber:f,store:o});let d=!1;return{configure(r={}){let{gl:a,size:i,scene:l,events:u,onCreated:c,shadows:f=!1,linear:p=!1,flat:h=!1,legacy:m=!1,orthographic:A=!1,frameloop:B="always",dpr:g=[1,2],performance:C,raycaster:v,camera:b,onPointerMissed:y}=r,E=o.getState(),F=E.gl;E.gl||E.set({gl:F=eB(a,e)});let S=E.raycaster;S||E.set({raycaster:S=new s.Raycaster});let{params:I,...R}=v||{};if(P.equ(R,S,eA)||em(S,{...R}),P.equ(I,S.params,eA)||em(S,{params:{...S.params,...I}}),!E.camera||E.camera===n&&!P.equ(n,b,eA)){n=b;let e=b instanceof s.Camera,t=e?b:A?new s.OrthographicCamera(0,0,0,0,.1,1e3):new s.PerspectiveCamera(75,0,.1,1e3);e||(t.position.z=5,b&&em(t,b),E.camera||null!=b&&b.rotation||t.lookAt(0,0,0)),E.set({camera:t})}if(!E.scene){let e;l instanceof s.Scene?e=l:(e=new s.Scene,l&&em(e,l)),E.set({scene:L(e)})}if(!E.xr){let e=(e,t)=>{let n=o.getState();"never"!==n.frameloop&&ep(e,!0,n,t)},t=()=>{let t=o.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||ed(t)},n={connect(){let e=o.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=o.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};F.xr&&n.connect(),E.set({xr:n})}if(F.shadowMap){let e=F.shadowMap.enabled,t=F.shadowMap.type;if(F.shadowMap.enabled=!!f,P.boo(f))F.shadowMap.type=s.PCFSoftShadowMap;else if(P.str(f)){var x;let e={basic:s.BasicShadowMap,percentage:s.PCFShadowMap,soft:s.PCFSoftShadowMap,variance:s.VSMShadowMap};F.shadowMap.type=null!=(x=e[f])?x:s.PCFSoftShadowMap}else P.obj(f)&&Object.assign(F.shadowMap,f);(e!==F.shadowMap.enabled||t!==F.shadowMap.type)&&(F.shadowMap.needsUpdate=!0)}let w=M();w&&("enabled"in w?w.enabled=!m:"legacyMode"in w&&(w.legacyMode=m)),em(F,{outputEncoding:p?3e3:3001,toneMapping:h?s.NoToneMapping:s.ACESFilmicToneMapping}),E.legacy!==m&&E.set(()=>({legacy:m})),E.linear!==p&&E.set(()=>({linear:p})),E.flat!==h&&E.set(()=>({flat:h})),!a||P.fun(a)||Z(a)||P.equ(a,F,eA)||em(F,a),u&&!E.events.handlers&&E.set({events:u(o)});let D=function(e,t){if(t)return t;if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:a}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:a}}return"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0}}(e,i);return P.equ(D,E.size,eA)||E.setSize(D.width,D.height,D.updateStyle,D.top,D.left),g&&E.viewport.dpr!==_(g)&&E.setDpr(g),E.frameloop!==B&&E.setFrameloop(B),E.onPointerMissed||E.set({onPointerMissed:y}),C&&!P.equ(C,E.performance,eA)&&E.set(e=>({performance:{...e.performance,...C}})),t=c,d=!0,this},render(n){return d||this.configure(),eh.updateContainer(u.createElement(eC,{store:o,children:n,onCreated:t,rootElement:e}),f,null,()=>void 0),o},unmount(){ev(e)}}}function eC({store:e,children:t,onCreated:n,rootElement:r}){return I(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),u.createElement(q.Provider,{value:e},t)}function ev(e,t){let n=ef.get(e),r=null==n?void 0:n.fiber;if(r){let a=null==n?void 0:n.store.getState();a&&(a.internal.active=!1),eh.updateContainer(null,r,null,()=>{a&&setTimeout(()=>{try{var n,r,i,l;null==a.events.disconnect||a.events.disconnect(),null==(n=a.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=a.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(l=a.gl)&&l.xr&&a.xr.disconnect(),function(e){for(let t in e.dispose&&"Scene"!==e.type&&e.dispose(),e)null==t.dispose||t.dispose(),delete e[t]}(a),ef.delete(e),t&&t(e)}catch(e){}},500)})}}function eb(e,t,n){return u.createElement(ey,{key:t.uuid,children:e,container:t,state:n})}function ey({state:e={},children:t,container:n}){let{events:r,size:a,...i}=e,l=el(),[o]=u.useState(()=>new s.Raycaster),[c]=u.useState(()=>new s.Vector2),d=u.useCallback((e,t)=>{let u;let f={...e};if(Object.keys(e).forEach(n=>{(V.includes(n)||e[n]!==t[n]&&t[n])&&delete f[n]}),t&&a){let n=t.camera;u=e.viewport.getCurrentViewport(n,new s.Vector3,a),n!==e.camera&&j(n,a)}return{...f,scene:n,raycaster:o,pointer:c,mouse:c,previousRoot:l,events:{...e.events,...null==t?void 0:t.events,...r},size:{...e.size,...a},viewport:{...e.viewport,...u},...i}},[e]),[p]=u.useState(()=>{let e=l.getState(),t=(0,f.Z)((t,s)=>({...e,scene:n,raycaster:o,pointer:c,mouse:c,previousRoot:l,events:{...e.events,...r},size:{...e.size,...a},...i,set:t,get:s,setEvents:e=>t(t=>({...t,events:{...t.events,...e}}))}));return t});return u.useEffect(()=>{let e=l.subscribe(e=>p.setState(t=>d(e,t)));return()=>{e(),p.destroy()}},[]),u.useEffect(()=>{p.setState(e=>d(l.getState(),e))},[d]),u.createElement(u.Fragment,null,eh.createPortal(u.createElement(q.Provider,{value:p},t),p,null))}eh.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:u.version}),u.unstable_act},5029:function(e,t,n){"use strict";n.d(t,{Xz:function(){return R}});var r=n(230),a=n(7462),i=n(7294),l=n(9477),o=n(296),s=n.n(o);let u=["x","y","top","bottom","left","right","width","height"],c=(e,t)=>u.every(n=>e[n]===t[n]);var f=Object.defineProperty,d=Object.defineProperties,p=Object.getOwnPropertyDescriptors,h=Object.getOwnPropertySymbols,m=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,B=(e,t,n)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,g=(e,t)=>{for(var n in t||(t={}))m.call(t,n)&&B(e,n,t[n]);if(h)for(var n of h(t))A.call(t,n)&&B(e,n,t[n]);return e},C=(e,t)=>d(e,p(t));function v(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}let b=v(i.createContext(null));class y extends i.Component{render(){return i.createElement(b.Provider,{value:this._reactInternals},this.props.children)}}let{ReactCurrentOwner:E,ReactCurrentDispatcher:M}=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;n(2576),n(6525),n(3840);let F={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function S(e){let{handlePointer:t}=(0,r.c)(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(F).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{var n;let{set:r,events:a}=e.getState();null==a.disconnect||a.disconnect(),r(e=>({events:{...e.events,connected:t}})),Object.entries(null!=(n=a.handlers)?n:[]).forEach(([e,n])=>{let[r,a]=F[e];t.addEventListener(r,n,{passive:a})})},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){var r;Object.entries(null!=(r=n.handlers)?r:[]).forEach(([e,t])=>{if(n&&n.connected instanceof HTMLElement){let[r]=F[e];n.connected.removeEventListener(r,t)}}),t(e=>({events:{...e.events,connected:void 0}}))}}}}let I=i.forwardRef(function({children:e,fallback:t,resize:n,style:o,gl:u,events:f=S,eventSource:d,eventPrefix:p,shadows:h,linear:m,flat:A,legacy:B,orthographic:F,frameloop:I,dpr:R,performance:x,raycaster:w,camera:D,onPointerMissed:G,onCreated:T,..._},H){i.useMemo(()=>(0,r.e)(l),[]);let P=function(){let e=function(){var e,t;let n=function(){let e=i.useContext(b);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=i.useId(),n=i.useMemo(()=>{var n;return null!=(n=null==E?void 0:E.current)?n:function e(t,n,r){if(!t)return;if(!0===r(t))return t;let a=n?t.return:t.child;for(;a;){let t=e(a,n,r);if(t)return t;a=n?null:a.sibling}}(e,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}})},[e,t]);return n}(),[r]=i.useState(()=>new Map);r.clear();let a=n;for(;a;){let n=null==(e=a.type)?void 0:e._context;n&&n!==b&&!r.has(n)&&r.set(n,null==(t=null==M?void 0:M.current)?void 0:t.readContext(v(n))),a=a.return}return r}();return i.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>r=>i.createElement(t,null,i.createElement(n.Provider,C(g({},r),{value:e.get(n)}))),e=>i.createElement(y,g({},e))),[e])}(),[L,k]=function(e){var t;let{debounce:n,scroll:r,polyfill:a,offsetSize:l}=void 0===e?{debounce:0,scroll:!1,offsetSize:!1}:e,o=a||("undefined"==typeof window?class{}:window.ResizeObserver);if(!o)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[u,f]=(0,i.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),d=(0,i.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:u}),p=n?"number"==typeof n?n:n.scroll:null,h=n?"number"==typeof n?n:n.resize:null,m=(0,i.useRef)(!1);(0,i.useEffect)(()=>(m.current=!0,()=>void(m.current=!1)));let[A,B,g]=(0,i.useMemo)(()=>{let e=()=>{if(!d.current.element)return;let{left:e,top:t,width:n,height:r,bottom:a,right:i,x:o,y:s}=d.current.element.getBoundingClientRect(),u={left:e,top:t,width:n,height:r,bottom:a,right:i,x:o,y:s};d.current.element instanceof HTMLElement&&l&&(u.height=d.current.element.offsetHeight,u.width=d.current.element.offsetWidth),Object.freeze(u),m.current&&!c(d.current.lastBounds,u)&&f(d.current.lastBounds=u)};return[e,h?s()(e,h):e,p?s()(e,p):e]},[f,l,p,h]);function C(){d.current.scrollContainers&&(d.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",g,!0)),d.current.scrollContainers=null),d.current.resizeObserver&&(d.current.resizeObserver.disconnect(),d.current.resizeObserver=null)}function v(){d.current.element&&(d.current.resizeObserver=new o(g),d.current.resizeObserver.observe(d.current.element),r&&d.current.scrollContainers&&d.current.scrollContainers.forEach(e=>e.addEventListener("scroll",g,{capture:!0,passive:!0})))}let b=e=>{e&&e!==d.current.element&&(C(),d.current.element=e,d.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:a,overflowY:i}=window.getComputedStyle(t);return[r,a,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),v())};return t=Boolean(r),(0,i.useEffect)(()=>{if(t)return window.addEventListener("scroll",g,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",g,!0)},[g,t]),(0,i.useEffect)(()=>(window.addEventListener("resize",B),()=>void window.removeEventListener("resize",B)),[B]),(0,i.useEffect)(()=>{C(),v()},[r,g,B]),(0,i.useEffect)(()=>C,[]),[b,u,A]}({scroll:!0,debounce:{scroll:50,resize:0},...n}),J=i.useRef(null),O=i.useRef(null);i.useImperativeHandle(H,()=>J.current);let U=(0,r.u)(G),[N,K]=i.useState(!1),[Q,z]=i.useState(!1);if(N)throw N;if(Q)throw Q;let j=i.useRef(null);return(0,r.a)(()=>{let t=J.current;k.width>0&&k.height>0&&t&&(j.current||(j.current=(0,r.b)(t)),j.current.configure({gl:u,events:f,shadows:h,linear:m,flat:A,legacy:B,orthographic:F,frameloop:I,dpr:R,performance:x,raycaster:w,camera:D,size:k,onPointerMissed:(...e)=>null==U.current?void 0:U.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(d?(0,r.i)(d)?d.current:d:O.current),p&&e.setEvents({compute:(e,t)=>{let n=e[p+"X"],r=e[p+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==T||T(e)}}),j.current.render(i.createElement(P,null,i.createElement(r.E,{set:z},i.createElement(i.Suspense,{fallback:i.createElement(r.B,{set:K})},e)))))}),i.useEffect(()=>{let e=J.current;if(e)return()=>(0,r.d)(e)},[]),i.createElement("div",(0,a.Z)({ref:O,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:d?"none":"auto",...o}},_),i.createElement("div",{ref:L,style:{width:"100%",height:"100%"}},i.createElement("canvas",{ref:J,style:{display:"block"}},t)))}),R=i.forwardRef(function(e,t){return i.createElement(y,null,i.createElement(I,(0,a.Z)({},e,{ref:t})))})},296:function(e){function t(e,t,n){function r(){var u=Date.now()-o;u<t&&u>=0?a=setTimeout(r,t-u):(a=null,n||(s=e.apply(l,i),l=i=null))}null==t&&(t=100);var a,i,l,o,s,u=function(){l=this,i=arguments,o=Date.now();var u=n&&!a;return a||(a=setTimeout(r,t)),u&&(s=e.apply(l,i),l=i=null),s};return u.clear=function(){a&&(clearTimeout(a),a=null)},u.flush=function(){a&&(s=e.apply(l,i),l=i=null,clearTimeout(a),a=null)},u}t.debounce=t,e.exports=t},1663:function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){return(a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}n.d(t,{Ui:function(){return el}});var i,l,o,s,u,c,f,d,p,h,m,A,B,g,C,v,b,y,E,M,F,S,I,R,x,w=n(9477);function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function G(e,t){if(e){if("string"==typeof e)return D(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return D(e,t)}}function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i=[],l=!0,o=!1;try{for(a=a.call(e);!(l=(n=a.next()).done)&&(i.push(n.value),!t||i.length!==t);l=!0);}catch(e){o=!0,r=e}finally{try{l||null==a.return||a.return()}finally{if(o)throw r}}return i}}(e,t)||G(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}new w.Vector2,new w.Vector2;var H=function e(t,n,a){var i=this;_(this,e),r(this,"dot2",function(e,t){return i.x*e+i.y*t}),r(this,"dot3",function(e,t,n){return i.x*e+i.y*t+i.z*n}),this.x=t,this.y=n,this.z=a},P=[new H(1,1,0),new H(-1,1,0),new H(1,-1,0),new H(-1,-1,0),new H(1,0,1),new H(-1,0,1),new H(1,0,-1),new H(-1,0,-1),new H(0,1,1),new H(0,-1,1),new H(0,1,-1),new H(0,-1,-1)],L=[151,160,137,91,90,15,131,13,201,95,96,53,194,233,7,225,140,36,103,30,69,142,8,99,37,240,21,10,23,190,6,148,247,120,234,75,0,26,197,62,94,252,219,203,117,35,11,32,57,177,33,88,237,149,56,87,174,20,125,136,171,168,68,175,74,165,71,134,139,48,27,166,77,146,158,231,83,111,229,122,60,211,133,230,220,105,92,41,55,46,245,40,244,102,143,54,65,25,63,161,1,216,80,73,209,76,132,187,208,89,18,169,200,196,135,130,116,188,159,86,164,100,109,198,173,186,3,64,52,217,226,250,124,123,5,202,38,147,118,126,255,82,85,212,207,206,59,227,47,16,58,17,182,189,28,42,223,183,170,213,119,248,152,2,44,154,163,70,221,153,101,155,167,43,172,9,129,22,39,253,19,98,108,110,79,113,224,232,178,185,112,104,218,246,97,228,251,34,242,193,238,210,144,12,191,179,162,241,81,51,145,235,249,14,239,107,49,192,214,31,181,199,106,157,184,84,204,176,115,121,50,45,127,4,150,254,138,236,205,93,222,114,67,29,24,72,243,141,128,195,78,66,215,61,156,180],k=Array(512),J=Array(512);function O(e){var t=function(e){if("number"==typeof e)e=Math.abs(e);else if("string"==typeof e){var t=e;e=0;for(var n=0;n<t.length;n++)e=(e+(n+1)*(t.charCodeAt(n)%96))%2147483647}return 0===e&&(e=311),e}(e);return function(){var e=48271*t%2147483647;return t=e,e/2147483647}}!function(e){e>0&&e<1&&(e*=65536),(e=Math.floor(e))<256&&(e|=e<<8);for(var t,n=0;n<256;n++)t=1&n?L[n]^255&e:L[n]^e>>8&255,k[n]=k[n+256]=t,J[n]=J[n+256]=P[t%12]}(0),new function e(t){var n=this;_(this,e),r(this,"seed",0),r(this,"init",function(e){n.seed=e,n.value=O(e)}),r(this,"value",O(this.seed)),this.init(t)}(Math.random());var U=function(e){return 1/(1+e+.48*e*e+.235*e*e*e)};function N(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.25,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.01,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1/0,l=arguments.length>6&&void 0!==arguments[6]?arguments[6]:U,o=arguments.length>7&&void 0!==arguments[7]?arguments[7]:.001,s="velocity_"+t;if(void 0===e.__damp&&(e.__damp={}),void 0===e.__damp[s]&&(e.__damp[s]=0),Math.abs(e[t]-n)<=o)return e[t]=n,!1;var u=2/(r=Math.max(1e-4,r)),c=l(u*a),f=e[t]-n,d=n,p=i*r;f=Math.min(Math.max(f,-p),p),n=e[t]-f;var h=(e.__damp[s]+u*f)*a;e.__damp[s]=(e.__damp[s]-u*h)*c;var m=n+(f+h)*c;return d-e[t]>0==m>d&&(m=d,e.__damp[s]=(m-d)/a),e[t]=m,!0}function K(e,t,n,r,a,i,l,o){var s,u,c,f;return N(e,t,e[t]+(c=(s=n-e[t])-Math.floor(s/(u=2*Math.PI))*u,(f=Math.max(0,Math.min(u,c)))>Math.PI&&(f-=2*Math.PI),f),r,a,i,l,o)}var Q=new w.Vector2,z=new w.Vector3;function j(e,t,n,r,a,i,l){return"number"==typeof t?z.setScalar(t):Array.isArray(t)?z.set(t[0],t[1],t[2]):z.copy(t),o=N(e,"x",z.x,n,r,a,i,l),s=N(e,"y",z.y,n,r,a,i,l),u=N(e,"z",z.z,n,r,a,i,l),o||s||u}var X=new w.Vector4,Y=new w.Euler,W=new w.Color,V=new w.Quaternion,Z=new w.Vector4,q=new w.Vector4,$=new w.Vector4;function ee(e,t,n,r,a,i,l){var o=e;Array.isArray(t)?V.set(t[0],t[1],t[2],t[3]):V.copy(t);var s=e.dot(V)>0?1:-1;return V.x*=s,V.y*=s,V.z*=s,V.w*=s,v=N(e,"x",V.x,n,r,a,i,l),b=N(e,"y",V.y,n,r,a,i,l),y=N(e,"z",V.z,n,r,a,i,l),E=N(e,"w",V.w,n,r,a,i,l),Z.set(e.x,e.y,e.z,e.w).normalize(),q.set(o.__damp.velocity_x,o.__damp.velocity_y,o.__damp.velocity_z,o.__damp.velocity_w),$.copy(Z).multiplyScalar(q.dot(Z)/Z.dot(Z)),o.__damp.velocity_x-=$.x,o.__damp.velocity_y-=$.y,o.__damp.velocity_z-=$.z,o.__damp.velocity_w-=$.w,e.set(Z.x,Z.y,Z.z,Z.w),v||b||y||E}var et=new w.Spherical,en=new w.Matrix4,er=new w.Vector3,ea=new w.Quaternion,ei=new w.Vector3,el=Object.freeze({__proto__:null,rsqw:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.01,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/(2*Math.PI);return n/Math.atan(1/t)*Math.atan(Math.sin(2*Math.PI*e*r)/t)},exp:U,damp:N,dampAngle:K,damp2:function(e,t,n,r,a,o,s){return"number"==typeof t?Q.setScalar(t):Array.isArray(t)?Q.set(t[0],t[1]):Q.copy(t),i=N(e,"x",Q.x,n,r,a,o,s),l=N(e,"y",Q.y,n,r,a,o,s),i||l},damp3:j,damp4:function(e,t,n,r,a,i,l){return"number"==typeof t?X.setScalar(t):Array.isArray(t)?X.set(t[0],t[1],t[2],t[3]):X.copy(t),c=N(e,"x",X.x,n,r,a,i,l),f=N(e,"y",X.y,n,r,a,i,l),d=N(e,"z",X.z,n,r,a,i,l),p=N(e,"w",X.w,n,r,a,i,l),c||f||d||p},dampE:function(e,t,n,r,a,i,l){return Array.isArray(t)?Y.set(t[0],t[1],t[2],t[3]):Y.copy(t),h=K(e,"x",Y.x,n,r,a,i,l),m=K(e,"y",Y.y,n,r,a,i,l),A=K(e,"z",Y.z,n,r,a,i,l),h||m||A},dampC:function(e,t,n,r,a,i,l){return t instanceof w.Color?W.copy(t):Array.isArray(t)?W.setRGB(t[0],t[1],t[2]):W.set(t),B=N(e,"r",W.r,n,r,a,i,l),g=N(e,"g",W.g,n,r,a,i,l),C=N(e,"b",W.b,n,r,a,i,l),B||g||C},dampQ:ee,dampS:function(e,t,n,r,a,i,l){return Array.isArray(t)?et.set(t[0],t[1],t[2]):et.copy(t),M=N(e,"radius",et.radius,n,r,a,i,l),F=K(e,"phi",et.phi,n,r,a,i,l),S=K(e,"theta",et.theta,n,r,a,i,l),M||F||S},dampM:function(e,t,n,r,a,i,l){var o,s=e;return void 0===s.__damp&&(s.__damp={position:new w.Vector3,rotation:new w.Quaternion,scale:new w.Vector3},e.decompose(s.__damp.position,s.__damp.rotation,s.__damp.scale)),Array.isArray(t)?en.set.apply(en,function(e){if(Array.isArray(e))return D(e)}(o=t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||G(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):en.copy(t),en.decompose(er,ea,ei),I=j(s.__damp.position,er,n,r,a,i,l),R=ee(s.__damp.rotation,ea,n,r,a,i,l),x=j(s.__damp.scale,ei,n,r,a,i,l),e.compose(s.__damp.position,s.__damp.rotation,s.__damp.scale),I||R||x}});w.BufferGeometry},6511:function(e,t){"use strict";/**
 * @license React
 * react-reconciler-constants.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */t.ConcurrentRoot=1,t.ContinuousEventPriority=4,t.DefaultEventPriority=16,t.DiscreteEventPriority=1},7287:function(e,t,n){/**
 * @license React
 * react-reconciler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */e.exports=function(e){"use strict";var t,r,a,i,l,o={},s=n(7294),u=n(3840),c=Object.assign;function f(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,p=Symbol.for("react.element"),h=Symbol.for("react.portal"),m=Symbol.for("react.fragment"),A=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),g=Symbol.for("react.provider"),C=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),E=Symbol.for("react.memo"),M=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var F=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var S=Symbol.iterator;function I(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=S&&e[S]||e["@@iterator"])?e:null}function R(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case m:return"Fragment";case h:return"Portal";case B:return"Profiler";case A:return"StrictMode";case b:return"Suspense";case y:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case g:return(e._context.displayName||"Context")+".Provider";case v:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case E:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case M:t=e._payload,e=e._init;try{return R(e(t))}catch(e){}}return null}function x(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do 0!=(4098&(t=e).flags)&&(n=t.return),e=t.return;while(e)}return 3===t.tag?n:null}function w(e){if(x(e)!==e)throw Error(f(188))}function D(e){var t=e.alternate;if(!t){if(null===(t=x(e)))throw Error(f(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return w(a),e;if(i===r)return w(a),t;i=i.sibling}throw Error(f(188))}if(n.return!==r.return)n=a,r=i;else{for(var l=!1,o=a.child;o;){if(o===n){l=!0,n=a,r=i;break}if(o===r){l=!0,r=a,n=i;break}o=o.sibling}if(!l){for(o=i.child;o;){if(o===n){l=!0,n=i,r=a;break}if(o===r){l=!0,r=i,n=a;break}o=o.sibling}if(!l)throw Error(f(189))}}if(n.alternate!==r)throw Error(f(190))}if(3!==n.tag)throw Error(f(188));return n.stateNode.current===n?e:t}function G(e){return null!==(e=D(e))?function e(t){if(5===t.tag||6===t.tag)return t;for(t=t.child;null!==t;){var n=e(t);if(null!==n)return n;t=t.sibling}return null}(e):null}var T,_=Array.isArray,H=e.getPublicInstance,P=e.getRootHostContext,L=e.getChildHostContext,k=e.prepareForCommit,J=e.resetAfterCommit,O=e.createInstance,U=e.appendInitialChild,N=e.finalizeInitialChildren,K=e.prepareUpdate,Q=e.shouldSetTextContent,z=e.createTextInstance,j=e.scheduleTimeout,X=e.cancelTimeout,Y=e.noTimeout,W=e.isPrimaryRenderer,V=e.supportsMutation,Z=e.supportsPersistence,q=e.supportsHydration,$=e.getInstanceFromNode,ee=e.preparePortalMount,et=e.getCurrentEventPriority,en=e.detachDeletedInstance,er=e.supportsMicrotasks,ea=e.scheduleMicrotask,ei=e.supportsTestSelectors,el=e.findFiberRoot,eo=e.getBoundingRect,es=e.getTextContent,eu=e.isHiddenSubtree,ec=e.matchAccessibilityRole,ef=e.setFocusIfFocusable,ed=e.setupIntersectionObserver,ep=e.appendChild,eh=e.appendChildToContainer,em=e.commitTextUpdate,eA=e.commitMount,eB=e.commitUpdate,eg=e.insertBefore,eC=e.insertInContainerBefore,ev=e.removeChild,eb=e.removeChildFromContainer,ey=e.resetTextContent,eE=e.hideInstance,eM=e.hideTextInstance,eF=e.unhideInstance,eS=e.unhideTextInstance,eI=e.clearContainer,eR=e.cloneInstance,ex=e.createContainerChildSet,ew=e.appendChildToContainerChildSet,eD=e.finalizeContainerChildren,eG=e.replaceContainerChildren,eT=e.cloneHiddenInstance,e_=e.cloneHiddenTextInstance,eH=e.canHydrateInstance,eP=e.canHydrateTextInstance,eL=e.canHydrateSuspenseInstance,ek=e.isSuspenseInstancePending,eJ=e.isSuspenseInstanceFallback,eO=e.registerSuspenseInstanceRetry,eU=e.getNextHydratableSibling,eN=e.getFirstHydratableChild,eK=e.getFirstHydratableChildWithinContainer,eQ=e.getFirstHydratableChildWithinSuspenseInstance,ez=e.hydrateInstance,ej=e.hydrateTextInstance,eX=e.hydrateSuspenseInstance,eY=e.getNextHydratableInstanceAfterSuspenseInstance,eW=e.commitHydratedContainer,eV=e.commitHydratedSuspenseInstance,eZ=e.clearSuspenseBoundary,eq=e.clearSuspenseBoundaryFromContainer,e$=e.shouldDeleteUnhydratedTailInstances,e0=e.didNotMatchHydratedContainerTextInstance,e1=e.didNotMatchHydratedTextInstance;function e9(e){if(void 0===T)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);T=t&&t[1]||""}return"\n"+T+e}var e2=!1;function e3(e,t){if(!e||e2)return"";e2=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t){if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),i=r.stack.split("\n"),l=a.length-1,o=i.length-1;1<=l&&0<=o&&a[l]!==i[o];)o--;for(;1<=l&&0<=o;l--,o--)if(a[l]!==i[o]){if(1!==l||1!==o)do if(l--,0>--o||a[l]!==i[o]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}while(1<=l&&0<=o);break}}}finally{e2=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?e9(e):""}var e8=Object.prototype.hasOwnProperty,e4=[],e6=-1;function e5(e){return{current:e}}function e7(e){0>e6||(e.current=e4[e6],e4[e6]=null,e6--)}function te(e,t){e4[++e6]=e.current,e.current=t}var tt={},tn=e5(tt),tr=e5(!1),ta=tt;function ti(e,t){var n=e.type.contextTypes;if(!n)return tt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function tl(e){return null!=(e=e.childContextTypes)}function to(){e7(tr),e7(tn)}function ts(e,t,n){if(tn.current!==tt)throw Error(f(168));te(tn,t),te(tr,n)}function tu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(f(108,function(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return R(t);case 8:return t===A?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}(e)||"Unknown",a));return c({},n,r)}function tc(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||tt,ta=tn.current,te(tn,e),te(tr,tr.current),!0}function tf(e,t,n){var r=e.stateNode;if(!r)throw Error(f(169));n?(e=tu(e,t,ta),r.__reactInternalMemoizedMergedChildContext=e,e7(tr),e7(tn),te(tn,e)):e7(tr),te(tr,n)}var td=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(tp(e)/th|0)|0},tp=Math.log,th=Math.LN2,tm=64,tA=4194304;function tB(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function tg(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,l=268435455&n;if(0!==l){var o=l&~a;0!==o?r=tB(o):0!=(i&=l)&&(r=tB(i))}else 0!=(l=n&~a)?r=tB(l):0!==i&&(r=tB(i));if(0===r)return 0;if(0!==t&&t!==r&&0==(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&0!=(4194240&i)))return t;if(0!=(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-td(t)),r|=e[n],t&=~a;return r}function tC(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function tv(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function tb(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-td(t)]=n}function ty(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-td(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var tE=0;function tM(e){return 1<(e&=-e)?4<e?0!=(268435455&e)?16:536870912:4:1}var tF=u.unstable_scheduleCallback,tS=u.unstable_cancelCallback,tI=u.unstable_shouldYield,tR=u.unstable_requestPaint,tx=u.unstable_now,tw=u.unstable_ImmediatePriority,tD=u.unstable_UserBlockingPriority,tG=u.unstable_NormalPriority,tT=u.unstable_IdlePriority,t_=null,tH=null,tP="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},tL=null,tk=!1,tJ=!1;function tO(){if(!tJ&&null!==tL){tJ=!0;var e=0,t=tE;try{var n=tL;for(tE=1;e<n.length;e++){var r=n[e];do r=r(!0);while(null!==r)}tL=null,tk=!1}catch(t){throw null!==tL&&(tL=tL.slice(e+1)),tF(tw,tO),t}finally{tE=t,tJ=!1}}return null}var tU=d.ReactCurrentBatchConfig;function tN(e,t){if(tP(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!e8.call(t,a)||!tP(e[a],t[a]))return!1}return!0}function tK(e,t){if(e&&e.defaultProps)for(var n in t=c({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var tQ=e5(null),tz=null,tj=null,tX=null;function tY(){tX=tj=tz=null}function tW(e,t,n){W?(te(tQ,t._currentValue),t._currentValue=n):(te(tQ,t._currentValue2),t._currentValue2=n)}function tV(e){var t=tQ.current;e7(tQ),W?e._currentValue=t:e._currentValue2=t}function tZ(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function tq(e,t){tz=e,tX=tj=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!=(e.lanes&t)&&(r$=!0),e.firstContext=null)}function t$(e){var t=W?e._currentValue:e._currentValue2;if(tX!==e){if(e={context:e,memoizedValue:t,next:null},null===tj){if(null===tz)throw Error(f(308));tj=e,tz.dependencies={lanes:0,firstContext:e}}else tj=tj.next=e}return t}var t0=null,t1=!1;function t9(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function t2(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function t3(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function t8(e,t){var n=e.updateQueue;null!==n&&(n=n.shared,null!==aX&&0!=(1&e.mode)&&0==(2&aj)?(null===(e=n.interleaved)?(t.next=t,null===t0?t0=[n]:t0.push(n)):(t.next=e.next,e.next=t),n.interleaved=t):(null===(e=n.pending)?t.next=t:(t.next=e.next,e.next=t),n.pending=t))}function t4(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!=(4194240&n))){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ty(e,n)}}function t6(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=l:i=i.next=l,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function t5(e,t,n,r){var a=e.updateQueue;t1=!1;var i=a.firstBaseUpdate,l=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,u=s.next;s.next=null,null===l?i=u:l.next=u,l=s;var f=e.alternate;null!==f&&(o=(f=f.updateQueue).lastBaseUpdate)!==l&&(null===o?f.firstBaseUpdate=u:o.next=u,f.lastBaseUpdate=s)}if(null!==i){var d=a.baseState;for(l=0,f=u=s=null,o=i;;){var p=o.lane,h=o.eventTime;if((r&p)===p){null!==f&&(f=f.next={eventTime:h,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var m=e,A=o;switch(p=t,h=n,A.tag){case 1:if("function"==typeof(m=A.payload)){d=m.call(h,d,p);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null==(p="function"==typeof(m=A.payload)?m.call(h,d,p):m))break e;d=c({},d,p);break e;case 2:t1=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(p=a.effects)?a.effects=[o]:p.push(o))}else h={eventTime:h,lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===f?(u=f=h,s=d):f=f.next=h,l|=p;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(p=o).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}if(null===f&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=f,null!==(t=a.shared.interleaved)){a=t;do l|=a.lane,a=a.next;while(a!==t)}else null===i&&(a.shared.lanes=0);a0|=l,e.lanes=l,e.memoizedState=d}}function t7(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(f(191,a));a.call(r)}}}var ne=(new s.Component).refs;function nt(e,t,n,r){t=e.memoizedState,n=null==(n=n(r,t))?t:c({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var nn={isMounted:function(e){return!!(e=e._reactInternals)&&x(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=iu(),a=ic(e),i=t3(r,a);i.payload=t,null!=n&&(i.callback=n),t8(e,i),null!==(t=id(e,a,r))&&t4(t,e,a)},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=iu(),a=ic(e),i=t3(r,a);i.tag=1,i.payload=t,null!=n&&(i.callback=n),t8(e,i),null!==(t=id(e,a,r))&&t4(t,e,a)},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=iu(),r=ic(e),a=t3(n,r);a.tag=2,null!=t&&(a.callback=t),t8(e,a),null!==(t=id(e,r,n))&&t4(t,e,r)}};function nr(e,t,n,r,a,i,l){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,l):!t.prototype||!t.prototype.isPureReactComponent||!tN(n,r)||!tN(a,i)}function na(e,t,n){var r=!1,a=tt,i=t.contextType;return"object"==typeof i&&null!==i?i=t$(i):(a=tl(t)?ta:tn.current,i=(r=null!=(r=t.contextTypes))?ti(e,a):tt),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=nn,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function ni(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nn.enqueueReplaceState(t,t.state,null)}function nl(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs=ne,t9(e);var i=t.contextType;"object"==typeof i&&null!==i?a.context=t$(i):(i=tl(t)?ta:tn.current,a.context=ti(e,i)),a.state=e.memoizedState,"function"==typeof(i=t.getDerivedStateFromProps)&&(nt(e,t,i,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&nn.enqueueReplaceState(a,a.state,null),t5(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}var no=[],ns=0,nu=null,nc=0,nf=[],nd=0,np=null,nh=1,nm="";function nA(e,t){no[ns++]=nc,no[ns++]=nu,nu=e,nc=t}function nB(e,t,n){nf[nd++]=nh,nf[nd++]=nm,nf[nd++]=np,np=e;var r=nh;e=nm;var a=32-td(r)-1;r&=~(1<<a),n+=1;var i=32-td(t)+a;if(30<i){var l=a-a%5;i=(r&(1<<l)-1).toString(32),r>>=l,a-=l,nh=1<<32-td(t)+a|n<<a|r,nm=i+e}else nh=1<<i|n<<a|r,nm=e}function ng(e){null!==e.return&&(nA(e,1),nB(e,1,0))}function nC(e){for(;e===nu;)nu=no[--ns],no[ns]=null,nc=no[--ns],no[ns]=null;for(;e===np;)np=nf[--nd],nf[nd]=null,nm=nf[--nd],nf[nd]=null,nh=nf[--nd],nf[nd]=null}var nv=null,nb=null,ny=!1,nE=!1,nM=null;function nF(e,t){var n=ik(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function nS(e,t){switch(e.tag){case 5:return null!==(t=eH(t,e.type,e.pendingProps))&&(e.stateNode=t,nv=e,nb=eN(t),!0);case 6:return null!==(t=eP(t,e.pendingProps))&&(e.stateNode=t,nv=e,nb=null,!0);case 13:if(null!==(t=eL(t))){var n=null!==np?{id:nh,overflow:nm}:null;return e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=ik(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nv=e,nb=null,!0}return!1;default:return!1}}function nI(e){return 0!=(1&e.mode)&&0==(128&e.flags)}function nR(e){if(ny){var t=nb;if(t){var n=t;if(!nS(e,t)){if(nI(e))throw Error(f(418));t=eU(n);var r=nv;t&&nS(e,t)?nF(r,n):(e.flags=-4097&e.flags|2,ny=!1,nv=e)}}else{if(nI(e))throw Error(f(418));e.flags=-4097&e.flags|2,ny=!1,nv=e}}}function nx(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nv=e}function nw(e){if(!q||e!==nv)return!1;if(!ny)return nx(e),ny=!0,!1;if(3!==e.tag&&(5!==e.tag||e$(e.type)&&!Q(e.type,e.memoizedProps))){var t=nb;if(t){if(nI(e)){for(e=nb;e;)e=eU(e);throw Error(f(418))}for(;t;)nF(e,t),t=eU(t)}}if(nx(e),13===e.tag){if(!q)throw Error(f(316));if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(f(317));nb=eY(e)}else nb=nv?eU(e.stateNode):null;return!0}function nD(){q&&(nb=nv=null,nE=ny=!1)}function nG(e){null===nM?nM=[e]:nM.push(e)}function nT(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(f(309));var r=n.stateNode}if(!r)throw Error(f(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=a.refs;t===ne&&(t=a.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!=typeof e)throw Error(f(284));if(!n._owner)throw Error(f(290,e))}return e}function n_(e,t){throw Error(f(31,"[object Object]"===(e=Object.prototype.toString.call(t))?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function nH(e){return(0,e._init)(e._payload)}function nP(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=iO(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return(t.index=r,e)?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=iQ(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var i=n.type;return i===m?c(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===M&&nH(i)===t.type)?((r=a(t,n.props)).ref=nT(e,t,n),r.return=e,r):((r=iU(n.type,n.key,n.props,null,e.mode,r)).ref=nT(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=iz(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function c(e,t,n,r,i){return null===t||7!==t.tag?((t=iN(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=iQ(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case p:return(n=iU(t.type,t.key,t.props,null,e.mode,n)).ref=nT(e,null,t),n.return=e,n;case h:return(t=iz(t,e.mode,n)).return=e,t;case M:var r=t._init;return d(e,r(t._payload),n)}if(_(t)||I(t))return(t=iN(t,e.mode,n,null)).return=e,t;n_(e,t)}return null}function A(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:o(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?s(e,t,n,r):null;case h:return n.key===a?u(e,t,n,r):null;case M:return A(e,t,(a=n._init)(n._payload),r)}if(_(n)||I(n))return null!==a?null:c(e,t,n,r,null);n_(e,n)}return null}function B(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case p:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case M:return B(e,t,n,(0,r._init)(r._payload),a)}if(_(r)||I(r))return c(t,e=e.get(n)||null,r,a,null);n_(t,r)}return null}return function o(s,u,c,g){if("object"==typeof c&&null!==c&&c.type===m&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var C=c.key,v=u;null!==v;){if(v.key===C){if((C=c.type)===m){if(7===v.tag){n(s,v.sibling),(u=a(v,c.props.children)).return=s,s=u;break e}}else if(v.elementType===C||"object"==typeof C&&null!==C&&C.$$typeof===M&&nH(C)===v.type){n(s,v.sibling),(u=a(v,c.props)).ref=nT(s,v,c),u.return=s,s=u;break e}n(s,v);break}t(s,v),v=v.sibling}c.type===m?((u=iN(c.props.children,s.mode,g,c.key)).return=s,s=u):((g=iU(c.type,c.key,c.props,null,s.mode,g)).ref=nT(s,u,c),g.return=s,s=g)}return l(s);case h:e:{for(v=c.key;null!==u;){if(u.key===v){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(u=a(u,c.children||[])).return=s,s=u;break e}n(s,u);break}t(s,u),u=u.sibling}(u=iz(c,s.mode,g)).return=s,s=u}return l(s);case M:return o(s,u,(v=c._init)(c._payload),g)}if(_(c))return function(a,l,o,s){for(var u=null,c=null,f=l,p=l=0,h=null;null!==f&&p<o.length;p++){f.index>p?(h=f,f=null):h=f.sibling;var m=A(a,f,o[p],s);if(null===m){null===f&&(f=h);break}e&&f&&null===m.alternate&&t(a,f),l=i(m,l,p),null===c?u=m:c.sibling=m,c=m,f=h}if(p===o.length)return n(a,f),ny&&nA(a,p),u;if(null===f){for(;p<o.length;p++)null!==(f=d(a,o[p],s))&&(l=i(f,l,p),null===c?u=f:c.sibling=f,c=f);return ny&&nA(a,p),u}for(f=r(a,f);p<o.length;p++)null!==(h=B(f,a,p,o[p],s))&&(e&&null!==h.alternate&&f.delete(null===h.key?p:h.key),l=i(h,l,p),null===c?u=h:c.sibling=h,c=h);return e&&f.forEach(function(e){return t(a,e)}),ny&&nA(a,p),u}(s,u,c,g);if(I(c))return function(a,l,o,s){var u=I(o);if("function"!=typeof u)throw Error(f(150));if(null==(o=u.call(o)))throw Error(f(151));for(var c=u=null,p=l,h=l=0,m=null,g=o.next();null!==p&&!g.done;h++,g=o.next()){p.index>h?(m=p,p=null):m=p.sibling;var C=A(a,p,g.value,s);if(null===C){null===p&&(p=m);break}e&&p&&null===C.alternate&&t(a,p),l=i(C,l,h),null===c?u=C:c.sibling=C,c=C,p=m}if(g.done)return n(a,p),ny&&nA(a,h),u;if(null===p){for(;!g.done;h++,g=o.next())null!==(g=d(a,g.value,s))&&(l=i(g,l,h),null===c?u=g:c.sibling=g,c=g);return ny&&nA(a,h),u}for(p=r(a,p);!g.done;h++,g=o.next())null!==(g=B(p,a,h,g.value,s))&&(e&&null!==g.alternate&&p.delete(null===g.key?h:g.key),l=i(g,l,h),null===c?u=g:c.sibling=g,c=g);return e&&p.forEach(function(e){return t(a,e)}),ny&&nA(a,h),u}(s,u,c,g);n_(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(u=a(u,c)).return=s,s=u):(n(s,u),(u=iQ(c,s.mode,g)).return=s,s=u),l(s)):n(s,u)}}var nL=nP(!0),nk=nP(!1),nJ={},nO=e5(nJ),nU=e5(nJ),nN=e5(nJ);function nK(e){if(e===nJ)throw Error(f(174));return e}function nQ(e,t){te(nN,t),te(nU,e),te(nO,nJ),e=P(t),e7(nO),te(nO,e)}function nz(){e7(nO),e7(nU),e7(nN)}function nj(e){var t=nK(nN.current),n=nK(nO.current);t=L(n,e.type,t),n!==t&&(te(nU,e),te(nO,t))}function nX(e){nU.current===e&&(e7(nO),e7(nU))}var nY=e5(0);function nW(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||ek(n)||eJ(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!=(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nV=[];function nZ(){for(var e=0;e<nV.length;e++){var t=nV[e];W?t._workInProgressVersionPrimary=null:t._workInProgressVersionSecondary=null}nV.length=0}var nq=d.ReactCurrentDispatcher,n$=d.ReactCurrentBatchConfig,n0=0,n1=null,n9=null,n2=null,n3=!1,n8=!1,n4=0,n6=0;function n5(){throw Error(f(321))}function n7(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!tP(e[n],t[n]))return!1;return!0}function re(e,t,n,r,a,i){if(n0=i,n1=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nq.current=null===e||null===e.memoizedState?rP:rL,e=n(r,a),n8){i=0;do{if(n8=!1,n4=0,25<=i)throw Error(f(301));i+=1,n2=n9=null,t.updateQueue=null,nq.current=rk,e=n(r,a)}while(n8)}if(nq.current=rH,t=null!==n9&&null!==n9.next,n0=0,n2=n9=n1=null,n3=!1,t)throw Error(f(300));return e}function rt(){var e=0!==n4;return n4=0,e}function rn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===n2?n1.memoizedState=n2=e:n2=n2.next=e,n2}function rr(){if(null===n9){var e=n1.alternate;e=null!==e?e.memoizedState:null}else e=n9.next;var t=null===n2?n1.memoizedState:n2.next;if(null!==t)n2=t,n9=e;else{if(null===e)throw Error(f(310));e={memoizedState:(n9=e).memoizedState,baseState:n9.baseState,baseQueue:n9.baseQueue,queue:n9.queue,next:null},null===n2?n1.memoizedState=n2=e:n2=n2.next=e}return n2}function ra(e,t){return"function"==typeof t?t(e):t}function ri(e){var t=rr(),n=t.queue;if(null===n)throw Error(f(311));n.lastRenderedReducer=e;var r=n9,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var l=a.next;a.next=i.next,i.next=l}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var o=l=null,s=null,u=i;do{var c=u.lane;if((n0&c)===c)null!==s&&(s=s.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===s?(o=s=d,l=r):s=s.next=d,n1.lanes|=c,a0|=c}u=u.next}while(null!==u&&u!==i);null===s?l=r:s.next=o,tP(r,t.memoizedState)||(r$=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=s,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do i=a.lane,n1.lanes|=i,a0|=i,a=a.next;while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function rl(e){var t=rr(),n=t.queue;if(null===n)throw Error(f(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var l=a=a.next;do i=e(i,l.action),l=l.next;while(l!==a);tP(i,t.memoizedState)||(r$=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function ro(){}function rs(e,t){var n=n1,r=rr(),a=t(),i=!tP(r.memoizedState,a);if(i&&(r.memoizedState=a,r$=!0),r=r.queue,rC(rf.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==n2&&1&n2.memoizedState.tag){if(n.flags|=2048,rh(9,rc.bind(null,n,r,a,t),void 0,null),null===aX)throw Error(f(349));0!=(30&n0)||ru(n,t,a)}return a}function ru(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=n1.updateQueue)?(t={lastEffect:null,stores:null},n1.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function rc(e,t,n,r){t.value=n,t.getSnapshot=r,rd(t)&&id(e,1,-1)}function rf(e,t,n){return n(function(){rd(t)&&id(e,1,-1)})}function rd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!tP(e,n)}catch(e){return!0}}function rp(e){var t=rn();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ra,lastRenderedState:e},t.queue=e,e=e.dispatch=rw.bind(null,n1,e),[t.memoizedState,e]}function rh(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=n1.updateQueue)?(t={lastEffect:null,stores:null},n1.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function rm(){return rr().memoizedState}function rA(e,t,n,r){var a=rn();n1.flags|=e,a.memoizedState=rh(1|t,n,void 0,void 0===r?null:r)}function rB(e,t,n,r){var a=rr();r=void 0===r?null:r;var i=void 0;if(null!==n9){var l=n9.memoizedState;if(i=l.destroy,null!==r&&n7(r,l.deps)){a.memoizedState=rh(t,n,i,r);return}}n1.flags|=e,a.memoizedState=rh(1|t,n,i,r)}function rg(e,t){return rA(8390656,8,e,t)}function rC(e,t){return rB(2048,8,e,t)}function rv(e,t){return rB(4,2,e,t)}function rb(e,t){return rB(4,4,e,t)}function ry(e,t){return"function"==typeof t?(t(e=e()),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function rE(e,t,n){return n=null!=n?n.concat([e]):null,rB(4,4,ry.bind(null,t,e),n)}function rM(){}function rF(e,t){var n=rr();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&n7(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function rS(e,t){var n=rr();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&n7(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function rI(e,t){var n=tE;tE=0!==n&&4>n?n:4,e(!0);var r=n$.transition;n$.transition={};try{e(!1),t()}finally{tE=n,n$.transition=r}}function rR(){return rr().memoizedState}function rx(e,t,n){var r=ic(e);n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},rD(e)?rG(t,n):(rT(e,t,n),null!==(e=id(e,r,n=iu()))&&r_(e,t,r))}function rw(e,t,n){var r=ic(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(rD(e))rG(t,a);else{rT(e,t,a);var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var l=t.lastRenderedState,o=i(l,n);if(a.hasEagerState=!0,a.eagerState=o,tP(o,l))return}catch(e){}finally{}null!==(e=id(e,r,n=iu()))&&r_(e,t,r)}}function rD(e){var t=e.alternate;return e===n1||null!==t&&t===n1}function rG(e,t){n8=n3=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function rT(e,t,n){null!==aX&&0!=(1&e.mode)&&0==(2&aj)?(null===(e=t.interleaved)?(n.next=n,null===t0?t0=[t]:t0.push(t)):(n.next=e.next,e.next=n),t.interleaved=n):(null===(e=t.pending)?n.next=n:(n.next=e.next,e.next=n),t.pending=n)}function r_(e,t,n){if(0!=(4194240&n)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ty(e,n)}}var rH={readContext:t$,useCallback:n5,useContext:n5,useEffect:n5,useImperativeHandle:n5,useInsertionEffect:n5,useLayoutEffect:n5,useMemo:n5,useReducer:n5,useRef:n5,useState:n5,useDebugValue:n5,useDeferredValue:n5,useTransition:n5,useMutableSource:n5,useSyncExternalStore:n5,useId:n5,unstable_isNewReconciler:!1},rP={readContext:t$,useCallback:function(e,t){return rn().memoizedState=[e,void 0===t?null:t],e},useContext:t$,useEffect:rg,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,rA(4194308,4,ry.bind(null,t,e),n)},useLayoutEffect:function(e,t){return rA(4194308,4,e,t)},useInsertionEffect:function(e,t){return rA(4,2,e,t)},useMemo:function(e,t){var n=rn();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=rn();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=rx.bind(null,n1,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},rn().memoizedState=e},useState:rp,useDebugValue:rM,useDeferredValue:function(e){var t=rp(e),n=t[0],r=t[1];return rg(function(){var t=n$.transition;n$.transition={};try{r(e)}finally{n$.transition=t}},[e]),n},useTransition:function(){var e=rp(!1),t=e[0];return e=rI.bind(null,e[1]),rn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=n1,a=rn();if(ny){if(void 0===n)throw Error(f(407));n=n()}else{if(n=t(),null===aX)throw Error(f(349));0!=(30&n0)||ru(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,rg(rf.bind(null,r,i,e),[e]),r.flags|=2048,rh(9,rc.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=rn(),t=aX.identifierPrefix;if(ny){var n=nm,r=nh;t=":"+t+"R"+(n=(r&~(1<<32-td(r)-1)).toString(32)+n),0<(n=n4++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=n6++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rL={readContext:t$,useCallback:rF,useContext:t$,useEffect:rC,useImperativeHandle:rE,useInsertionEffect:rv,useLayoutEffect:rb,useMemo:rS,useReducer:ri,useRef:rm,useState:function(){return ri(ra)},useDebugValue:rM,useDeferredValue:function(e){var t=ri(ra),n=t[0],r=t[1];return rC(function(){var t=n$.transition;n$.transition={};try{r(e)}finally{n$.transition=t}},[e]),n},useTransition:function(){return[ri(ra)[0],rr().memoizedState]},useMutableSource:ro,useSyncExternalStore:rs,useId:rR,unstable_isNewReconciler:!1},rk={readContext:t$,useCallback:rF,useContext:t$,useEffect:rC,useImperativeHandle:rE,useInsertionEffect:rv,useLayoutEffect:rb,useMemo:rS,useReducer:rl,useRef:rm,useState:function(){return rl(ra)},useDebugValue:rM,useDeferredValue:function(e){var t=rl(ra),n=t[0],r=t[1];return rC(function(){var t=n$.transition;n$.transition={};try{r(e)}finally{n$.transition=t}},[e]),n},useTransition:function(){return[rl(ra)[0],rr().memoizedState]},useMutableSource:ro,useSyncExternalStore:rs,useId:rR,unstable_isNewReconciler:!1};function rJ(e,t){try{var n="",r=t;do n+=function(e){switch(e.tag){case 5:return e9(e.type);case 16:return e9("Lazy");case 13:return e9("Suspense");case 19:return e9("SuspenseList");case 0:case 2:case 15:return e=e3(e.type,!1);case 11:return e=e3(e.type.render,!1);case 1:return e=e3(e.type,!0);default:return""}}(r),r=r.return;while(r);var a=n}catch(e){a="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:a}}function rO(e,t){try{console.error(t.value)}catch(e){setTimeout(function(){throw e})}}var rU="function"==typeof WeakMap?WeakMap:Map;function rN(e,t,n){(n=t3(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){a5||(a5=!0,a7=r),rO(e,t)},n}function rK(e,t,n){(n=t3(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){rO(e,t)}}var i=e.stateNode;return null!==i&&"function"==typeof i.componentDidCatch&&(n.callback=function(){rO(e,t),"function"!=typeof r&&(null===ie?ie=new Set([this]):ie.add(this));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}function rQ(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new rU;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=iT.bind(null,e,t,n),t.then(e,e))}function rz(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function rj(e,t,n,r,a){return 0==(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=t3(-1,1)).tag=2,t8(n,t))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}function rX(e){e.flags|=4}function rY(e,t){if(null!==e&&e.child===t.child)return!0;if(0!=(16&t.flags))return!1;for(e=t.child;null!==e;){if(0!=(12854&e.flags)||0!=(12854&e.subtreeFlags))return!1;e=e.sibling}return!0}if(V)t=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)U(e,n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},r=function(){},a=function(e,t,n,r,a){(e=e.memoizedProps)!==r&&(n=K(t.stateNode,n,e,r,a,nK(nO.current)),(t.updateQueue=n)&&rX(t))},i=function(e,t,n,r){n!==r&&rX(t)};else if(Z){t=function(e,n,r,a){for(var i=n.child;null!==i;){if(5===i.tag){var l=i.stateNode;r&&a&&(l=eT(l,i.type,i.memoizedProps,i)),U(e,l)}else if(6===i.tag)l=i.stateNode,r&&a&&(l=e_(l,i.memoizedProps,i)),U(e,l);else if(4!==i.tag){if(22===i.tag&&null!==i.memoizedState)null!==(l=i.child)&&(l.return=i),t(e,i,!0,!0);else if(null!==i.child){i.child.return=i,i=i.child;continue}}if(i===n)break;for(;null===i.sibling;){if(null===i.return||i.return===n)return;i=i.return}i.sibling.return=i.return,i=i.sibling}};var rW=function(e,t,n,r){for(var a=t.child;null!==a;){if(5===a.tag){var i=a.stateNode;n&&r&&(i=eT(i,a.type,a.memoizedProps,a)),ew(e,i)}else if(6===a.tag)i=a.stateNode,n&&r&&(i=e_(i,a.memoizedProps,a)),ew(e,i);else if(4!==a.tag){if(22===a.tag&&null!==a.memoizedState)null!==(i=a.child)&&(i.return=a),rW(e,a,!0,!0);else if(null!==a.child){a.child.return=a,a=a.child;continue}}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;a=a.return}a.sibling.return=a.return,a=a.sibling}};r=function(e,t){var n=t.stateNode;if(!rY(e,t)){var r=ex(e=n.containerInfo);rW(r,t,!1,!1),n.pendingChildren=r,rX(t),eD(e,r)}},a=function(e,n,r,a,i){var l=e.stateNode,o=e.memoizedProps;if((e=rY(e,n))&&o===a)n.stateNode=l;else{var s=n.stateNode,u=nK(nO.current),c=null;o!==a&&(c=K(s,r,o,a,i,u)),e&&null===c?n.stateNode=l:(N(l=eR(l,c,r,o,a,n,e,s),r,a,i,u)&&rX(n),n.stateNode=l,e?rX(n):t(l,n,!1,!1))}},i=function(e,t,n,r){n!==r?(e=nK(nN.current),n=nK(nO.current),t.stateNode=z(r,e,n,t),rX(t)):t.stateNode=e.stateNode}}else r=function(){},a=function(){},i=function(){};function rV(e,t){if(!ny)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rZ(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}var rq=d.ReactCurrentOwner,r$=!1;function r0(e,t,n,r){t.child=null===e?nk(t,null,n,r):nL(t,e.child,n,r)}function r1(e,t,n,r,a){n=n.render;var i=t.ref;return(tq(t,a),r=re(e,t,n,r,i,a),n=rt(),null===e||r$)?(ny&&n&&ng(t),t.flags|=1,r0(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,au(e,t,a))}function r9(e,t,n,r,a){if(null===e){var i=n.type;return"function"!=typeof i||iJ(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=iU(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,r2(e,t,i,r,a))}if(i=e.child,0==(e.lanes&a)){var l=i.memoizedProps;if((n=null!==(n=n.compare)?n:tN)(l,r)&&e.ref===t.ref)return au(e,t,a)}return t.flags|=1,(e=iO(i,r)).ref=t.ref,e.return=t,t.child=e}function r2(e,t,n,r,a){if(null!==e&&tN(e.memoizedProps,r)&&e.ref===t.ref){if(r$=!1,0==(e.lanes&a))return t.lanes=e.lanes,au(e,t,a);0!=(131072&e.flags)&&(r$=!0)}return r4(e,t,n,r,a)}function r3(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0==(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null},te(aZ,aV),aV|=n;else{if(0==(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null},t.updateQueue=null,te(aZ,aV),aV|=e,null;t.memoizedState={baseLanes:0,cachePool:null},r=null!==i?i.baseLanes:n,te(aZ,aV),aV|=r}}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,te(aZ,aV),aV|=r;return r0(e,t,a,n),t.child}function r8(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function r4(e,t,n,r,a){var i=tl(n)?ta:tn.current;return(i=ti(t,i),tq(t,a),n=re(e,t,n,r,i,a),r=rt(),null===e||r$)?(ny&&r&&ng(t),t.flags|=1,r0(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,au(e,t,a))}function r6(e,t,n,r,a){if(tl(n)){var i=!0;tc(t)}else i=!1;if(tq(t,a),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),na(t,n,r),nl(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,o=t.memoizedProps;l.props=o;var s=l.context,u=n.contextType;u="object"==typeof u&&null!==u?t$(u):ti(t,u=tl(n)?ta:tn.current);var c=n.getDerivedStateFromProps,f="function"==typeof c||"function"==typeof l.getSnapshotBeforeUpdate;f||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==r||s!==u)&&ni(t,l,r,u),t1=!1;var d=t.memoizedState;l.state=d,t5(t,r,l,a),s=t.memoizedState,o!==r||d!==s||tr.current||t1?("function"==typeof c&&(nt(t,n,c,r),s=t.memoizedState),(o=t1||nr(t,n,o,r,d,s,u))?(f||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=u,r=o):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,t2(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:tK(t.type,o),l.props=u,f=t.pendingProps,d=l.context,s="object"==typeof(s=n.contextType)&&null!==s?t$(s):ti(t,s=tl(n)?ta:tn.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==f||d!==s)&&ni(t,l,r,s),t1=!1,d=t.memoizedState,l.state=d,t5(t,r,l,a);var h=t.memoizedState;o!==f||d!==h||tr.current||t1?("function"==typeof p&&(nt(t,n,p,r),h=t.memoizedState),(u=t1||nr(t,n,u,r,d,h,s)||!1)?(c||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,s),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,s)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=s,r=u):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return r5(e,t,n,r,i,a)}function r5(e,t,n,r,a,i){r8(e,t);var l=0!=(128&t.flags);if(!r&&!l)return a&&tf(t,n,!1),au(e,t,i);r=t.stateNode,rq.current=t;var o=l&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=nL(t,e.child,null,i),t.child=nL(t,null,o,i)):r0(e,t,o,i),t.memoizedState=r.state,a&&tf(t,n,!0),t.child}function r7(e){var t=e.stateNode;t.pendingContext?ts(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ts(e,t.context,!1),nQ(e,t.containerInfo)}function ae(e,t,n,r,a){return nD(),nG(a),t.flags|=256,r0(e,t,n,r),t.child}var at={dehydrated:null,treeContext:null,retryLane:0};function an(e){return{baseLanes:e,cachePool:null}}function ar(e,t,n){var r,a,i,l,o,s,u,c,d,p,h,m,A,B,g=t.pendingProps,C=nY.current,v=!1,b=0!=(128&t.flags);if((B=b)||(B=(null===e||null!==e.memoizedState)&&0!=(2&C)),B?(v=!0,t.flags&=-129):(null===e||null!==e.memoizedState)&&(C|=1),te(nY,1&C),null===e)return(nR(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated))?(0==(1&t.mode)?t.lanes=1:eJ(e)?t.lanes=8:t.lanes=1073741824,null):(C=g.children,e=g.fallback,v?(g=t.mode,v=t.child,C={mode:"hidden",children:C},0==(1&g)&&null!==v?(v.childLanes=0,v.pendingProps=C):v=iK(C,g,0,null),e=iN(e,g,n,null),v.return=t,e.return=t,v.sibling=e,t.child=v,t.child.memoizedState=an(n),t.memoizedState=at,e):aa(t,C));if(null!==(C=e.memoizedState)&&null!==(B=C.dehydrated)){if(b)return 256&t.flags?(t.flags&=-257,ai(e,t,n,Error(f(422)))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(v=g.fallback,C=t.mode,g=iK({mode:"visible",children:g.children},C,0,null),v=iN(v,C,n,null),v.flags|=2,g.return=t,v.return=t,g.sibling=v,t.child=g,0!=(1&t.mode)&&nL(t,e.child,null,n),t.child.memoizedState=an(n),t.memoizedState=at,v);if(0==(1&t.mode))t=ai(e,t,n,null);else if(eJ(B))t=ai(e,t,n,Error(f(419)));else if(g=0!=(n&e.childLanes),r$||g){if(null!==(g=aX)){switch(n&-n){case 4:v=2;break;case 16:v=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:v=32;break;case 536870912:v=268435456;break;default:v=0}0!==(g=0!=(v&(g.suspendedLanes|n))?0:v)&&g!==C.retryLane&&(C.retryLane=g,id(e,g,-1))}iF(),t=ai(e,t,n,Error(f(421)))}else ek(B)?(t.flags|=128,t.child=e.child,eO(B,t=iH.bind(null,e)),t=null):(n=C.treeContext,q&&(nb=eQ(B),nv=t,ny=!0,nM=null,nE=!1,null!==n&&(nf[nd++]=nh,nf[nd++]=nm,nf[nd++]=np,nh=n.id,nm=n.overflow,np=t)),t=aa(t,t.pendingProps.children),t.flags|=4096);return t}return v?(r=e,a=t,i=g.children,l=g.fallback,o=n,s=a.mode,u=(r=r.child).sibling,c={mode:"hidden",children:i},0==(1&s)&&a.child!==r?((i=a.child).childLanes=0,i.pendingProps=c,a.deletions=null):(i=iO(r,c)).subtreeFlags=14680064&r.subtreeFlags,null!==u?l=iO(u,l):(l=iN(l,s,o,null),l.flags|=2),l.return=a,i.return=a,i.sibling=l,a.child=i,g=l,v=t.child,C=e.child.memoizedState,v.memoizedState=null===C?an(n):{baseLanes:C.baseLanes|n,cachePool:null},v.childLanes=e.childLanes&~n,t.memoizedState=at,g):(d=e,p=t,h=g.children,m=n,d=(A=d.child).sibling,h=iO(A,{mode:"visible",children:h}),0==(1&p.mode)&&(h.lanes=m),h.return=p,h.sibling=null,null!==d&&(null===(m=p.deletions)?(p.deletions=[d],p.flags|=16):m.push(d)),n=p.child=h,t.memoizedState=null,n)}function aa(e,t){return(t=iK({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function ai(e,t,n,r){return null!==r&&nG(r),nL(t,e.child,null,n),e=aa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function al(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),tZ(e.return,t,n)}function ao(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function as(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(r0(e,t,r.children,n),0!=(2&(r=nY.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!=(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&al(e,n,t);else if(19===e.tag)al(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(nY,r),0==(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(a=null,n=t.child;null!==n;)null!==(e=n.alternate)&&null===nW(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),ao(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===nW(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}ao(t,!0,n,null,i);break;case"together":ao(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function au(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),a0|=t.lanes,0==(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(f(153));if(null!==t.child){for(n=iO(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=iO(e,e.pendingProps)).return=t;n.sibling=null}return t.child}var ac=!1,af=!1,ad="function"==typeof WeakSet?WeakSet:Set,ap=null;function ah(e,t){var n=e.ref;if(null!==n){if("function"==typeof n)try{n(null)}catch(n){iG(e,t,n)}else n.current=null}}function am(e,t,n){try{n()}catch(n){iG(e,t,n)}}var aA=!1;function aB(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&am(t,n,i)}a=a.next}while(a!==r)}}function ag(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function aC(e){var t=e.ref;if(null!==t){var n=e.stateNode;e=5===e.tag?H(n):n,"function"==typeof t?t(e):t.current=e}}function av(e,t,n){if(tH&&"function"==typeof tH.onCommitFiberUnmount)try{tH.onCommitFiberUnmount(t_,t)}catch(e){}switch(t.tag){case 0:case 11:case 14:case 15:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e=e.next;do{var a=r,i=a.destroy;a=a.tag,void 0!==i&&(0!=(2&a)?am(t,n,i):0!=(4&a)&&am(t,n,i)),r=r.next}while(r!==e)}break;case 1:if(ah(t,n),"function"==typeof(e=t.stateNode).componentWillUnmount)try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(e){iG(t,n,e)}break;case 5:ah(t,n);break;case 4:V?aF(e,t,n):Z&&Z&&(n=ex(t=t.stateNode.containerInfo),eG(t,n))}}function ab(e,t,n){for(var r=t;;)if(av(e,r,n),null===r.child||V&&4===r.tag){if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}else r.child.return=r,r=r.child}function ay(e){return 5===e.tag||3===e.tag||4===e.tag}function aE(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ay(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags||null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function aM(e){if(V){e:{for(var t=e.return;null!==t;){if(ay(t))break e;t=t.return}throw Error(f(160))}var n=t;switch(n.tag){case 5:t=n.stateNode,32&n.flags&&(ey(t),n.flags&=-33),n=aE(e),function e(t,n,r){var a=t.tag;if(5===a||6===a)t=t.stateNode,n?eg(r,t,n):ep(r,t);else if(4!==a&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t);break;case 3:case 4:t=n.stateNode.containerInfo,n=aE(e),function e(t,n,r){var a=t.tag;if(5===a||6===a)t=t.stateNode,n?eC(r,t,n):eh(r,t);else if(4!==a&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t);break;default:throw Error(f(161))}}}function aF(e,t,n){for(var r,a,i=t,l=!1;;){if(!l){l=i.return;e:for(;;){if(null===l)throw Error(f(160));switch(r=l.stateNode,l.tag){case 5:a=!1;break e;case 3:case 4:r=r.containerInfo,a=!0;break e}l=l.return}l=!0}if(5===i.tag||6===i.tag)ab(e,i,n),a?eb(r,i.stateNode):ev(r,i.stateNode);else if(18===i.tag)a?eq(r,i.stateNode):eZ(r,i.stateNode);else if(4===i.tag){if(null!==i.child){r=i.stateNode.containerInfo,a=!0,i.child.return=i,i=i.child;continue}}else if(av(e,i,n),null!==i.child){i.child.return=i,i=i.child;continue}if(i===t)break;for(;null===i.sibling;){if(null===i.return||i.return===t)return;4===(i=i.return).tag&&(l=!1)}i.sibling.return=i.return,i=i.sibling}}function aS(e,t){if(V){switch(t.tag){case 0:case 11:case 14:case 15:aB(3,t,t.return),ag(3,t),aB(5,t,t.return);return;case 1:case 12:case 17:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps;e=null!==e?e.memoizedProps:r;var a=t.type,i=t.updateQueue;t.updateQueue=null,null!==i&&eB(n,i,a,e,r,t)}return;case 6:if(null===t.stateNode)throw Error(f(162));n=t.memoizedProps,em(t.stateNode,null!==e?e.memoizedProps:n,n);return;case 3:q&&null!==e&&e.memoizedState.isDehydrated&&eW(t.stateNode.containerInfo);return;case 13:case 19:aI(t);return}throw Error(f(163))}switch(t.tag){case 0:case 11:case 14:case 15:aB(3,t,t.return),ag(3,t),aB(5,t,t.return);return;case 12:case 22:case 23:return;case 13:case 19:aI(t);return;case 3:q&&null!==e&&e.memoizedState.isDehydrated&&eW(t.stateNode.containerInfo)}e:if(Z){switch(t.tag){case 1:case 5:case 6:break e;case 3:case 4:eG((t=t.stateNode).containerInfo,t.pendingChildren);break e}throw Error(f(163))}}function aI(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new ad),t.forEach(function(t){var r=iP.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function aR(e){for(;null!==ap;){var t=ap;if(0!=(8772&t.flags)){var n=t.alternate;try{if(0!=(8772&t.flags))switch(t.tag){case 0:case 11:case 15:af||ag(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!af){if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:tK(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}}var i=t.updateQueue;null!==i&&t7(t,i,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:n=H(t.child.stateNode);break;case 1:n=t.child.stateNode}t7(t,l,n)}break;case 5:var o=t.stateNode;null===n&&4&t.flags&&eA(o,t.type,t.memoizedProps,t);break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:break;case 13:if(q&&null===t.memoizedState){var s=t.alternate;if(null!==s){var u=s.memoizedState;if(null!==u){var c=u.dehydrated;null!==c&&eV(c)}}}break;default:throw Error(f(163))}af||512&t.flags&&aC(t)}catch(e){iG(t,t.return,e)}}if(t===e){ap=null;break}if(null!==(n=t.sibling)){n.return=t.return,ap=n;break}ap=t.return}}function ax(e){for(;null!==ap;){var t=ap;if(t===e){ap=null;break}var n=t.sibling;if(null!==n){n.return=t.return,ap=n;break}ap=t.return}}function aw(e){for(;null!==ap;){var t=ap;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ag(4,t)}catch(e){iG(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(e){iG(t,a,e)}}var i=t.return;try{aC(t)}catch(e){iG(t,i,e)}break;case 5:var l=t.return;try{aC(t)}catch(e){iG(t,l,e)}}}catch(e){iG(t,t.return,e)}if(t===e){ap=null;break}var o=t.sibling;if(null!==o){o.return=t.return,ap=o;break}ap=t.return}}var aD=0,aG=1,aT=2,a_=3,aH=4;if("function"==typeof Symbol&&Symbol.for){var aP=Symbol.for;aD=aP("selector.component"),aG=aP("selector.has_pseudo_class"),aT=aP("selector.role"),a_=aP("selector.test_id"),aH=aP("selector.text")}function aL(e){var t=$(e);if(null!=t){if("string"!=typeof t.memoizedProps["data-testname"])throw Error(f(364));return t}if(null===(e=el(e)))throw Error(f(362));return e.stateNode.current}function ak(e,t){switch(t.$$typeof){case aD:if(e.type===t.value)return!0;break;case aG:e:{t=t.value,e=[e,0];for(var n=0;n<e.length;){var r=e[n++],a=e[n++],i=t[a];if(5!==r.tag||!eu(r)){for(;null!=i&&ak(r,i);)i=t[++a];if(a===t.length){t=!0;break e}for(r=r.child;null!==r;)e.push(r,a),r=r.sibling}}t=!1}return t;case aT:if(5===e.tag&&ec(e.stateNode,t.value))return!0;break;case aH:if((5===e.tag||6===e.tag)&&null!==(e=es(e))&&0<=e.indexOf(t.value))return!0;break;case a_:if(5===e.tag&&"string"==typeof(e=e.memoizedProps["data-testname"])&&e.toLowerCase()===t.value.toLowerCase())return!0;break;default:throw Error(f(365))}return!1}function aJ(e){switch(e.$$typeof){case aD:return"<"+(R(e.value)||"Unknown")+">";case aG:return":has("+(aJ(e)||"")+")";case aT:return'[role="'+e.value+'"]';case aH:return'"'+e.value+'"';case a_:return'[data-testname="'+e.value+'"]';default:throw Error(f(365))}}function aO(e,t){var n=[];e=[e,0];for(var r=0;r<e.length;){var a=e[r++],i=e[r++],l=t[i];if(5!==a.tag||!eu(a)){for(;null!=l&&ak(a,l);)l=t[++i];if(i===t.length)n.push(a);else for(a=a.child;null!==a;)e.push(a,i),a=a.sibling}}return n}function aU(e,t){if(!ei)throw Error(f(363));e=aO(e=aL(e),t),t=[],e=Array.from(e);for(var n=0;n<e.length;){var r=e[n++];if(5===r.tag)eu(r)||t.push(r.stateNode);else for(r=r.child;null!==r;)e.push(r),r=r.sibling}return t}var aN=Math.ceil,aK=d.ReactCurrentDispatcher,aQ=d.ReactCurrentOwner,az=d.ReactCurrentBatchConfig,aj=0,aX=null,aY=null,aW=0,aV=0,aZ=e5(0),aq=0,a$=null,a0=0,a1=0,a9=0,a2=null,a3=null,a8=0,a4=1/0;function a6(){a4=tx()+500}var a5=!1,a7=null,ie=null,it=!1,ir=null,ia=0,ii=0,il=null,io=-1,is=0;function iu(){return 0!=(6&aj)?tx():-1!==io?io:io=tx()}function ic(e){return 0==(1&e.mode)?1:0!=(2&aj)&&0!==aW?aW&-aW:null!==tU.transition?(0===is&&(e=tm,0==(4194240&(tm<<=1))&&(tm=64),is=e),is):0!==(e=tE)?e:et()}function id(e,t,n){if(50<ii)throw ii=0,il=null,Error(f(185));var r=ip(e,t);return null===r?null:(tb(r,t,n),(0==(2&aj)||r!==aX)&&(r===aX&&(0==(2&aj)&&(a1|=t),4===aq&&ig(r,aW)),ih(r,n),1===t&&0===aj&&0==(1&e.mode)&&(a6(),tk&&tO())),r)}function ip(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}function ih(e,t){var n,r,a,i=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-td(i),o=1<<l,s=a[l];-1===s?(0==(o&n)||0!=(o&r))&&(a[l]=function(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return -1}}(o,t)):s<=t&&(e.expiredLanes|=o),i&=~o}}(e,t);var l=tg(e,e===aX?aW:0);if(0===l)null!==i&&tS(i),e.callbackNode=null,e.callbackPriority=0;else if(t=l&-l,e.callbackPriority!==t){if(null!=i&&tS(i),1===t){0===e.tag&&(tk=!0),n=iC.bind(null,e),null===tL?tL=[n]:tL.push(n),er?ea(function(){0===aj&&tO()}):tF(tw,tO),i=null}else{switch(tM(l)){case 1:i=tw;break;case 4:i=tD;break;case 16:default:i=tG;break;case 536870912:i=tT}r=i,a=im.bind(null,e),i=tF(r,a)}e.callbackPriority=t,e.callbackNode=i}}function im(e,t){if(io=-1,is=0,0!=(6&aj))throw Error(f(327));var n=e.callbackNode;if(iw()&&e.callbackNode!==n)return null;var r=tg(e,e===aX?aW:0);if(0===r)return null;if(0!=(30&r)||0!=(r&e.expiredLanes)||t)t=iS(e,r);else{t=r;var a=aj;aj|=2;var i=iM();for((aX!==e||aW!==t)&&(a6(),iy(e,t));;)try{!function(){for(;null!==aY&&!tI();)iI(aY)}();break}catch(t){iE(e,t)}tY(),aK.current=i,aj=a,null!==aY?t=0:(aX=null,aW=0,t=aq)}if(0!==t){if(2===t&&0!==(a=tC(e))&&(r=a,t=iA(e,a)),1===t)throw n=a$,iy(e,0),ig(e,r),ih(e,tx()),n;if(6===t)ig(e,r);else{if(a=e.current.alternate,0==(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!tP(i(),a))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=iS(e,r))&&0!==(i=tC(e))&&(r=i,t=iA(e,i)),1===t))throw n=a$,iy(e,0),ig(e,r),ih(e,tx()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(f(345));case 2:case 5:ix(e,a3);break;case 3:if(ig(e,r),(130023424&r)===r&&10<(t=a8+500-tx())){if(0!==tg(e,0))break;if(((a=e.suspendedLanes)&r)!==r){iu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=j(ix.bind(null,e,a3),t);break}ix(e,a3);break;case 4:if(ig(e,r),(4194240&r)===r)break;for(a=-1,t=e.eventTimes;0<r;){var l=31-td(r);i=1<<l,(l=t[l])>a&&(a=l),r&=~i}if(r=a,10<(r=(120>(r=tx()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*aN(r/1960))-r)){e.timeoutHandle=j(ix.bind(null,e,a3),r);break}ix(e,a3);break;default:throw Error(f(329))}}}return ih(e,tx()),e.callbackNode===n?im.bind(null,e):null}function iA(e,t){var n=a2;return e.current.memoizedState.isDehydrated&&(iy(e,t).flags|=256),2!==(e=iS(e,t))&&(t=a3,a3=n,null!==t&&iB(t)),e}function iB(e){null===a3?a3=e:a3.push.apply(a3,e)}function ig(e,t){for(t&=~a9,t&=~a1,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-td(t),r=1<<n;e[n]=-1,t&=~r}}function iC(e){if(0!=(6&aj))throw Error(f(327));iw();var t=tg(e,0);if(0==(1&t))return ih(e,tx()),null;var n=iS(e,t);if(0!==e.tag&&2===n){var r=tC(e);0!==r&&(t=r,n=iA(e,r))}if(1===n)throw n=a$,iy(e,0),ig(e,t),ih(e,tx()),n;if(6===n)throw Error(f(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ix(e,a3),ih(e,tx()),null}function iv(e){null!==ir&&0===ir.tag&&0==(6&aj)&&iw();var t=aj;aj|=1;var n=az.transition,r=tE;try{if(az.transition=null,tE=1,e)return e()}finally{tE=r,az.transition=n,0==(6&(aj=t))&&tO()}}function ib(){aV=aZ.current,e7(aZ)}function iy(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==Y&&(e.timeoutHandle=Y,X(n)),null!==aY)for(n=aY.return;null!==n;){var r=n;switch(nC(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&to();break;case 3:nz(),e7(tr),e7(tn),nZ();break;case 5:nX(r);break;case 4:nz();break;case 13:case 19:e7(nY);break;case 10:tV(r.type._context);break;case 22:case 23:ib()}n=n.return}if(aX=e,aY=e=iO(e.current,null),aW=aV=t,aq=0,a$=null,a9=a1=a0=0,a3=a2=null,null!==t0){for(t=0;t<t0.length;t++)if(null!==(r=(n=t0[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var l=i.next;i.next=a,r.next=l}n.pending=r}t0=null}return e}function iE(e,t){for(;;){var n=aY;try{if(tY(),nq.current=rH,n3){for(var r=n1.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}n3=!1}if(n0=0,n2=n9=n1=null,n8=!1,n4=0,aQ.current=null,null===n||null===n.return){aq=1,a$=t,aY=null;break}e:{var i=e,l=n.return,o=n,s=t;if(t=aW,o.flags|=32768,null!==s&&"object"==typeof s&&"function"==typeof s.then){var u=s,c=o,d=c.tag;if(0==(1&c.mode)&&(0===d||11===d||15===d)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=rz(l);if(null!==h){h.flags&=-257,rj(h,l,o,i,t),1&h.mode&&rQ(i,u,t),t=h,s=u;var m=t.updateQueue;if(null===m){var A=new Set;A.add(s),t.updateQueue=A}else m.add(s);break e}if(0==(1&t)){rQ(i,u,t),iF();break e}s=Error(f(426))}else if(ny&&1&o.mode){var B=rz(l);if(null!==B){0==(65536&B.flags)&&(B.flags|=256),rj(B,l,o,i,t),nG(s);break e}}i=s,4!==aq&&(aq=2),null===a2?a2=[i]:a2.push(i),s=rJ(s,o),o=l;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var g=rN(o,s,t);t6(o,g);break e;case 1:i=s;var C=o.type,v=o.stateNode;if(0==(128&o.flags)&&("function"==typeof C.getDerivedStateFromError||null!==v&&"function"==typeof v.componentDidCatch&&(null===ie||!ie.has(v)))){o.flags|=65536,t&=-t,o.lanes|=t;var b=rK(o,i,t);t6(o,b);break e}}o=o.return}while(null!==o)}iR(n)}catch(e){t=e,aY===n&&null!==n&&(aY=n=n.return);continue}break}}function iM(){var e=aK.current;return aK.current=rH,null===e?rH:e}function iF(){(0===aq||3===aq||2===aq)&&(aq=4),null===aX||0==(268435455&a0)&&0==(268435455&a1)||ig(aX,aW)}function iS(e,t){var n=aj;aj|=2;var r=iM();for(aX===e&&aW===t||iy(e,t);;)try{!function(){for(;null!==aY;)iI(aY)}();break}catch(t){iE(e,t)}if(tY(),aj=n,aK.current=r,null!==aY)throw Error(f(261));return aX=null,aW=0,aq}function iI(e){var t=l(e.alternate,e,aV);e.memoizedProps=e.pendingProps,null===t?iR(e):aY=t,aQ.current=null}function iR(e){var n=e;do{var l=n.alternate;if(e=n.return,0==(32768&n.flags)){if(null!==(l=function(e,n,l){var o=n.pendingProps;switch(nC(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rZ(n),null;case 1:case 17:return tl(n.type)&&to(),rZ(n),null;case 3:return o=n.stateNode,nz(),e7(tr),e7(tn),nZ(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(null===e||null===e.child)&&(nw(n)?rX(n):null===e||e.memoizedState.isDehydrated&&0==(256&n.flags)||(n.flags|=1024,null!==nM&&(iB(nM),nM=null))),r(e,n),rZ(n),null;case 5:nX(n),l=nK(nN.current);var s=n.type;if(null!==e&&null!=n.stateNode)a(e,n,s,o,l),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!o){if(null===n.stateNode)throw Error(f(166));return rZ(n),null}if(e=nK(nO.current),nw(n)){if(!q)throw Error(f(175));e=ez(n.stateNode,n.type,n.memoizedProps,l,e,n,!nE),n.updateQueue=e,null!==e&&rX(n)}else{var u=O(s,o,l,e,n);t(u,n,!1,!1),n.stateNode=u,N(u,s,o,l,e)&&rX(n)}null!==n.ref&&(n.flags|=512,n.flags|=2097152)}return rZ(n),null;case 6:if(e&&null!=n.stateNode)i(e,n,e.memoizedProps,o);else{if("string"!=typeof o&&null===n.stateNode)throw Error(f(166));if(e=nK(nN.current),l=nK(nO.current),nw(n)){if(!q)throw Error(f(176));if(e=n.stateNode,o=n.memoizedProps,(l=ej(e,o,n,!nE))&&null!==(s=nv))switch(u=0!=(1&s.mode),s.tag){case 3:e0(s.stateNode.containerInfo,e,o,u);break;case 5:e1(s.type,s.memoizedProps,s.stateNode,e,o,u)}l&&rX(n)}else n.stateNode=z(o,e,l,n)}return rZ(n),null;case 13:if(e7(nY),o=n.memoizedState,ny&&null!==nb&&0!=(1&n.mode)&&0==(128&n.flags)){for(e=nb;e;)e=eU(e);return nD(),n.flags|=98560,n}if(null!==o&&null!==o.dehydrated){if(o=nw(n),null===e){if(!o)throw Error(f(318));if(!q)throw Error(f(344));if(!(e=null!==(e=n.memoizedState)?e.dehydrated:null))throw Error(f(317));eX(e,n)}else nD(),0==(128&n.flags)&&(n.memoizedState=null),n.flags|=4;return rZ(n),null}if(null!==nM&&(iB(nM),nM=null),0!=(128&n.flags))return n.lanes=l,n;return o=null!==o,l=!1,null===e?nw(n):l=null!==e.memoizedState,o&&!l&&(n.child.flags|=8192,0!=(1&n.mode)&&(null===e||0!=(1&nY.current)?0===aq&&(aq=3):iF())),null!==n.updateQueue&&(n.flags|=4),rZ(n),null;case 4:return nz(),r(e,n),null===e&&ee(n.stateNode.containerInfo),rZ(n),null;case 10:return tV(n.type._context),rZ(n),null;case 19:if(e7(nY),null===(s=n.memoizedState))return rZ(n),null;if(o=0!=(128&n.flags),null===(u=s.rendering)){if(o)rV(s,!1);else{if(0!==aq||null!==e&&0!=(128&e.flags))for(e=n.child;null!==e;){if(null!==(u=nW(e))){for(n.flags|=128,rV(s,!1),null!==(e=u.updateQueue)&&(n.updateQueue=e,n.flags|=4),n.subtreeFlags=0,e=l,o=n.child;null!==o;)l=o,s=e,l.flags&=14680066,null===(u=l.alternate)?(l.childLanes=0,l.lanes=s,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=u.childLanes,l.lanes=u.lanes,l.child=u.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=u.memoizedProps,l.memoizedState=u.memoizedState,l.updateQueue=u.updateQueue,l.type=u.type,s=u.dependencies,l.dependencies=null===s?null:{lanes:s.lanes,firstContext:s.firstContext}),o=o.sibling;return te(nY,1&nY.current|2),n.child}e=e.sibling}null!==s.tail&&tx()>a4&&(n.flags|=128,o=!0,rV(s,!1),n.lanes=4194304)}}else{if(!o){if(null!==(e=nW(u))){if(n.flags|=128,o=!0,null!==(e=e.updateQueue)&&(n.updateQueue=e,n.flags|=4),rV(s,!0),null===s.tail&&"hidden"===s.tailMode&&!u.alternate&&!ny)return rZ(n),null}else 2*tx()-s.renderingStartTime>a4&&1073741824!==l&&(n.flags|=128,o=!0,rV(s,!1),n.lanes=4194304)}s.isBackwards?(u.sibling=n.child,n.child=u):(null!==(e=s.last)?e.sibling=u:n.child=u,s.last=u)}if(null!==s.tail)return n=s.tail,s.rendering=n,s.tail=n.sibling,s.renderingStartTime=tx(),n.sibling=null,e=nY.current,te(nY,o?1&e|2:1&e),n;return rZ(n),null;case 22:case 23:return ib(),o=null!==n.memoizedState,null!==e&&null!==e.memoizedState!==o&&(n.flags|=8192),o&&0!=(1&n.mode)?0!=(1073741824&aV)&&(rZ(n),V&&6&n.subtreeFlags&&(n.flags|=8192)):rZ(n),null;case 24:case 25:return null}throw Error(f(156,n.tag))}(l,n,aV))){aY=l;return}}else{if(null!==(l=function(e,t){switch(nC(t),t.tag){case 1:return tl(t.type)&&to(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return nz(),e7(tr),e7(tn),nZ(),0!=(65536&(e=t.flags))&&0==(128&e)?(t.flags=-65537&e|128,t):null;case 5:return nX(t),null;case 13:if(e7(nY),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(f(340));nD()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return e7(nY),null;case 4:return nz(),null;case 10:return tV(t.type._context),null;case 22:case 23:return ib(),null;default:return null}}(l,n))){l.flags&=32767,aY=l;return}if(null!==e)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{aq=6,aY=null;return}}if(null!==(n=n.sibling)){aY=n;return}aY=n=e}while(null!==n);0===aq&&(aq=5)}function ix(e,t){var n=tE,r=az.transition;try{az.transition=null,tE=1,function(e,t,n){do iw();while(null!==ir);if(0!=(6&aj))throw Error(f(327));var r=e.finishedWork,a=e.finishedLanes;if(null!==r){if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(f(177));e.callbackNode=null,e.callbackPriority=0;var i=r.lanes|r.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-td(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,i),e===aX&&(aY=aX=null,aW=0),0==(2064&r.subtreeFlags)&&0==(2064&r.flags)||it||(it=!0,l=tG,o=function(){return iw(),null},tF(l,o)),i=0!=(15990&r.flags),0!=(15990&r.subtreeFlags)||i){i=az.transition,az.transition=null;var l,o,s,u,c=tE;tE=1;var d=aj;aj|=4,aQ.current=null,function(e,t){for(k(e.containerInfo),ap=t;null!==ap;)if(t=(e=ap).child,0!=(1028&e.subtreeFlags)&&null!==t)t.return=e,ap=t;else for(;null!==ap;){e=ap;try{var n=e.alternate;if(0!=(1024&e.flags))switch(e.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==n){var r=n.memoizedProps,a=n.memoizedState,i=e.stateNode,l=i.getSnapshotBeforeUpdate(e.elementType===e.type?r:tK(e.type,r),a);i.__reactInternalSnapshotBeforeUpdate=l}break;case 3:V&&eI(e.stateNode.containerInfo);break;default:throw Error(f(163))}}catch(t){iG(e,e.return,t)}if(null!==(t=e.sibling)){t.return=e.return,ap=t;break}ap=e.return}n=aA,aA=!1}(e,r),function(e,t){for(ap=t;null!==ap;){var n=(t=ap).deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e;V?aF(i,a,t):ab(i,a,t);var l=a.alternate;null!==l&&(l.return=null),a.return=null}catch(e){iG(a,t,e)}}if(n=t.child,0!=(12854&t.subtreeFlags)&&null!==n)n.return=t,ap=n;else for(;null!==ap;){t=ap;try{var o=t.flags;if(32&o&&V&&ey(t.stateNode),512&o){var s=t.alternate;if(null!==s){var u=s.ref;null!==u&&("function"==typeof u?u(null):u.current=null)}}if(8192&o)switch(t.tag){case 13:if(null!==t.memoizedState){var c=t.alternate;(null===c||null===c.memoizedState)&&(a8=tx())}break;case 22:var f=null!==t.memoizedState,d=t.alternate,p=null!==d&&null!==d.memoizedState;if(n=t,V){e:if(r=n,a=f,i=null,V)for(var h=r;;){if(5===h.tag){if(null===i){i=h;var m=h.stateNode;a?eE(m):eF(h.stateNode,h.memoizedProps)}}else if(6===h.tag){if(null===i){var A=h.stateNode;a?eM(A):eS(A,h.memoizedProps)}}else if((22!==h.tag&&23!==h.tag||null===h.memoizedState||h===r)&&null!==h.child){h.child.return=h,h=h.child;continue}if(h===r)break;for(;null===h.sibling;){if(null===h.return||h.return===r)break e;i===h&&(i=null),h=h.return}i===h&&(i=null),h.sibling.return=h.return,h=h.sibling}}if(f&&!p&&0!=(1&n.mode)){ap=n;for(var B=n.child;null!==B;){for(n=ap=B;null!==ap;){var g=(r=ap).child;switch(r.tag){case 0:case 11:case 14:case 15:aB(4,r,r.return);break;case 1:ah(r,r.return);var C=r.stateNode;if("function"==typeof C.componentWillUnmount){var v=r.return;try{C.props=r.memoizedProps,C.state=r.memoizedState,C.componentWillUnmount()}catch(e){iG(r,v,e)}}break;case 5:ah(r,r.return);break;case 22:if(null!==r.memoizedState){ax(n);continue}}null!==g?(g.return=r,ap=g):ax(n)}B=B.sibling}}}switch(4102&o){case 2:aM(t),t.flags&=-3;break;case 6:aM(t),t.flags&=-3,aS(t.alternate,t);break;case 4096:t.flags&=-4097;break;case 4100:t.flags&=-4097,aS(t.alternate,t);break;case 4:aS(t.alternate,t)}}catch(e){iG(t,t.return,e)}if(null!==(n=t.sibling)){n.return=t.return,ap=n;break}ap=t.return}}}(e,r,a),J(e.containerInfo),e.current=r,s=r,u=e,ap=s,function e(t,n,r){for(var a=0!=(1&t.mode);null!==ap;){var i=ap,l=i.child;if(22===i.tag&&a){var o=null!==i.memoizedState||ac;if(!o){var s=i.alternate,u=null!==s&&null!==s.memoizedState||af;s=ac;var c=af;if(ac=o,(af=u)&&!c)for(ap=i;null!==ap;)u=(o=ap).child,22===o.tag&&null!==o.memoizedState?aw(i):null!==u?(u.return=o,ap=u):aw(i);for(;null!==l;)ap=l,e(l,n,r),l=l.sibling;ap=i,ac=s,af=c}aR(t,n,r)}else 0!=(8772&i.subtreeFlags)&&null!==l?(l.return=i,ap=l):aR(t,n,r)}}(s,u,a),tR(),aj=d,tE=c,az.transition=i}else e.current=r;if(it&&(it=!1,ir=e,ia=a),0===(i=e.pendingLanes)&&(ie=null),function(e){if(tH&&"function"==typeof tH.onCommitFiberRoot)try{tH.onCommitFiberRoot(t_,e,void 0,128==(128&e.current.flags))}catch(e){}}(r.stateNode,n),ih(e,tx()),null!==t)for(n=e.onRecoverableError,r=0;r<t.length;r++)n(t[r]);if(a5)throw a5=!1,e=a7,a7=null,e;0!=(1&ia)&&0!==e.tag&&iw(),0!=(1&(i=e.pendingLanes))?e===il?ii++:(ii=0,il=e):ii=0,tO()}}(e,t,n)}finally{az.transition=r,tE=n}return null}function iw(){if(null!==ir){var e=tM(ia),t=az.transition,n=tE;try{if(az.transition=null,tE=16>e?16:e,null===ir)var r=!1;else{if(e=ir,ir=null,ia=0,0!=(6&aj))throw Error(f(331));var a=aj;for(aj|=4,ap=e.current;null!==ap;){var i=ap,l=i.child;if(0!=(16&ap.flags)){var o=i.deletions;if(null!==o){for(var s=0;s<o.length;s++){var u=o[s];for(ap=u;null!==ap;){var c=ap;switch(c.tag){case 0:case 11:case 15:aB(8,c,i)}var d=c.child;if(null!==d)d.return=c,ap=d;else for(;null!==ap;){var p=(c=ap).sibling,h=c.return;if(!function e(t){var n=t.alternate;null!==n&&(t.alternate=null,e(n)),t.child=null,t.deletions=null,t.sibling=null,5===t.tag&&null!==(n=t.stateNode)&&en(n),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}(c),c===u){ap=null;break}if(null!==p){p.return=h,ap=p;break}ap=h}}}var m=i.alternate;if(null!==m){var A=m.child;if(null!==A){m.child=null;do{var B=A.sibling;A.sibling=null,A=B}while(null!==A)}}ap=i}}if(0!=(2064&i.subtreeFlags)&&null!==l)l.return=i,ap=l;else t:for(;null!==ap;){if(i=ap,0!=(2048&i.flags))switch(i.tag){case 0:case 11:case 15:aB(9,i,i.return)}var g=i.sibling;if(null!==g){g.return=i.return,ap=g;break t}ap=i.return}}var C=e.current;for(ap=C;null!==ap;){var v=(l=ap).child;if(0!=(2064&l.subtreeFlags)&&null!==v)v.return=l,ap=v;else t:for(l=C;null!==ap;){if(o=ap,0!=(2048&o.flags))try{switch(o.tag){case 0:case 11:case 15:ag(9,o)}}catch(e){iG(o,o.return,e)}if(o===l){ap=null;break t}var b=o.sibling;if(null!==b){b.return=o.return,ap=b;break t}ap=o.return}}if(aj=a,tO(),tH&&"function"==typeof tH.onPostCommitFiberRoot)try{tH.onPostCommitFiberRoot(t_,e)}catch(e){}r=!0}return r}finally{tE=n,az.transition=t}}return!1}function iD(e,t,n){t=rJ(n,t),t=rN(e,t,1),t8(e,t),t=iu(),null!==(e=ip(e,1))&&(tb(e,1,t),ih(e,t))}function iG(e,t,n){if(3===e.tag)iD(e,e,n);else for(;null!==t;){if(3===t.tag){iD(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===ie||!ie.has(r))){e=rJ(n,e),e=rK(t,e,1),t8(t,e),e=iu(),null!==(t=ip(t,1))&&(tb(t,1,e),ih(t,e));break}}t=t.return}}function iT(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=iu(),e.pingedLanes|=e.suspendedLanes&n,aX===e&&(aW&n)===n&&(4===aq||3===aq&&(130023424&aW)===aW&&500>tx()-a8?iy(e,0):a9|=n),ih(e,t)}function i_(e,t){0===t&&(0==(1&e.mode)?t=1:(t=tA,0==(130023424&(tA<<=1))&&(tA=4194304)));var n=iu();null!==(e=ip(e,t))&&(tb(e,t,n),ih(e,n))}function iH(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),i_(e,n)}function iP(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(f(314))}null!==r&&r.delete(t),i_(e,n)}function iL(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ik(e,t,n,r){return new iL(e,t,n,r)}function iJ(e){return!(!(e=e.prototype)||!e.isReactComponent)}function iO(e,t){var n=e.alternate;return null===n?((n=ik(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function iU(e,t,n,r,a,i){var l=2;if(r=e,"function"==typeof e)iJ(e)&&(l=1);else if("string"==typeof e)l=5;else e:switch(e){case m:return iN(n.children,a,i,t);case A:l=8,a|=8;break;case B:return(e=ik(12,n,t,2|a)).elementType=B,e.lanes=i,e;case b:return(e=ik(13,n,t,a)).elementType=b,e.lanes=i,e;case y:return(e=ik(19,n,t,a)).elementType=y,e.lanes=i,e;case F:return iK(n,a,i,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case g:l=10;break e;case C:l=9;break e;case v:l=11;break e;case E:l=14;break e;case M:l=16,r=null;break e}throw Error(f(130,null==e?e:typeof e,""))}return(t=ik(l,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function iN(e,t,n,r){return(e=ik(7,e,r,t)).lanes=n,e}function iK(e,t,n,r){return(e=ik(22,e,r,t)).elementType=F,e.lanes=n,e.stateNode={},e}function iQ(e,t,n){return(e=ik(6,e,null,t)).lanes=n,e}function iz(e,t,n){return(t=ik(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ij(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=Y,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=tv(0),this.expirationTimes=tv(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=tv(0),this.identifierPrefix=r,this.onRecoverableError=a,q&&(this.mutableSourceEagerHydrationData=null)}function iX(e,t,n,r,a,i,l,o,s){return e=new ij(e,t,n,o,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=ik(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null},t9(i),e}function iY(e){if(!e)return tt;e=e._reactInternals;e:{if(x(e)!==e||1!==e.tag)throw Error(f(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tl(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(f(171))}if(1===e.tag){var n=e.type;if(tl(n))return tu(e,n,t)}return t}function iW(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(f(188));throw Error(f(268,e=Object.keys(e).join(",")))}return null===(e=G(t))?null:e.stateNode}function iV(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function iZ(e,t){iV(e,t),(e=e.alternate)&&iV(e,t)}function iq(e){return null===(e=G(e))?null:e.stateNode}function i$(){return null}return l=function(e,t,n){if(null!==e){if(e.memoizedProps!==t.pendingProps||tr.current)r$=!0;else{if(0==(e.lanes&n)&&0==(128&t.flags))return r$=!1,function(e,t,n){switch(t.tag){case 3:r7(t),nD();break;case 5:nj(t);break;case 1:tl(t.type)&&tc(t);break;case 4:nQ(t,t.stateNode.containerInfo);break;case 10:tW(t,t.type._context,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r){if(null!==r.dehydrated)return te(nY,1&nY.current),t.flags|=128,null;if(0!=(n&t.child.childLanes))return ar(e,t,n);return te(nY,1&nY.current),null!==(e=au(e,t,n))?e.sibling:null}te(nY,1&nY.current);break;case 19:if(r=0!=(n&t.childLanes),0!=(128&e.flags)){if(r)return as(e,t,n);t.flags|=128}var a=t.memoizedState;if(null!==a&&(a.rendering=null,a.tail=null,a.lastEffect=null),te(nY,nY.current),!r)return null;break;case 22:case 23:return t.lanes=0,r3(e,t,n)}return au(e,t,n)}(e,t,n);r$=0!=(131072&e.flags)}}else r$=!1,ny&&0!=(1048576&t.flags)&&nB(t,nc,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps;var a=ti(t,tn.current);tq(t,n),a=re(null,t,r,e,a,n);var i=rt();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tl(r)?(i=!0,tc(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,t9(t),a.updater=nn,t.stateNode=a,a._reactInternals=t,nl(t,r,e,n),t=r5(null,t,r,!0,i,n)):(t.tag=0,ny&&i&&ng(t),r0(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return iJ(e)?1:0;if(null!=e){if((e=e.$$typeof)===v)return 11;if(e===E)return 14}return 2}(r),e=tK(r,e),a){case 0:t=r4(null,t,r,e,n);break e;case 1:t=r6(null,t,r,e,n);break e;case 11:t=r1(null,t,r,e,n);break e;case 14:t=r9(null,t,r,tK(r.type,e),n);break e}throw Error(f(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:tK(r,a),r4(e,t,r,a,n);case 1:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:tK(r,a),r6(e,t,r,a,n);case 3:e:{if(r7(t),null===e)throw Error(f(387));r=t.pendingProps,a=(i=t.memoizedState).element,t2(e,t),t5(t,r,null,n);var l=t.memoizedState;if(r=l.element,q&&i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:l.cache,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=ae(e,t,r,n,a=Error(f(423)));break e}if(r!==a){t=ae(e,t,r,n,a=Error(f(424)));break e}for(q&&(nb=eK(t.stateNode.containerInfo),nv=t,ny=!0,nM=null,nE=!1),n=nk(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(nD(),r===a){t=au(e,t,n);break e}r0(e,t,r,n)}t=t.child}return t;case 5:return nj(t),null===e&&nR(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,l=a.children,Q(r,a)?l=null:null!==i&&Q(r,i)&&(t.flags|=32),r8(e,t),r0(e,t,l,n),t.child;case 6:return null===e&&nR(t),null;case 13:return ar(e,t,n);case 4:return nQ(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=nL(t,null,r,n):r0(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:tK(r,a),r1(e,t,r,a,n);case 7:return r0(e,t,t.pendingProps,n),t.child;case 8:case 12:return r0(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,l=a.value,tW(t,r,l),null!==i){if(tP(i.value,l)){if(i.children===a.children&&!tr.current){t=au(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var o=i.dependencies;if(null!==o){l=i.child;for(var s=o.firstContext;null!==s;){if(s.context===r){if(1===i.tag){(s=t3(-1,n&-n)).tag=2;var u=i.updateQueue;if(null!==u){var c=(u=u.shared).pending;null===c?s.next=s:(s.next=c.next,c.next=s),u.pending=s}}i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),tZ(i.return,n,t),o.lanes|=n;break}s=s.next}}else if(10===i.tag)l=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(l=i.return))throw Error(f(341));l.lanes|=n,null!==(o=l.alternate)&&(o.lanes|=n),tZ(l,n,t),l=i.sibling}else l=i.child;if(null!==l)l.return=i;else for(l=i;null!==l;){if(l===t){l=null;break}if(null!==(i=l.sibling)){i.return=l.return,l=i;break}l=l.return}i=l}}r0(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,tq(t,n),a=t$(a),r=r(a),t.flags|=1,r0(e,t,r,n),t.child;case 14:return a=tK(r=t.type,t.pendingProps),a=tK(r.type,a),r9(e,t,r,a,n);case 15:return r2(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:tK(r,a),null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2),t.tag=1,tl(r)?(e=!0,tc(t)):e=!1,tq(t,n),na(t,r,a),nl(t,r,a,n),r5(null,t,r,!0,e,n);case 19:return as(e,t,n);case 22:return r3(e,t,n)}throw Error(f(156,t.tag))},o.attemptContinuousHydration=function(e){13===e.tag&&(id(e,134217728,iu()),iZ(e,134217728))},o.attemptHydrationAtCurrentPriority=function(e){if(13===e.tag){var t=iu(),n=ic(e);id(e,n,t),iZ(e,n)}},o.attemptSynchronousHydration=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=tB(t.pendingLanes);0!==n&&(ty(t,1|n),ih(t,tx()),0==(6&aj)&&(a6(),tO()))}break;case 13:var r=iu();iv(function(){return id(e,1,r)}),iZ(e,1)}},o.batchedUpdates=function(e,t){var n=aj;aj|=1;try{return e(t)}finally{0===(aj=n)&&(a6(),tk&&tO())}},o.createComponentSelector=function(e){return{$$typeof:aD,value:e}},o.createContainer=function(e,t,n,r,a,i,l){return iX(e,t,!1,null,n,r,a,i,l)},o.createHasPseudoClassSelector=function(e){return{$$typeof:aG,value:e}},o.createHydrationContainer=function(e,t,n,r,a,i,l,o,s){return(e=iX(n,r,!0,e,a,i,l,o,s)).context=iY(null),n=e.current,(i=t3(r=iu(),a=ic(n))).callback=null!=t?t:null,t8(n,i),e.current.lanes=a,tb(e,a,r),ih(e,r),e},o.createPortal=function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:h,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}},o.createRoleSelector=function(e){return{$$typeof:aT,value:e}},o.createTestNameSelector=function(e){return{$$typeof:a_,value:e}},o.createTextSelector=function(e){return{$$typeof:aH,value:e}},o.deferredUpdates=function(e){var t=tE,n=az.transition;try{return az.transition=null,tE=16,e()}finally{tE=t,az.transition=n}},o.discreteUpdates=function(e,t,n,r,a){var i=tE,l=az.transition;try{return az.transition=null,tE=1,e(t,n,r,a)}finally{tE=i,az.transition=l,0===aj&&a6()}},o.findAllNodes=aU,o.findBoundingRects=function(e,t){if(!ei)throw Error(f(363));t=aU(e,t),e=[];for(var n=0;n<t.length;n++)e.push(eo(t[n]));for(t=e.length-1;0<t;t--){n=e[t];for(var r=n.x,a=r+n.width,i=n.y,l=i+n.height,o=t-1;0<=o;o--)if(t!==o){var s=e[o],u=s.x,c=u+s.width,d=s.y,p=d+s.height;if(r>=u&&i>=d&&a<=c&&l<=p){e.splice(t,1);break}if(r!==u||n.width!==s.width||p<i||d>l){if(!(i!==d||n.height!==s.height||c<r||u>a)){u>r&&(s.width+=u-r,s.x=r),c<a&&(s.width=a-u),e.splice(t,1);break}}else{d>i&&(s.height+=d-i,s.y=i),p<l&&(s.height=l-d),e.splice(t,1);break}}}return e},o.findHostInstance=iW,o.findHostInstanceWithNoPortals=function(e){return null===(e=null!==(e=D(e))?function e(t){if(5===t.tag||6===t.tag)return t;for(t=t.child;null!==t;){if(4!==t.tag){var n=e(t);if(null!==n)return n}t=t.sibling}return null}(e):null)?null:e.stateNode},o.findHostInstanceWithWarning=function(e){return iW(e)},o.flushControlled=function(e){var t=aj;aj|=1;var n=az.transition,r=tE;try{az.transition=null,tE=1,e()}finally{tE=r,az.transition=n,0===(aj=t)&&(a6(),tO())}},o.flushPassiveEffects=iw,o.flushSync=iv,o.focusWithin=function(e,t){if(!ei)throw Error(f(363));for(t=Array.from(t=aO(e=aL(e),t)),e=0;e<t.length;){var n=t[e++];if(!eu(n)){if(5===n.tag&&ef(n.stateNode))return!0;for(n=n.child;null!==n;)t.push(n),n=n.sibling}}return!1},o.getCurrentUpdatePriority=function(){return tE},o.getFindAllNodesFailureDescription=function(e,t){if(!ei)throw Error(f(363));var n=0,r=[];e=[aL(e),0];for(var a=0;a<e.length;){var i=e[a++],l=e[a++],o=t[l];if((5!==i.tag||!eu(i))&&(ak(i,o)&&(r.push(aJ(o)),++l>n&&(n=l)),l<t.length))for(i=i.child;null!==i;)e.push(i,l),i=i.sibling}if(n<t.length){for(e=[];n<t.length;n++)e.push(aJ(t[n]));return"findAllNodes was able to match part of the selector:\n  "+r.join(" > ")+"\n\nNo matching component was found for:\n  "+e.join(" > ")}return null},o.getPublicRootInstance=function(e){return(e=e.current).child?5===e.child.tag?H(e.child.stateNode):e.child.stateNode:null},o.injectIntoDevTools=function(e){if(e={bundleType:e.bundleType,version:e.version,rendererPackageName:e.rendererPackageName,rendererConfig:e.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:d.ReactCurrentDispatcher,findHostInstanceByFiber:iq,findFiberByHostInstance:e.findFiberByHostInstance||i$,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.0.0-fc46dba67-20220329"},"undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)e=!1;else{var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)e=!0;else{try{t_=t.inject(e),tH=t}catch(e){}e=!!t.checkDCE}}return e},o.isAlreadyRendering=function(){return!1},o.observeVisibleRects=function(e,t,n,r){if(!ei)throw Error(f(363));var a=ed(e=aU(e,t),n,r).disconnect;return{disconnect:function(){a()}}},o.registerMutableSourceForHydration=function(e,t){var n=t._getVersion;n=n(t._source),null==e.mutableSourceEagerHydrationData?e.mutableSourceEagerHydrationData=[t,n]:e.mutableSourceEagerHydrationData.push(t,n)},o.runWithPriority=function(e,t){var n=tE;try{return tE=e,t()}finally{tE=n}},o.shouldError=function(){return null},o.shouldSuspend=function(){return!1},o.updateContainer=function(e,t,n,r){var a=t.current,i=iu(),l=ic(a);return n=iY(n),null===t.context?t.context=n:t.pendingContext=n,(t=t3(i,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),t8(a,t),null!==(e=id(a,l,i))&&t4(e,a,l),l},o}},2576:function(e,t,n){"use strict";e.exports=n(6511)},6525:function(e,t,n){"use strict";e.exports=n(7287)},8536:function(e,t,n){"use strict";var r=n(7294);t.Z=function(e,t){var n=(0,r.useState)(null),a=n[0],i=n[1];return(0,r.useEffect)(function(){if(e.current&&"function"==typeof IntersectionObserver){var n=new IntersectionObserver(function(e){i(e[0])},t);return n.observe(e.current),function(){i(null),n.disconnect()}}return function(){}},[e.current,t.threshold,t.root,t.rootMargin]),a}},53:function(e,t){"use strict";/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(0<i(a,t))e[r]=t,e[n]=a,n=r;else break e}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var o=2*(r+1)-1,s=e[o],u=o+1,c=e[u];if(0>i(s,n))u<a&&0>i(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[o]=n,r=o);else if(u<a&&0>i(c,n))e[r]=c,e[u]=n,r=u;else break e}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var l,o=performance;t.unstable_now=function(){return o.now()}}else{var s=Date,u=s.now();t.unstable_now=function(){return s.now()-u}}var c=[],f=[],d=1,p=null,h=3,m=!1,A=!1,B=!1,g="function"==typeof setTimeout?setTimeout:null,C="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var t=r(f);null!==t;){if(null===t.callback)a(f);else if(t.startTime<=e)a(f),t.sortIndex=t.expirationTime,n(c,t);else break;t=r(f)}}function y(e){if(B=!1,b(e),!A){if(null!==r(c))A=!0,T(E);else{var t=r(f);null!==t&&_(y,t.startTime-e)}}}function E(e,n){A=!1,B&&(B=!1,C(S),S=-1),m=!0;var i=h;try{for(b(n),p=r(c);null!==p&&(!(p.expirationTime>n)||e&&!x());){var l=p.callback;if("function"==typeof l){p.callback=null,h=p.priorityLevel;var o=l(p.expirationTime<=n);n=t.unstable_now(),"function"==typeof o?p.callback=o:p===r(c)&&a(c),b(n)}else a(c);p=r(c)}if(null!==p)var s=!0;else{var u=r(f);null!==u&&_(y,u.startTime-n),s=!1}return s}finally{p=null,h=i,m=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var M=!1,F=null,S=-1,I=5,R=-1;function x(){return!(t.unstable_now()-R<I)}function w(){if(null!==F){var e=t.unstable_now();R=e;var n=!0;try{n=F(!0,e)}finally{n?l():(M=!1,F=null)}}else M=!1}if("function"==typeof v)l=function(){v(w)};else if("undefined"!=typeof MessageChannel){var D=new MessageChannel,G=D.port2;D.port1.onmessage=w,l=function(){G.postMessage(null)}}else l=function(){g(w,0)};function T(e){F=e,M||(M=!0,l())}function _(e,n){S=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){A||m||(A=!0,T(E))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var l=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?l+i:l,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return o=i+o,e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:o,sortIndex:-1},i>l?(e.sortIndex=i,n(f,e),null===r(c)&&e===r(f)&&(B?(C(S),S=-1):B=!0,_(y,i-l))):(e.sortIndex=o,n(c,e),A||m||(A=!0,T(E))),e},t.unstable_shouldYield=x,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},3840:function(e,t,n){"use strict";e.exports=n(53)},8197:function(e,t,n){"use strict";n.d(t,{x:function(){return a}});var r=n(9477);class a extends r.DataTextureLoader{constructor(e){super(e),this.type=r.HalfFloatType}parse(e){let t=function(e,t){switch(e){case 1:console.error("THREE.RGBELoader Read Error: "+(t||""));break;case 2:console.error("THREE.RGBELoader Write Error: "+(t||""));break;case 3:console.error("THREE.RGBELoader Bad File Format: "+(t||""));break;default:console.error("THREE.RGBELoader: Error: "+(t||""))}return -1},n=function(e,t,n){t=t||1024;let r=e.pos,a=-1,i=0,l="",o=String.fromCharCode.apply(null,new Uint16Array(e.subarray(r,r+128)));for(;0>(a=o.indexOf("\n"))&&i<t&&r<e.byteLength;)l+=o,i+=o.length,r+=128,o+=String.fromCharCode.apply(null,new Uint16Array(e.subarray(r,r+128)));return -1<a&&(!1!==n&&(e.pos+=i+a+1),l+o.slice(0,a))},a=new Uint8Array(e);a.pos=0;let i=function(e){let r,a;let i=/^\s*GAMMA\s*=\s*(\d+(\.\d+)?)\s*$/,l=/^\s*EXPOSURE\s*=\s*(\d+(\.\d+)?)\s*$/,o=/^\s*FORMAT=(\S+)\s*$/,s=/^\s*\-Y\s+(\d+)\s+\+X\s+(\d+)\s*$/,u={valid:0,string:"",comments:"",programtype:"RGBE",format:"",gamma:1,exposure:1,width:0,height:0};if(e.pos>=e.byteLength||!(r=n(e)))return t(1,"no header found");if(!(a=r.match(/^#\?(\S+)/)))return t(3,"bad initial token");for(u.valid|=1,u.programtype=a[1],u.string+=r+"\n";!1!==(r=n(e));){if(u.string+=r+"\n","#"===r.charAt(0)){u.comments+=r+"\n";continue}if((a=r.match(i))&&(u.gamma=parseFloat(a[1])),(a=r.match(l))&&(u.exposure=parseFloat(a[1])),(a=r.match(o))&&(u.valid|=2,u.format=a[1]),(a=r.match(s))&&(u.valid|=4,u.height=parseInt(a[1],10),u.width=parseInt(a[2],10)),2&u.valid&&4&u.valid)break}return 2&u.valid?4&u.valid?u:t(3,"missing image size specifier"):t(3,"missing format specifier")}(a);if(-1!==i){let e=i.width,n=i.height,l=function(e,n,r){if(n<8||n>32767||2!==e[0]||2!==e[1]||128&e[2])return new Uint8Array(e);if(n!==(e[2]<<8|e[3]))return t(3,"wrong scanline width");let a=new Uint8Array(4*n*r);if(!a.length)return t(4,"unable to allocate buffer space");let i=0,l=0,o=4*n,s=new Uint8Array(4),u=new Uint8Array(o),c=r;for(;c>0&&l<e.byteLength;){if(l+4>e.byteLength)return t(1);if(s[0]=e[l++],s[1]=e[l++],s[2]=e[l++],s[3]=e[l++],2!=s[0]||2!=s[1]||(s[2]<<8|s[3])!=n)return t(3,"bad rgbe scanline format");let r=0,f;for(;r<o&&l<e.byteLength;){f=e[l++];let n=f>128;if(n&&(f-=128),0===f||r+f>o)return t(3,"bad scanline data");if(n){let t=e[l++];for(let e=0;e<f;e++)u[r++]=t}else u.set(e.subarray(l,l+f),r),r+=f,l+=f}for(let e=0;e<n;e++){let t=0;a[i]=u[e+t],t+=n,a[i+1]=u[e+t],t+=n,a[i+2]=u[e+t],t+=n,a[i+3]=u[e+t],i+=4}c--}return a}(a.subarray(a.pos),e,n);if(-1!==l){let t,a,o;switch(this.type){case r.FloatType:o=l.length/4;let s=new Float32Array(4*o);for(let e=0;e<o;e++)!function(e,t,n,r){let a=e[t+3],i=Math.pow(2,a-128)/255;n[r+0]=e[t+0]*i,n[r+1]=e[t+1]*i,n[r+2]=e[t+2]*i,n[r+3]=1}(l,4*e,s,4*e);t=s,a=r.FloatType;break;case r.HalfFloatType:o=l.length/4;let u=new Uint16Array(4*o);for(let e=0;e<o;e++)!function(e,t,n,a){let i=e[t+3],l=Math.pow(2,i-128)/255;n[a+0]=r.DataUtils.toHalfFloat(Math.min(e[t+0]*l,65504)),n[a+1]=r.DataUtils.toHalfFloat(Math.min(e[t+1]*l,65504)),n[a+2]=r.DataUtils.toHalfFloat(Math.min(e[t+2]*l,65504)),n[a+3]=r.DataUtils.toHalfFloat(1)}(l,4*e,u,4*e);t=u,a=r.HalfFloatType;break;default:console.error("THREE.RGBELoader: unsupported type: ",this.type)}return{width:e,height:n,data:t,header:i.string,gamma:i.gamma,exposure:i.exposure,type:a}}}return null}setDataType(e){return this.type=e,this}load(e,t,n,a){return super.load(e,function(e,n){switch(e.type){case r.FloatType:case r.HalfFloatType:e.encoding=r.LinearEncoding,e.minFilter=r.LinearFilter,e.magFilter=r.LinearFilter,e.generateMipmaps=!1,e.flipY=!0}t&&t(e,n)},n,a)}}},4671:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var r=n(7294);let a="undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),i=a?r.useEffect:r.useLayoutEffect;function l(e){let t="function"==typeof e?function(e){let t;let n=new Set,r=(e,r)=>{let a="function"==typeof e?e(t):e;if(a!==t){let e=t;t=r?a:Object.assign({},t,a),n.forEach(n=>n(t,e))}},a=()=>t,i=(e,r=a,i=Object.is)=>{console.warn("[DEPRECATED] Please use `subscribeWithSelector` middleware");let l=r(t);function o(){let n=r(t);if(!i(l,n)){let t=l;e(l=n,t)}}return n.add(o),()=>n.delete(o)},l=(e,t,r)=>t||r?i(e,t,r):(n.add(e),()=>n.delete(e)),o=()=>n.clear(),s={setState:r,getState:a,subscribe:l,destroy:o};return t=e(r,a,s),s}(e):e,n=(e=t.getState,n=Object.is)=>{let a;let[,l]=(0,r.useReducer)(e=>e+1,0),o=t.getState(),s=(0,r.useRef)(o),u=(0,r.useRef)(e),c=(0,r.useRef)(n),f=(0,r.useRef)(!1),d=(0,r.useRef)();void 0===d.current&&(d.current=e(o));let p=!1;(s.current!==o||u.current!==e||c.current!==n||f.current)&&(a=e(o),p=!n(d.current,a)),i(()=>{p&&(d.current=a),s.current=o,u.current=e,c.current=n,f.current=!1});let h=(0,r.useRef)(o);i(()=>{let e=()=>{try{let e=t.getState(),n=u.current(e);c.current(d.current,n)||(s.current=e,d.current=n,l())}catch(e){f.current=!0,l()}},n=t.subscribe(e);return t.getState()!==h.current&&e(),n},[]);let m=p?a:d.current;return(0,r.useDebugValue)(m),m};return Object.assign(n,t),n[Symbol.iterator]=function(){console.warn("[useStore, api] = create() is deprecated and will be removed in v4");let e=[n,t];return{next(){let t=e.length<=0;return{value:e.shift(),done:t}}}},n}},7462:function(e,t,n){"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}n.d(t,{Z:function(){return r}})},4503:function(e,t,n){"use strict";let r;n.d(t,{Ff:function(){return u},xC:function(){return f},cy:function(){return d}});var a=n(2248),i=n(7294),l=n(9477),o=n(230);let s=function(e,t){return void 0===t&&(t=a.YQ.NORMAL),(0,i.forwardRef)(function(n,r){let{blendFunction:a,opacity:l,...s}=n,u=(0,o.z)(e=>e.invalidate),c=(0,i.useMemo)(()=>new e(s),[s]);return(0,i.useLayoutEffect)(()=>{c.blendMode.blendFunction=a||0===a?a:t,void 0!==l&&(c.blendMode.opacity.value=l),u()},[a,c.blendMode,l]),i.createElement("primitive",{ref:r,object:c,dispose:null})})};s(a.rk,a.YQ.ADD);let u=s(a.at);s(a.zo),s(a.AL);let c=(0,i.createContext)(null),f=i.memo((0,i.forwardRef)((e,t)=>{let{children:n,camera:s,scene:u,resolutionScale:f,enabled:d=!0,renderPriority:p=1,autoClear:h=!0,depthBuffer:m,disableNormalPass:A,stencilBuffer:B,multisampling:g=8,frameBufferType:C=l.HalfFloatType}=e,{gl:v,scene:b,camera:y,size:E}=(0,o.z)();u=u||b,s=s||y;let[M,F,S]=(0,i.useMemo)(()=>{let e=function(){if(void 0!==r)return r;try{var e;let t;let n=document.createElement("canvas");return r=!!(window.WebGL2RenderingContext&&(t=n.getContext("webgl2"))),t&&(null===(e=t.getExtension("WEBGL_lose_context"))||void 0===e||e.loseContext()),r}catch(e){return r=!1}}(),t=new a.xC(v,{depthBuffer:m,stencilBuffer:B,multisampling:g>0&&e?g:0,frameBufferType:C});t.addPass(new a.CD(u,s));let n=null,i=null;return!A&&((i=new a.gh(u,s)).enabled=!1,t.addPass(i),void 0!==f&&e&&((n=new a.xs({normalBuffer:i.texture,resolutionScale:f})).enabled=!1,t.addPass(n))),[t,i,n]},[s,v,m,B,g,C,u,A,f]);(0,i.useEffect)(()=>null==M?void 0:M.setSize(E.width,E.height),[M,E]),(0,o.A)((e,t)=>{d&&(v.autoClear=h,M.render(t))},d?p:0);let I=(0,i.useRef)(null);(0,i.useLayoutEffect)(()=>{let e;return I.current&&I.current.__r3f&&M&&((e=new a.H5(s,...I.current.__r3f.objects)).renderToScreen=!0,M.addPass(e),F&&(F.enabled=!0),S&&(S.enabled=!0)),()=>{e&&(null==M||M.removePass(e)),F&&(F.enabled=!1),S&&(S.enabled=!1)}},[M,n,s,F,S]);let R=(0,i.useMemo)(()=>({composer:M,normalPass:F,downSamplingPass:S,resolutionScale:f,camera:s,scene:u}),[M,F,S,f,s,u]);return(0,i.useImperativeHandle)(t,()=>M,[M]),i.createElement(c.Provider,{value:R},i.createElement("group",{ref:I},n))}));s(a.ol),s(a.JL);let d=s(a.xV,a.YQ.COLOR_DODGE);s(a.p$,a.YQ.OVERLAY),s(a.wL),s(a.M4),s(a.Dd),s(a.Uy),s(a.$L,a.YQ.ADD)}}]);