{"pageProps": {"page": {"id": "ZsxTWBEAACYAuPvu", "uid": "synththeatre", "url": "/project/synththeatre", "type": "project", "href": "https://design-is-funny.cdn.prismic.io/api/v2/documents/search?ref=Z0hw5BEAAB8At7Hg&q=%5B%5B%3Ad+%3D+at%28document.id%2C+%22ZsxTWBEAACYAuPvu%22%29+%5D%5D", "tags": ["SynthTheatre"], "first_publication_date": "2024-08-28T11:47:18+0000", "last_publication_date": "2024-08-28T11:50:20+0000", "slugs": ["synthetic-theatre"], "linked_documents": [], "lang": "en-gb", "alternate_languages": [], "data": {"title": [{"type": "heading1", "text": "Synthetic Theatre", "spans": [], "direction": "ltr"}], "hyphenated_title": [], "type": "Website", "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=0,60,1920,960&w=3200&h=1600", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 0, "y": 60, "zoom": 1, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=0,36,1920,1008&w=1200&h=630", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 0, "y": 36, "zoom": 1, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/ZsxT60aF0TcGJY5l_MainComposition1.jpg?auto=format,compress&rect=420,0,1080,1080&w=3200&h=3200", "id": "ZsxT60aF0TcGJY5l", "edit": {"x": 420, "y": 0, "zoom": 1, "background": "transparent"}}}, "skills": [{"item": "Creative Direction"}, {"item": "Visual"}, {"item": "Motion"}, {"item": "Storytelling"}], "slices": [{"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "Project overview", "spans": []}], "first_column": [{"type": "paragraph", "text": "The Synthetic Theatre is an experimental project that screens imaginary stories, brought to life through the merge of Generative AI, Design, Motion and Storytelling.", "spans": [], "direction": "ltr"}], "second_column": [{"type": "paragraph", "text": "The project focused on creating an interactive experience around the world of Synthetic Theatre and see its constant evolution.", "spans": [], "direction": "ltr"}]}, "id": "paragraph$5ea44268-8928-4890-8572-f8425deb7715", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"video_1080p": {"link_type": "Media", "kind": "file", "id": "ZsxU-kaF0TcGJY6N", "url": "https://design-is-funny.cdn.prismic.io/design-is-funny/ZsxU-kaF0TcGJY6N_Social.mp4", "name": "Social.mp4", "size": "37845223"}, "video_720p": {"link_type": "Media"}, "video_540p": {"link_type": "Media"}, "video_360p": {"link_type": "Media"}}, "id": "video$a09751bd-ccc3-4bb9-8d69-3ef3f32d3848", "slice_type": "video", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "The Manifesto", "spans": [], "direction": "ltr"}], "first_column": [{"type": "paragraph", "text": "The Synthetic Theatre exists only if imagination is set free. It is a realm where stories unravel, and where visual freedom is at the core.", "spans": [], "direction": "ltr"}], "second_column": [{"type": "paragraph", "text": "Here, stories of unseen worlds take centre stage. exploring what can only exist at the intersection of Human Sensibility and AI's infinite possibilities.", "spans": [], "direction": "ltr"}]}, "id": "paragraph$e81f08c3-98b4-4545-aad6-699bc9c5b6b5", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/Zs8NX0aF0TcGJdva_031.jpg?auto=format,compress", "id": "Zs8NX0aF0TcGJdva", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, "id": "image$526d01ca-96b3-4526-883d-7c3ff591809e", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/Zs8NYEaF0TcGJdvc_041.jpg?auto=format,compress", "id": "Zs8NYEaF0TcGJdvc", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, "id": "image$7718f081-37e0-467f-a439-941888dbbcad", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/Zs8ONEaF0TcGJdv0_Frame1533210930.jpg?auto=format,compress", "id": "Zs8ONEaF0TcGJdv0", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, "id": "parallax_image$029093ee-0160-4117-8348-071189a7b141", "slice_type": "parallax_image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{"text": "Year/2024"}, {"text": "Personal Project"}], "primary": {}, "id": "meta_info$d54379ae-1148-4979-a94f-6d60aa9347d0", "slice_type": "meta_info", "slice_label": null}], "meta_description": []}}, "nextProject": {"id": "ZAxAZhAAACgAXly8", "type": "project", "tags": [], "lang": "en-gb", "slug": "headspace", "first_publication_date": "2023-03-11T08:51:27+0000", "last_publication_date": "2023-05-23T12:30:32+0000", "uid": "headspace", "url": "/project/headspace", "data": {"title": [{"type": "heading1", "text": "Headspace", "spans": []}], "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/95fc023c-93f0-4ba9-88f7-b53f1f830ebc_Cover.png?auto=compress,format&rect=0,60,1920,960&w=3200&h=1600", "id": "ZB3iexAAACMAqedp", "edit": {"x": 0, "y": -100, "zoom": 1.6666666666666667, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/95fc023c-93f0-4ba9-88f7-b53f1f830ebc_Cover.png?auto=compress,format&rect=0,35,1920,1008&w=1200&h=630", "id": "ZB3iexAAACMAqedp", "edit": {"x": 0, "y": -22, "zoom": 0.625, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/95fc023c-93f0-4ba9-88f7-b53f1f830ebc_Cover.png?auto=compress,format&rect=420,0,1080,1080&w=3200&h=3200", "id": "ZB3iexAAACMAqedp", "edit": {"x": -1244, "y": 0, "zoom": 2.962962962962963, "background": "transparent"}}}, "hyphenated_title": [{"type": "paragraph", "text": "Head- space", "spans": []}]}, "link_type": "Document", "isBroken": false}}, "__N_SSG": true}