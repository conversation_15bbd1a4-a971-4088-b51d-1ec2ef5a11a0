# Design is Funny - Creative Portfolio

A stunning creative portfolio website featuring 3D animations, smooth AJAX transitions, and interactive design elements.

## ✨ Features

- **3D Animations** - Powered by Three.js with HDR lighting and 3D models
- **Smooth Transitions** - Professional GSAP animations for seamless navigation
- **Interactive Design** - Hover effects, smooth scrolling, and dynamic content loading
- **Responsive Layout** - Works perfectly on all devices
- **Offline Ready** - Complete static site with all assets included

## 🚀 Technologies Used

- **Three.js** - 3D graphics and WebGL rendering
- **GSAP** - Professional animation library
- **Next.js** - React framework (static export)
- **Modern CSS** - Custom animations and responsive design

## 📦 Project Structure

```
├── _next/static/          # Next.js static assets
├── images/               # Image assets and textures
├── 3d/                   # 3D models and HDR files
├── favicon/              # Favicon files
├── index.html            # Main homepage
└── project/              # Individual project pages
```

## 🛠️ Development

```bash
# Start local development server
npm run dev

# Deploy to Vercel
npm run deploy
```

## 🌐 Live Demo

Visit the live website: [https://design-is-funny.vercel.app](https://design-is-funny.vercel.app)

## 📄 License

MIT License - feel free to use this code for your own projects!

---

**Design is Funny** - Creative Direction, Visual Design, Motion & Storytelling
