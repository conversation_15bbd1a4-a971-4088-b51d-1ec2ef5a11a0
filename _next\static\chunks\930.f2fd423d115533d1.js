"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[930],{9521:function(e,r,t){t.d(r,{S:function(){return a}});var n=t(9477),o=t(4671);let s=0,a=(0,o.Z)(e=>(n.DefaultLoadingManager.onStart=(r,t,n)=>{e({active:!0,item:r,loaded:t,total:n,progress:(t-s)/(n-s)*100})},n.DefaultLoadingManager.onLoad=()=>{e({active:!1})},n.DefaultLoadingManager.onError=r=>e(e=>({errors:[...e.errors,r]})),n.DefaultLoadingManager.onProgress=(r,t,n)=>{t===n&&(s=n),e({active:!0,item:r,loaded:t,total:n,progress:(t-s)/(n-s)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0}))},2930:function(e,r,t){t.r(r),t.d(r,{default:function(){return S}});var n=t(5893),o=t(7294),s=t(8626),a=t(9521),i=t(9477),l=t(230),u=t(1663),c=t(297),d=t(1316),x=t(5359),f=t(2149),m=t(533),g=t(319),h=t(9656),v=t(9827);let p=(0,o.forwardRef)((e,r)=>{let{model:t,material:s,rotation:a}=e,u=(0,o.useRef)(),c=(0,o.useRef)(),d=(0,o.useRef)(),x=(0,o.useRef)(),f=(0,o.useRef)(),m=(0,o.useRef)(),p=(0,o.useMemo)(()=>{let e=new i.Scene;return e},[]),j=(0,g.R)();return(0,l.A)(e=>{let{gl:r,camera:t}=e;u.current.rotation.y=a.current,c.current.rotation.y+=.001,c.current.rotation.x=-.6,c.current.rotation.z=.7,d.current.rotation.z+=.002,x.current.rotation.z-=.002,f.current.rotation.x+=.002,m.current.rotation.x-=.002,r.setRenderTarget(j),r.render(p,t),r.setRenderTarget(null)}),(0,o.useImperativeHandle)(r,()=>({getTexture:()=>j.texture})),(0,n.jsx)(n.Fragment,{children:(0,l.g)((0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("group",{ref:u,children:(0,n.jsx)("group",{ref:c,children:(0,n.jsx)(h.Z,{model:t,material:s,mouthRef:d,eyesRef:x,leftEyeRef:f,rightEyeRef:m})})}),(0,n.jsx)(v.Z,{})]}),p)})});p.displayName="FooterSmileyLogo";var j=t(7078),y=t(9594);let b=()=>{let e=(0,s.L)("/3d/smiley_logo.glb"),r=(0,o.useMemo)(()=>[(0,n.jsx)(d.Z,{},"2"),(0,n.jsx)(j.Z,{},"1"),(0,n.jsx)(x.Z,{},"3"),(0,n.jsx)(f.Z,{},"4"),(0,n.jsx)(m.Z,{},"5")],[]),t=(0,o.useRef)([{getTexture:()=>null},{getTexture:()=>null},{getTexture:()=>null},{getTexture:()=>null},{getTexture:()=>null}]),a=(0,o.useRef)(),[c,g]=(0,o.useState)(0),[h,v]=(0,o.useState)(0),y=()=>{g(e=>e+1),v(e=>e+Math.PI)},b=(0,o.useRef)(0),M=(0,o.useRef)(0);(0,l.A)((e,r)=>{if(u.Ui.damp(b,"current",c,.2,r),u.Ui.damp(M,"current",h,.2,r),a.current){let e=Math.floor(b.current);a.current.uniforms.uTexture1.value=t.current[e%5].getTexture(),a.current.uniforms.uTexture2.value=t.current[(e+1)%5].getTexture(),a.current.uniforms.uMaterialIndex.value=b.current}});let R=(0,o.useMemo)(()=>({uTexture1:{value:new i.Texture},uTexture2:{value:new i.Texture},uMaterialIndex:{value:0}}),[]);return(0,n.jsxs)(n.Fragment,{children:[r.map((r,o)=>(0,n.jsx)(p,{index:o,model:e,material:r,ref:e=>t.current[o]=e,activeMaterialIndex:b,rotation:M},o)),(0,n.jsx)("group",{children:(0,n.jsxs)("mesh",{onPointerDown:y,children:[(0,n.jsx)("planeBufferGeometry",{args:[6,6]}),(0,n.jsx)("shaderMaterial",{ref:a,vertexShader:"\n              varying vec2 vUv;\n              void main() {\n                vUv = uv;\n                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n              }\n            ",fragmentShader:"\n              precision mediump float;\n              uniform float uTime;\n              uniform sampler2D uTexture1;\n              uniform sampler2D uTexture2;\n              uniform float uMaterialIndex;\n          \n              varying vec2 vUv;\n          \n              void main() {\n                float textureMix = fract(uMaterialIndex);\n          \n                vec4 texture1 = texture2D(uTexture1, vUv).rgba;\n                vec4 texture2 = texture2D(uTexture2, vUv).rgba;\n                gl_FragColor = texture1 * (1.0 - textureMix) + texture2 * textureMix;\n              }\n            ",uniforms:R})]})})]})},M=()=>((0,a.S)(e=>(0!==e.progress&&(y.HS.threeLoadingProgress=e.progress),e.progress)),(0,n.jsx)(o.Fragment,{})),R=()=>(0,n.jsx)(o.Suspense,{fallback:(0,n.jsx)(M,{}),children:(0,n.jsx)(c.Z,{className:"w-full h-full",children:(0,n.jsx)(b,{})})});var S=(0,o.memo)(R)},297:function(e,r,t){t.d(r,{Z:function(){return h}});var n=t(5893),o=t(3520),s=t(3758),a=t(230),i=t(5029),l=t(9477),u=t(7294),c=t(8536),d=t(4503),x=t(2248);let f=()=>(0,n.jsx)(u.Suspense,{fallback:null,children:(0,n.jsxs)(d.xC,{children:[(0,n.jsx)(d.cy,{premultiply:!0,blendFunction:x.YQ.NORMAL,opacity:.4}),(0,n.jsx)(d.Ff,{brightness:0,contrast:.35})]})});var m=t(9827);(0,a.e)({Mesh:l.Mesh,Group:l.Group,Texture:l.Texture,ShaderMaterial:l.ShaderMaterial,Object3D:l.Object3D});let g=e=>{let{children:r,eventSource:t,className:a,runOnScrollTop:l}=e,d=(0,u.useRef)(null),x=(0,c.Z)(d,{root:null,rootMargin:"0px",threshold:0}),[g,h]=(0,u.useState)(!1);return(0,u.useEffect)(()=>{h(0===window.scrollY);let e=()=>{l&&(0!==window.scrollY||g?g&&h(!1):h(!0))};return l&&window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[l,g]),(0,n.jsx)("div",{className:"w-full h-full",ref:d,children:(0,n.jsxs)(i.Xz,{dpr:[1,2],eventSource:t,className:a,gl:{antialias:!1,physicallyCorrectLights:!0},frameloop:l&&g||x&&x.intersectionRatio>0?"always":"demand",camera:{position:[0,0,10],fov:35},resize:{offsetSize:!0,debounce:{scroll:500,resize:500}},style:{touchAction:"auto"},children:[(0,n.jsx)(o.E,{}),(0,n.jsx)(m.Z,{}),(0,n.jsx)(f,{}),(0,n.jsx)(s.S,{pixelated:!0}),r]})})};var h=g},9827:function(e,r,t){var n=t(5893),o=t(2749);let s=()=>(0,n.jsx)(o.qA,{files:"/3d/lobby.hdr"});r.Z=s},9656:function(e,r,t){var n=t(5893);let o=e=>{let{model:{nodes:r},material:t,mouthRef:o,eyesRef:s,leftEyeRef:a,rightEyeRef:i}=e,l=r.eye_1,u=r.eye_2,c=r.smile,d=r.outer_ring;return l.geometry.center(),u.geometry.center(),(0,n.jsxs)("group",{rotation:[Math.PI/2,0,0],scale:2.55,children:[(0,n.jsxs)("group",{ref:s,children:[(0,n.jsx)("mesh",{position:[-.51,0,-.3],ref:a,geometry:l.geometry,children:t}),(0,n.jsx)("mesh",{position:[.51,0,-.3],ref:i,geometry:u.geometry,children:t})]}),(0,n.jsx)("mesh",{ref:o,geometry:c.geometry,children:t}),(0,n.jsx)("mesh",{geometry:d.geometry,children:t})]})};r.Z=o},5359:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),a=t(8197);let i=()=>{let e=(0,s.D)(a.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{color:"#C9A15C",clearcoat:.4,clearcoatRoughness:.1,metalness:.6,roughness:.3,thickness:.8,transmission:.2,envMapIntensity:.9,distortionScale:0,temporalDistortion:0,ior:2,buffer:e})};r.Z=i},7078:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),a=t(8197);let i=()=>{let e=(0,s.D)(a.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{distortionScale:1,temporalDistortion:0,distortion:0,transmission:.8,color:"#ddd",backside:!0,roughness:.25,thickness:.4,chromaticAberration:1,metalness:.2,clearcoat:0,clearcoatRoughness:.1,backsideResolution:32,resolution:512,samples:1,ior:1.8,buffer:e})};r.Z=i},1316:function(e,r,t){var n=t(5893),o=t(5769),s=t(3435);let a=()=>{let e=(0,o.m)("/3d/gradient_bg.jpg");return e.repeat.set(3,3),(0,n.jsx)(s.z,{background:e,distortionScale:1,distortion:0,temporalDistortion:0,transmission:1.2,color:"#fff",resolution:16,chromaticAberration:20,roughness:.2,thickness:100,metalness:.3,clearcoat:0,clearcoatRoughness:.1,envMapIntensity:.6,buffer:e})};r.Z=a},533:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),a=t(8197);let i=()=>{let e=(0,s.D)(a.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{distortionScale:1,temporalDistortion:0,distortion:0,transmission:.6,color:"#4a4",roughness:.22,thickness:.4,chromaticAberration:1,metalness:.1,clearcoat:.3,clearcoatRoughness:.1,ior:1.8,background:e,samples:1,envMapIntensity:1,backside:!0,buffer:e,backsideResolution:32,resolution:512})};r.Z=i},2149:function(e,r,t){var n=t(5893),o=t(3435),s=t(230),a=t(8197);let i=()=>{let e=(0,s.D)(a.x,"/3d/lobby.hdr");return(0,n.jsx)(o.z,{color:"#ddf",clearcoat:.4,clearcoatRoughness:.1,roughness:.2,envMapIntensity:1,ior:2,metalness:.1,transmission:.6,buffer:e,thickness:20,distortionScale:0,temporalDistortion:0})};r.Z=i}}]);