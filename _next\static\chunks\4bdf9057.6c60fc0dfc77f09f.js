"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[444],{2248:function(e,t,i){i.d(t,{$L:function(){return ef},AL:function(){return $},CD:function(){return _},Dd:function(){return ep},H5:function(){return L},JL:function(){return ee},M4:function(){return ev},Uy:function(){return ec},YQ:function(){return a},at:function(){return Y},gh:function(){return H},ol:function(){return J},p$:function(){return eo},rk:function(){return K},wL:function(){return ed},xC:function(){return W},xV:function(){return ea},xs:function(){return P},zo:function(){return Q}});var r=i(9477),n="varying vec2 vUv;void main(){vUv=position.xy*0.5+0.5;gl_Position=vec4(position.xy,1.0,1.0);}",s=class extends r.ShaderMaterial{constructor(){super({name:"AdaptiveLuminanceMaterial",defines:{THREE_REVISION:r.REVISION.replace(/\D+/g,""),MIP_LEVEL_1X1:"0.0"},uniforms:{luminanceBuffer0:new r.Uniform(null),luminanceBuffer1:new r.Uniform(null),minLuminance:new r.Uniform(.01),deltaTime:new r.Uniform(0),tau:new r.Uniform(1)},extensions:{shaderTextureLOD:!0},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#include <packing>\n#define packFloatToRGBA(v) packDepthToRGBA(v)\n#define unpackRGBAToFloat(v) unpackRGBAToDepth(v)\nuniform lowp sampler2D luminanceBuffer0;uniform lowp sampler2D luminanceBuffer1;uniform float minLuminance;uniform float deltaTime;uniform float tau;varying vec2 vUv;void main(){float l0=unpackRGBAToFloat(texture2D(luminanceBuffer0,vUv));\n#if __VERSION__ < 300\nfloat l1=texture2DLodEXT(luminanceBuffer1,vUv,MIP_LEVEL_1X1).r;\n#else\nfloat l1=textureLod(luminanceBuffer1,vUv,MIP_LEVEL_1X1).r;\n#endif\nl0=max(minLuminance,l0);l1=max(minLuminance,l1);float adaptedLum=l0+(l1-l0)*(1.0-exp(-deltaTime*tau));gl_FragColor=(adaptedLum==1.0)?vec4(1.0):packFloatToRGBA(adaptedLum);}",vertexShader:n}),this.toneMapped=!1}set luminanceBuffer0(e){this.uniforms.luminanceBuffer0.value=e}setLuminanceBuffer0(e){this.uniforms.luminanceBuffer0.value=e}set luminanceBuffer1(e){this.uniforms.luminanceBuffer1.value=e}setLuminanceBuffer1(e){this.uniforms.luminanceBuffer1.value=e}set mipLevel1x1(e){this.defines.MIP_LEVEL_1X1=e.toFixed(1),this.needsUpdate=!0}setMipLevel1x1(e){this.mipLevel1x1=e}set deltaTime(e){this.uniforms.deltaTime.value=e}setDeltaTime(e){this.uniforms.deltaTime.value=e}get minLuminance(){return this.uniforms.minLuminance.value}set minLuminance(e){this.uniforms.minLuminance.value=e}getMinLuminance(){return this.uniforms.minLuminance.value}setMinLuminance(e){this.uniforms.minLuminance.value=e}get adaptationRate(){return this.uniforms.tau.value}set adaptationRate(e){this.uniforms.tau.value=e}getAdaptationRate(){return this.uniforms.tau.value}setAdaptationRate(e){this.uniforms.tau.value=e}},a={SKIP:9,SET:30,ADD:0,ALPHA:1,AVERAGE:2,COLOR:3,COLOR_BURN:4,COLOR_DODGE:5,DARKEN:6,DIFFERENCE:7,DIVIDE:8,DST:9,EXCLUSION:10,HARD_LIGHT:11,HARD_MIX:12,HUE:13,INVERT:14,INVERT_RGB:15,LIGHTEN:16,LINEAR_BURN:17,LINEAR_DODGE:18,LINEAR_LIGHT:19,LUMINOSITY:20,MULTIPLY:21,NEGATION:22,NORMAL:23,OVERLAY:24,PIN_LIGHT:25,REFLECT:26,SATURATION:27,SCREEN:28,SOFT_LIGHT:29,SRC:30,SUBTRACT:31,VIVID_LIGHT:32},o={NONE:0,DEPTH:1,CONVOLUTION:2},l={FRAGMENT_HEAD:"FRAGMENT_HEAD",FRAGMENT_MAIN_UV:"FRAGMENT_MAIN_UV",FRAGMENT_MAIN_IMAGE:"FRAGMENT_MAIN_IMAGE",VERTEX_HEAD:"VERTEX_HEAD",VERTEX_MAIN_SUPPORT:"VERTEX_MAIN_SUPPORT"},u={VERY_SMALL:0,SMALL:1,MEDIUM:2,LARGE:3,VERY_LARGE:4,HUGE:5},h={SCALE_UP:"lut.scaleup"},c={REINHARD:0,REINHARD2:1,REINHARD2_ADAPTIVE:2,OPTIMIZED_CINEON:3,ACES_FILMIC:4},d={DEFAULT:0,ESKIL:1},f=[new Float32Array([0,0]),new Float32Array([0,1,1]),new Float32Array([0,1,1,2]),new Float32Array([0,1,2,2,3]),new Float32Array([0,1,2,3,4,4,5]),new Float32Array([0,1,2,3,4,5,7,8,9,10])],v=class extends r.ShaderMaterial{constructor(e=new r.Vector4){super({name:"KawaseBlurMaterial",uniforms:{inputBuffer:new r.Uniform(null),texelSize:new r.Uniform(new r.Vector4),scale:new r.Uniform(1),kernel:new r.Uniform(0)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\nvarying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;void main(){vec4 sum=texture2D(inputBuffer,vUv0);sum+=texture2D(inputBuffer,vUv1);sum+=texture2D(inputBuffer,vUv2);sum+=texture2D(inputBuffer,vUv3);gl_FragColor=sum*0.25;\n#include <encodings_fragment>\n}",vertexShader:"uniform vec4 texelSize;uniform float kernel;uniform float scale;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;void main(){vec2 uv=position.xy*0.5+0.5;vec2 dUv=(texelSize.xy*vec2(kernel)+texelSize.zw)*scale;vUv0=vec2(uv.x-dUv.x,uv.y+dUv.y);vUv1=vec2(uv.x+dUv.x,uv.y+dUv.y);vUv2=vec2(uv.x+dUv.x,uv.y-dUv.y);vUv3=vec2(uv.x-dUv.x,uv.y-dUv.y);gl_Position=vec4(position.xy,1.0,1.0);}"}),this.toneMapped=!1,this.setTexelSize(e.x,e.y),this.kernelSize=u.MEDIUM}set inputBuffer(e){this.uniforms.inputBuffer.value=e}setInputBuffer(e){this.inputBuffer=e}get kernelSequence(){return f[this.kernelSize]}get scale(){return this.uniforms.scale.value}set scale(e){this.uniforms.scale.value=e}getScale(){return this.uniforms.scale.value}setScale(e){this.uniforms.scale.value=e}getKernel(){return null}get kernel(){return this.uniforms.kernel.value}set kernel(e){this.uniforms.kernel.value=e}setKernel(e){this.kernel=e}setTexelSize(e,t){this.uniforms.texelSize.value.set(e,t,.5*e,.5*t)}setSize(e,t){let i=1/e,r=1/t;this.uniforms.texelSize.value.set(i,r,.5*i,.5*r)}},p=class extends r.ShaderMaterial{constructor(){super({name:"CopyMaterial",uniforms:{inputBuffer:new r.Uniform(null),opacity:new r.Uniform(1)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#include <common>\n#include <dithering_pars_fragment>\n#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\nuniform float opacity;varying vec2 vUv;void main(){vec4 texel=texture2D(inputBuffer,vUv);gl_FragColor=opacity*texel;\n#include <encodings_fragment>\n#include <dithering_fragment>\n}",vertexShader:n}),this.toneMapped=!1}set inputBuffer(e){this.uniforms.inputBuffer.value=e}setInputBuffer(e){this.uniforms.inputBuffer.value=e}getOpacity(e){return this.uniforms.opacity.value}setOpacity(e){this.uniforms.opacity.value=e}},m=class extends r.ShaderMaterial{constructor(){super({name:"DepthDownsamplingMaterial",defines:{DEPTH_PACKING:"0"},uniforms:{depthBuffer:new r.Uniform(null),normalBuffer:new r.Uniform(null),texelSize:new r.Uniform(new r.Vector2)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#include <packing>\n#ifdef GL_FRAGMENT_PRECISION_HIGH\nuniform highp sampler2D depthBuffer;\n#else\nuniform mediump sampler2D depthBuffer;\n#endif\n#ifdef DOWNSAMPLE_NORMALS\nuniform lowp sampler2D normalBuffer;\n#endif\nvarying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;float readDepth(const in vec2 uv){\n#if DEPTH_PACKING == 3201\nreturn unpackRGBAToDepth(texture2D(depthBuffer,uv));\n#else\nreturn texture2D(depthBuffer,uv).r;\n#endif\n}int findBestDepth(const in float samples[4]){float c=(samples[0]+samples[1]+samples[2]+samples[3])*0.25;float distances[]=float[4](abs(c-samples[0]),abs(c-samples[1]),abs(c-samples[2]),abs(c-samples[3]));float maxDistance=max(max(distances[0],distances[1]),max(distances[2],distances[3]));int remaining[3];int rejected[3];int i,j,k;for(i=0,j=0,k=0;i<4;++i){if(distances[i]<maxDistance){remaining[j++]=i;}else{rejected[k++]=i;}}for(;j<3;++j){remaining[j]=rejected[--k];}vec3 s=vec3(samples[remaining[0]],samples[remaining[1]],samples[remaining[2]]);c=(s.x+s.y+s.z)/3.0;distances[0]=abs(c-s.x);distances[1]=abs(c-s.y);distances[2]=abs(c-s.z);float minDistance=min(distances[0],min(distances[1],distances[2]));for(i=0;i<3;++i){if(distances[i]==minDistance){break;}}return remaining[i];}void main(){float d[]=float[4](readDepth(vUv0),readDepth(vUv1),readDepth(vUv2),readDepth(vUv3));int index=findBestDepth(d);\n#ifdef DOWNSAMPLE_NORMALS\nvec3 n[]=vec3[4](texture2D(normalBuffer,vUv0).rgb,texture2D(normalBuffer,vUv1).rgb,texture2D(normalBuffer,vUv2).rgb,texture2D(normalBuffer,vUv3).rgb);\n#else\nvec3 n[]=vec3[4](vec3(0.0),vec3(0.0),vec3(0.0),vec3(0.0));\n#endif\ngl_FragColor=vec4(n[index],d[index]);}",vertexShader:"uniform vec2 texelSize;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;void main(){vec2 uv=position.xy*0.5+0.5;vUv0=uv;vUv1=vec2(uv.x,uv.y+texelSize.y);vUv2=vec2(uv.x+texelSize.x,uv.y);vUv3=uv+texelSize;gl_Position=vec4(position.xy,1.0,1.0);}"}),this.toneMapped=!1}set depthBuffer(e){this.uniforms.depthBuffer.value=e}set depthPacking(e){this.defines.DEPTH_PACKING=e.toFixed(0),this.needsUpdate=!0}setDepthBuffer(e,t=r.BasicDepthPacking){this.depthBuffer=e,this.depthPacking=t}set normalBuffer(e){this.uniforms.normalBuffer.value=e,null!==e?this.defines.DOWNSAMPLE_NORMALS="1":delete this.defines.DOWNSAMPLE_NORMALS,this.needsUpdate=!0}setNormalBuffer(e){this.normalBuffer=e}setTexelSize(e,t){this.uniforms.texelSize.value.set(e,t)}setSize(e,t){this.uniforms.texelSize.value.set(1/e,1/t)}},g=class extends r.ShaderMaterial{constructor(){super({name:"DownsamplingMaterial",uniforms:{inputBuffer:new r.Uniform(null),texelSize:new r.Uniform(new r.Vector2)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\n#define WEIGHT_INNER 0.125\n#define WEIGHT_OUTER 0.0555555\nvarying vec2 vUv;varying vec2 vUv00;varying vec2 vUv01;varying vec2 vUv02;varying vec2 vUv03;varying vec2 vUv04;varying vec2 vUv05;varying vec2 vUv06;varying vec2 vUv07;varying vec2 vUv08;varying vec2 vUv09;varying vec2 vUv10;varying vec2 vUv11;float clampToBorder(const in vec2 uv){return float(uv.s>=0.0&&uv.s<=1.0&&uv.t>=0.0&&uv.t<=1.0);}void main(){vec4 c=vec4(0.0);vec4 w=WEIGHT_INNER*vec4(clampToBorder(vUv00),clampToBorder(vUv01),clampToBorder(vUv02),clampToBorder(vUv03));c+=w.x*texture2D(inputBuffer,vUv00);c+=w.y*texture2D(inputBuffer,vUv01);c+=w.z*texture2D(inputBuffer,vUv02);c+=w.w*texture2D(inputBuffer,vUv03);w=WEIGHT_OUTER*vec4(clampToBorder(vUv04),clampToBorder(vUv05),clampToBorder(vUv06),clampToBorder(vUv07));c+=w.x*texture2D(inputBuffer,vUv04);c+=w.y*texture2D(inputBuffer,vUv05);c+=w.z*texture2D(inputBuffer,vUv06);c+=w.w*texture2D(inputBuffer,vUv07);w=WEIGHT_OUTER*vec4(clampToBorder(vUv08),clampToBorder(vUv09),clampToBorder(vUv10),clampToBorder(vUv11));c+=w.x*texture2D(inputBuffer,vUv08);c+=w.y*texture2D(inputBuffer,vUv09);c+=w.z*texture2D(inputBuffer,vUv10);c+=w.w*texture2D(inputBuffer,vUv11);c+=WEIGHT_OUTER*texture2D(inputBuffer,vUv);gl_FragColor=c;\n#include <encodings_fragment>\n}",vertexShader:"uniform vec2 texelSize;varying vec2 vUv;varying vec2 vUv00;varying vec2 vUv01;varying vec2 vUv02;varying vec2 vUv03;varying vec2 vUv04;varying vec2 vUv05;varying vec2 vUv06;varying vec2 vUv07;varying vec2 vUv08;varying vec2 vUv09;varying vec2 vUv10;varying vec2 vUv11;void main(){vUv=position.xy*0.5+0.5;vUv00=vUv+texelSize*vec2(-1.0,1.0);vUv01=vUv+texelSize*vec2(1.0,1.0);vUv02=vUv+texelSize*vec2(-1.0,-1.0);vUv03=vUv+texelSize*vec2(1.0,-1.0);vUv04=vUv+texelSize*vec2(-2.0,2.0);vUv05=vUv+texelSize*vec2(0.0,2.0);vUv06=vUv+texelSize*vec2(2.0,2.0);vUv07=vUv+texelSize*vec2(-2.0,0.0);vUv08=vUv+texelSize*vec2(2.0,0.0);vUv09=vUv+texelSize*vec2(-2.0,-2.0);vUv10=vUv+texelSize*vec2(0.0,-2.0);vUv11=vUv+texelSize*vec2(2.0,-2.0);gl_Position=vec4(position.xy,1.0,1.0);}"}),this.toneMapped=!1}set inputBuffer(e){this.uniforms.inputBuffer.value=e}setSize(e,t){this.uniforms.texelSize.value.set(1/e,1/t)}},x=class extends r.ShaderMaterial{constructor(e,t,i,n,s=!1){super({name:"EffectMaterial",defines:{THREE_REVISION:r.REVISION.replace(/\D+/g,""),DEPTH_PACKING:"0",ENCODE_OUTPUT:"1"},uniforms:{inputBuffer:new r.Uniform(null),depthBuffer:new r.Uniform(null),resolution:new r.Uniform(new r.Vector2),texelSize:new r.Uniform(new r.Vector2),cameraNear:new r.Uniform(.3),cameraFar:new r.Uniform(1e3),aspect:new r.Uniform(1),time:new r.Uniform(0)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,dithering:s}),this.toneMapped=!1,e&&this.setShaderParts(e),t&&this.setDefines(t),i&&this.setUniforms(i),this.copyCameraSettings(n)}set inputBuffer(e){this.uniforms.inputBuffer.value=e}setInputBuffer(e){this.uniforms.inputBuffer.value=e}get depthBuffer(){return this.uniforms.depthBuffer.value}set depthBuffer(e){this.uniforms.depthBuffer.value=e}get depthPacking(){return Number(this.defines.DEPTH_PACKING)}set depthPacking(e){this.defines.DEPTH_PACKING=e.toFixed(0),this.needsUpdate=!0}setDepthBuffer(e,t=r.BasicDepthPacking){this.depthBuffer=e,this.depthPacking=t}setShaderData(e){this.setShaderParts(e.shaderParts),this.setDefines(e.defines),this.setUniforms(e.uniforms),this.setExtensions(e.extensions)}setShaderParts(e){var t,i,r,n,s;return this.fragmentShader="#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#define packFloatToRGBA(v) packDepthToRGBA(v)\n#define unpackRGBAToFloat(v) unpackRGBAToDepth(v)\n#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\n#if DEPTH_PACKING == 3201\nuniform lowp sampler2D depthBuffer;\n#elif defined(GL_FRAGMENT_PRECISION_HIGH)\nuniform highp sampler2D depthBuffer;\n#else\nuniform mediump sampler2D depthBuffer;\n#endif\nuniform vec2 resolution;uniform vec2 texelSize;uniform float cameraNear;uniform float cameraFar;uniform float aspect;uniform float time;varying vec2 vUv;\n#if THREE_REVISION < 143\n#define luminance(v) linearToRelativeLuminance(v)\n#endif\n#if THREE_REVISION >= 137\nvec4 sRGBToLinear(const in vec4 value){return vec4(mix(pow(value.rgb*0.9478672986+vec3(0.0521327014),vec3(2.4)),value.rgb*0.0773993808,vec3(lessThanEqual(value.rgb,vec3(0.04045)))),value.a);}\n#endif\nfloat readDepth(const in vec2 uv){\n#if DEPTH_PACKING == 3201\nreturn unpackRGBAToDepth(texture2D(depthBuffer,uv));\n#else\nreturn texture2D(depthBuffer,uv).r;\n#endif\n}float getViewZ(const in float depth){\n#ifdef PERSPECTIVE_CAMERA\nreturn perspectiveDepthToViewZ(depth,cameraNear,cameraFar);\n#else\nreturn orthographicDepthToViewZ(depth,cameraNear,cameraFar);\n#endif\n}vec3 RGBToHCV(const in vec3 RGB){vec4 P=mix(vec4(RGB.bg,-1.0,2.0/3.0),vec4(RGB.gb,0.0,-1.0/3.0),step(RGB.b,RGB.g));vec4 Q=mix(vec4(P.xyw,RGB.r),vec4(RGB.r,P.yzx),step(P.x,RGB.r));float C=Q.x-min(Q.w,Q.y);float H=abs((Q.w-Q.y)/(6.0*C+EPSILON)+Q.z);return vec3(H,C,Q.x);}vec3 RGBToHSL(const in vec3 RGB){vec3 HCV=RGBToHCV(RGB);float L=HCV.z-HCV.y*0.5;float S=HCV.y/(1.0-abs(L*2.0-1.0)+EPSILON);return vec3(HCV.x,S,L);}vec3 HueToRGB(const in float H){float R=abs(H*6.0-3.0)-1.0;float G=2.0-abs(H*6.0-2.0);float B=2.0-abs(H*6.0-4.0);return clamp(vec3(R,G,B),0.0,1.0);}vec3 HSLToRGB(const in vec3 HSL){vec3 RGB=HueToRGB(HSL.x);float C=(1.0-abs(2.0*HSL.z-1.0))*HSL.y;return(RGB-0.5)*C+HSL.z;}FRAGMENT_HEADvoid main(){FRAGMENT_MAIN_UVvec4 color0=texture2D(inputBuffer,UV);vec4 color1=vec4(0.0);FRAGMENT_MAIN_IMAGEgl_FragColor=color0;\n#ifdef ENCODE_OUTPUT\n#include <encodings_fragment>\n#endif\n#include <dithering_fragment>\n}".replace(l.FRAGMENT_HEAD,null!=(t=e.get(l.FRAGMENT_HEAD))?t:"").replace(l.FRAGMENT_MAIN_UV,null!=(i=e.get(l.FRAGMENT_MAIN_UV))?i:"").replace(l.FRAGMENT_MAIN_IMAGE,null!=(r=e.get(l.FRAGMENT_MAIN_IMAGE))?r:""),this.vertexShader="uniform vec2 resolution;uniform vec2 texelSize;uniform float cameraNear;uniform float cameraFar;uniform float aspect;uniform float time;varying vec2 vUv;VERTEX_HEADvoid main(){vUv=position.xy*0.5+0.5;VERTEX_MAIN_SUPPORTgl_Position=vec4(position.xy,1.0,1.0);}".replace(l.VERTEX_HEAD,null!=(n=e.get(l.VERTEX_HEAD))?n:"").replace(l.VERTEX_MAIN_SUPPORT,null!=(s=e.get(l.VERTEX_MAIN_SUPPORT))?s:""),this.needsUpdate=!0,this}setDefines(e){for(let t of e.entries())this.defines[t[0]]=t[1];return this.needsUpdate=!0,this}setUniforms(e){for(let t of e.entries())this.uniforms[t[0]]=t[1];return this}setExtensions(e){for(let t of(this.extensions={},e))this.extensions[t]=!0;return this}get encodeOutput(){return void 0!==this.defines.ENCODE_OUTPUT}set encodeOutput(e){this.encodeOutput!==e&&(e?this.defines.ENCODE_OUTPUT="1":delete this.defines.ENCODE_OUTPUT,this.needsUpdate=!0)}isOutputEncodingEnabled(e){return this.encodeOutput}setOutputEncodingEnabled(e){this.encodeOutput=e}get time(){return this.uniforms.time.value}set time(e){this.uniforms.time.value=e}setDeltaTime(e){this.uniforms.time.value+=e}adoptCameraSettings(e){this.copyCameraSettings(e)}copyCameraSettings(e){e&&(this.uniforms.cameraNear.value=e.near,this.uniforms.cameraFar.value=e.far,e instanceof r.PerspectiveCamera?this.defines.PERSPECTIVE_CAMERA="1":delete this.defines.PERSPECTIVE_CAMERA,this.needsUpdate=!0)}setSize(e,t){let i=this.uniforms;i.resolution.value.set(e,t),i.texelSize.value.set(1/e,1/t),i.aspect.value=e/t}static get Section(){return l}},y=class extends r.ShaderMaterial{constructor(e=!1,t=null){super({name:"LuminanceMaterial",defines:{THREE_REVISION:r.REVISION.replace(/\D+/g,"")},uniforms:{inputBuffer:new r.Uniform(null),threshold:new r.Uniform(0),smoothing:new r.Uniform(1),range:new r.Uniform(null)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#include <common>\n#if THREE_REVISION < 143\n#define luminance(v) linearToRelativeLuminance(v)\n#endif\n#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\n#ifdef RANGE\nuniform vec2 range;\n#elif defined(THRESHOLD)\nuniform float threshold;uniform float smoothing;\n#endif\nvarying vec2 vUv;void main(){vec4 texel=texture2D(inputBuffer,vUv);float l=luminance(texel.rgb);\n#ifdef RANGE\nfloat low=step(range.x,l);float high=step(l,range.y);l*=low*high;\n#elif defined(THRESHOLD)\nl=smoothstep(threshold,threshold+smoothing,l);\n#endif\n#ifdef COLOR\ngl_FragColor=vec4(texel.rgb*l,l);\n#else\ngl_FragColor=vec4(l);\n#endif\n}",vertexShader:n}),this.toneMapped=!1,this.colorOutput=e,this.luminanceRange=t}set inputBuffer(e){this.uniforms.inputBuffer.value=e}setInputBuffer(e){this.uniforms.inputBuffer.value=e}get threshold(){return this.uniforms.threshold.value}set threshold(e){this.smoothing>0||e>0?this.defines.THRESHOLD="1":delete this.defines.THRESHOLD,this.uniforms.threshold.value=e}getThreshold(){return this.threshold}setThreshold(e){this.threshold=e}get smoothing(){return this.uniforms.smoothing.value}set smoothing(e){this.threshold>0||e>0?this.defines.THRESHOLD="1":delete this.defines.THRESHOLD,this.uniforms.smoothing.value=e}getSmoothingFactor(){return this.smoothing}setSmoothingFactor(e){this.smoothing=e}get useThreshold(){return this.threshold>0||this.smoothing>0}set useThreshold(e){}get colorOutput(){return void 0!==this.defines.COLOR}set colorOutput(e){e?this.defines.COLOR="1":delete this.defines.COLOR,this.needsUpdate=!0}isColorOutputEnabled(e){return this.colorOutput}setColorOutputEnabled(e){this.colorOutput=e}get useRange(){return null!==this.luminanceRange}set useRange(e){this.luminanceRange=null}get luminanceRange(){return this.uniforms.range.value}set luminanceRange(e){null!==e?this.defines.RANGE="1":delete this.defines.RANGE,this.uniforms.range.value=e,this.needsUpdate=!0}getLuminanceRange(){return this.luminanceRange}setLuminanceRange(e){this.luminanceRange=e}},S=class extends v{constructor({kernelSize:e=u.MEDIUM,offset:t=0,rotation:i=0,focusArea:n=.4,feather:s=.3}={}){super(),this.fragmentShader="#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;\n#else\nuniform lowp sampler2D inputBuffer;\n#endif\nuniform vec4 maskParams;varying vec2 vUv;varying vec2 vUv2;varying vec2 vOffset;float linearGradientMask(const in float x){return smoothstep(maskParams.x,maskParams.y,x)-smoothstep(maskParams.w,maskParams.z,x);}void main(){vec2 dUv=vOffset*(1.0-linearGradientMask(vUv2.y));vec4 sum=texture2D(inputBuffer,vec2(vUv.x-dUv.x,vUv.y+dUv.y));sum+=texture2D(inputBuffer,vec2(vUv.x+dUv.x,vUv.y+dUv.y));sum+=texture2D(inputBuffer,vec2(vUv.x+dUv.x,vUv.y-dUv.y));sum+=texture2D(inputBuffer,vec2(vUv.x-dUv.x,vUv.y-dUv.y));gl_FragColor=sum*0.25;\n#include <encodings_fragment>\n}",this.vertexShader="uniform vec4 texelSize;uniform float kernel;uniform float scale;uniform float aspect;uniform vec2 rotation;varying vec2 vUv;varying vec2 vUv2;varying vec2 vOffset;void main(){vec2 uv=position.xy*0.5+0.5;vUv=uv;vUv2=(uv-0.5)*2.0*vec2(aspect,1.0);vUv2=vec2(dot(rotation,vUv2),dot(rotation,vec2(vUv2.y,-vUv2.x)));vOffset=(texelSize.xy*vec2(kernel)+texelSize.zw)*scale;gl_Position=vec4(position.xy,1.0,1.0);}",this.kernelSize=e,this.uniforms.aspect=new r.Uniform(1),this.uniforms.rotation=new r.Uniform(new r.Vector2),this.uniforms.maskParams=new r.Uniform(new r.Vector4),this._offset=t,this._focusArea=n,this._feather=s,this.rotation=i,this.updateParams()}updateParams(){let e=this.uniforms.maskParams.value,t=Math.max(this.focusArea,0),i=Math.max(t-this.feather,0);e.set(this.offset-t,this.offset-i,this.offset+t,this.offset+i)}get rotation(){return Math.acos(this.uniforms.rotation.value.x)}set rotation(e){this.uniforms.rotation.value.set(Math.cos(e),Math.sin(e))}get offset(){return this._offset}set offset(e){this._offset=e,this.updateParams()}get focusArea(){return this._focusArea}set focusArea(e){this._focusArea=e,this.updateParams()}get feather(){return this._feather}set feather(e){this._feather=e,this.updateParams()}setSize(e,t){super.setSize(e,t),this.uniforms.aspect.value=e/t}},w=class extends r.ShaderMaterial{constructor(){super({name:"UpsamplingMaterial",uniforms:{inputBuffer:new r.Uniform(null),supportBuffer:new r.Uniform(null),texelSize:new r.Uniform(new r.Vector2),radius:new r.Uniform(.85)},blending:r.NoBlending,depthWrite:!1,depthTest:!1,fragmentShader:"#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D inputBuffer;uniform mediump sampler2D supportBuffer;\n#else\nuniform lowp sampler2D inputBuffer;uniform lowp sampler2D supportBuffer;\n#endif\nuniform float radius;varying vec2 vUv;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;varying vec2 vUv4;varying vec2 vUv5;varying vec2 vUv6;varying vec2 vUv7;void main(){vec4 c=vec4(0.0);c+=texture2D(inputBuffer,vUv0)*0.0625;c+=texture2D(inputBuffer,vUv1)*0.125;c+=texture2D(inputBuffer,vUv2)*0.0625;c+=texture2D(inputBuffer,vUv3)*0.125;c+=texture2D(inputBuffer,vUv)*0.25;c+=texture2D(inputBuffer,vUv4)*0.125;c+=texture2D(inputBuffer,vUv5)*0.0625;c+=texture2D(inputBuffer,vUv6)*0.125;c+=texture2D(inputBuffer,vUv7)*0.0625;vec4 baseColor=texture2D(supportBuffer,vUv);gl_FragColor=mix(baseColor,c,radius);\n#include <encodings_fragment>\n}",vertexShader:"uniform vec2 texelSize;varying vec2 vUv;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;varying vec2 vUv4;varying vec2 vUv5;varying vec2 vUv6;varying vec2 vUv7;void main(){vUv=position.xy*0.5+0.5;vUv0=vUv+texelSize*vec2(-1.0,1.0);vUv1=vUv+texelSize*vec2(0.0,1.0);vUv2=vUv+texelSize*vec2(1.0,1.0);vUv3=vUv+texelSize*vec2(-1.0,0.0);vUv4=vUv+texelSize*vec2(1.0,0.0);vUv5=vUv+texelSize*vec2(-1.0,-1.0);vUv6=vUv+texelSize*vec2(0.0,-1.0);vUv7=vUv+texelSize*vec2(1.0,-1.0);gl_Position=vec4(position.xy,1.0,1.0);}"}),this.toneMapped=!1}set inputBuffer(e){this.uniforms.inputBuffer.value=e}set supportBuffer(e){this.uniforms.supportBuffer.value=e}get radius(){return this.uniforms.radius.value}set radius(e){this.uniforms.radius.value=e}setSize(e,t){this.uniforms.texelSize.value.set(1/e,1/t)}},T=new r.Camera,E=null,U=class{constructor(e="Pass",t=new r.Scene,i=T){this.name=e,this.renderer=null,this.scene=t,this.camera=i,this.screen=null,this.rtt=!0,this.needsSwap=!0,this.needsDepthTexture=!1,this.enabled=!0}get renderToScreen(){return!this.rtt}set renderToScreen(e){if(this.rtt===e){let t=this.fullscreenMaterial;null!==t&&(t.needsUpdate=!0),this.rtt=!e}}set mainScene(e){}set mainCamera(e){}setRenderer(e){this.renderer=e}isEnabled(){return this.enabled}setEnabled(e){this.enabled=e}get fullscreenMaterial(){return null!==this.screen?this.screen.material:null}set fullscreenMaterial(e){let t=this.screen;null!==t?t.material=e:((t=new r.Mesh(function(){if(null===E){let e=new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),t=new Float32Array([0,0,2,0,0,2]);void 0!==(E=new r.BufferGeometry).setAttribute?(E.setAttribute("position",new r.BufferAttribute(e,3)),E.setAttribute("uv",new r.BufferAttribute(t,2))):(E.addAttribute("position",new r.BufferAttribute(e,3)),E.addAttribute("uv",new r.BufferAttribute(t,2)))}return E}(),e)).frustumCulled=!1,null===this.scene&&(this.scene=new r.Scene),this.scene.add(t),this.screen=t)}getFullscreenMaterial(){return this.fullscreenMaterial}setFullscreenMaterial(e){this.fullscreenMaterial=e}getDepthTexture(){return null}setDepthTexture(e,t=r.BasicDepthPacking){}render(e,t,i,r,n){throw Error("Render method not implemented!")}setSize(e,t){}initialize(e,t,i){}dispose(){for(let e of Object.keys(this)){let t=this[e],i=t instanceof r.WebGLRenderTarget||t instanceof r.Material||t instanceof r.Texture||t instanceof U;i&&this[e].dispose()}}},A=class extends U{constructor(e,t=!0){super("CopyPass"),this.fullscreenMaterial=new p,this.needsSwap=!1,this.renderTarget=e,void 0===e&&(this.renderTarget=new r.WebGLRenderTarget(1,1,{minFilter:r.LinearFilter,magFilter:r.LinearFilter,stencilBuffer:!1,depthBuffer:!1}),this.renderTarget.texture.name="CopyPass.Target"),this.autoResize=t}get resize(){return this.autoResize}set resize(e){this.autoResize=e}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}setAutoResizeEnabled(e){this.autoResize=e}render(e,t,i,r,n){this.fullscreenMaterial.inputBuffer=t.texture,e.setRenderTarget(this.renderToScreen?null:this.renderTarget),e.render(this.scene,this.camera)}setSize(e,t){this.autoResize&&this.renderTarget.setSize(e,t)}initialize(e,t,i){void 0!==i&&(this.renderTarget.texture.type=i,i!==r.UnsignedByteType?this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1":e.outputEncoding===r.sRGBEncoding&&(this.renderTarget.texture.encoding=r.sRGBEncoding))}},M=class extends U{constructor(e,{minLuminance:t=.01,adaptationRate:i=1}={}){super("AdaptiveLuminancePass"),this.fullscreenMaterial=new s,this.needsSwap=!1,this.renderTargetPrevious=new r.WebGLRenderTarget(1,1,{minFilter:r.NearestFilter,magFilter:r.NearestFilter,depthBuffer:!1}),this.renderTargetPrevious.texture.name="Luminance.Previous";let n=this.fullscreenMaterial;n.luminanceBuffer0=this.renderTargetPrevious.texture,n.luminanceBuffer1=e,n.minLuminance=t,n.adaptationRate=i,this.renderTargetAdapted=this.renderTargetPrevious.clone(),this.renderTargetAdapted.texture.name="Luminance.Adapted",this.copyPass=new A(this.renderTargetPrevious,!1)}get texture(){return this.renderTargetAdapted.texture}getTexture(){return this.renderTargetAdapted.texture}set mipLevel1x1(e){this.fullscreenMaterial.mipLevel1x1=e}get adaptationRate(){return this.fullscreenMaterial.adaptationRate}set adaptationRate(e){this.fullscreenMaterial.adaptationRate=e}render(e,t,i,r,n){this.fullscreenMaterial.deltaTime=r,e.setRenderTarget(this.renderToScreen?null:this.renderTargetAdapted),e.render(this.scene,this.camera),this.copyPass.render(e,this.renderTargetAdapted)}},R=class extends U{constructor(){super("ClearMaskPass",null,null),this.needsSwap=!1}render(e,t,i,r,n){let s=e.state.buffers.stencil;s.setLocked(!1),s.setTest(!1)}},B=new r.Color,b=class extends U{constructor(e=!0,t=!0,i=!1){super("ClearPass",null,null),this.needsSwap=!1,this.color=e,this.depth=t,this.stencil=i,this.overrideClearColor=null,this.overrideClearAlpha=-1}setClearFlags(e,t,i){this.color=e,this.depth=t,this.stencil=i}getOverrideClearColor(){return this.overrideClearColor}setOverrideClearColor(e){this.overrideClearColor=e}getOverrideClearAlpha(){return this.overrideClearAlpha}setOverrideClearAlpha(e){this.overrideClearAlpha=e}render(e,t,i,r,n){let s=this.overrideClearColor,a=this.overrideClearAlpha,o=e.getClearAlpha(),l=null!==s,u=a>=0;l?(e.getClearColor(B),e.setClearColor(s,u?a:o)):u&&e.setClearAlpha(a),e.setRenderTarget(this.renderToScreen?null:t),e.clear(this.color,this.depth,this.stencil),l?e.setClearColor(B,o):u&&e.setClearAlpha(o)}},C=class extends r.EventDispatcher{constructor(e,t=-1,i=-1,n=1){super(),this.resizable=e,this.baseSize=new r.Vector2(1,1),this.preferredSize=new r.Vector2(t,i),this.target=this.preferredSize,this.s=n,this.effectiveSize=new r.Vector2,this.addEventListener("change",()=>this.updateEffectiveSize()),this.updateEffectiveSize()}updateEffectiveSize(){let e=this.baseSize,t=this.preferredSize,i=this.effectiveSize,r=this.scale;-1!==t.width?i.width=t.width:-1!==t.height?i.width=Math.round(t.height*(e.width/Math.max(e.height,1))):i.width=Math.round(e.width*r),-1!==t.height?i.height=t.height:-1!==t.width?i.height=Math.round(t.width/Math.max(e.width/Math.max(e.height,1),1)):i.height=Math.round(e.height*r)}get width(){return this.effectiveSize.width}set width(e){this.preferredWidth=e}get height(){return this.effectiveSize.height}set height(e){this.preferredHeight=e}getWidth(){return this.width}getHeight(){return this.height}get scale(){return this.s}set scale(e){this.s!==e&&(this.s=e,this.preferredSize.setScalar(-1),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getScale(){return this.scale}setScale(e){this.scale=e}get baseWidth(){return this.baseSize.width}set baseWidth(e){this.baseSize.width!==e&&(this.baseSize.width=e,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getBaseWidth(){return this.baseWidth}setBaseWidth(e){this.baseWidth=e}get baseHeight(){return this.baseSize.height}set baseHeight(e){this.baseSize.height!==e&&(this.baseSize.height=e,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getBaseHeight(){return this.baseHeight}setBaseHeight(e){this.baseHeight=e}setBaseSize(e,t){(this.baseSize.width!==e||this.baseSize.height!==t)&&(this.baseSize.set(e,t),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}get preferredWidth(){return this.preferredSize.width}set preferredWidth(e){this.preferredSize.width!==e&&(this.preferredSize.width=e,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getPreferredWidth(){return this.preferredWidth}setPreferredWidth(e){this.preferredWidth=e}get preferredHeight(){return this.preferredSize.height}set preferredHeight(e){this.preferredSize.height!==e&&(this.preferredSize.height=e,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getPreferredHeight(){return this.preferredHeight}setPreferredHeight(e){this.preferredHeight=e}setPreferredSize(e,t){(this.preferredSize.width!==e||this.preferredSize.height!==t)&&(this.preferredSize.set(e,t),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}copy(e){this.s=e.scale,this.baseSize.set(e.baseWidth,e.baseHeight),this.preferredSize.set(e.preferredWidth,e.preferredHeight),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height)}static get AUTO_SIZE(){return -1}},D=!1,F=class{constructor(e=null){this.originalMaterials=new Map,this.material=null,this.materials=null,this.materialsBackSide=null,this.materialsDoubleSide=null,this.materialsFlatShaded=null,this.materialsFlatShadedBackSide=null,this.materialsFlatShadedDoubleSide=null,this.setMaterial(e),this.meshCount=0,this.replaceMaterial=e=>{if(e.isMesh){let t;if(e.material.flatShading)switch(e.material.side){case r.DoubleSide:t=this.materialsFlatShadedDoubleSide;break;case r.BackSide:t=this.materialsFlatShadedBackSide;break;default:t=this.materialsFlatShaded}else switch(e.material.side){case r.DoubleSide:t=this.materialsDoubleSide;break;case r.BackSide:t=this.materialsBackSide;break;default:t=this.materials}this.originalMaterials.set(e,e.material),e.isSkinnedMesh?e.material=t[2]:e.isInstancedMesh?e.material=t[1]:e.material=t[0],++this.meshCount}}}setMaterial(e){if(this.disposeMaterials(),this.material=e,null!==e){let t=this.materials=[e.clone(),e.clone(),e.clone()];for(let i of t)i.uniforms=Object.assign({},e.uniforms),i.side=r.FrontSide;t[2].skinning=!0,this.materialsBackSide=t.map(t=>{let i=t.clone();return i.uniforms=Object.assign({},e.uniforms),i.side=r.BackSide,i}),this.materialsDoubleSide=t.map(t=>{let i=t.clone();return i.uniforms=Object.assign({},e.uniforms),i.side=r.DoubleSide,i}),this.materialsFlatShaded=t.map(t=>{let i=t.clone();return i.uniforms=Object.assign({},e.uniforms),i.flatShading=!0,i}),this.materialsFlatShadedBackSide=t.map(t=>{let i=t.clone();return i.uniforms=Object.assign({},e.uniforms),i.flatShading=!0,i.side=r.BackSide,i}),this.materialsFlatShadedDoubleSide=t.map(t=>{let i=t.clone();return i.uniforms=Object.assign({},e.uniforms),i.flatShading=!0,i.side=r.DoubleSide,i})}}render(e,t,i){let r=e.shadowMap.enabled;if(e.shadowMap.enabled=!1,D){let r=this.originalMaterials;for(let n of(this.meshCount=0,t.traverse(this.replaceMaterial),e.render(t,i),r))n[0].material=n[1];this.meshCount!==r.size&&r.clear()}else{let r=t.overrideMaterial;t.overrideMaterial=this.material,e.render(t,i),t.overrideMaterial=r}e.shadowMap.enabled=r}disposeMaterials(){if(null!==this.material){let e=this.materials.concat(this.materialsBackSide).concat(this.materialsDoubleSide).concat(this.materialsFlatShaded).concat(this.materialsFlatShadedBackSide).concat(this.materialsFlatShadedDoubleSide);for(let t of e)t.dispose()}}dispose(){this.originalMaterials.clear(),this.disposeMaterials()}static get workaroundEnabled(){return D}static set workaroundEnabled(e){D=e}},_=class extends U{constructor(e,t,i=null){super("RenderPass",e,t),this.needsSwap=!1,this.clearPass=new b,this.overrideMaterialManager=null===i?null:new F(i),this.ignoreBackground=!1,this.skipShadowMapUpdate=!1,this.selection=null}set mainScene(e){this.scene=e}set mainCamera(e){this.camera=e}get renderToScreen(){return super.renderToScreen}set renderToScreen(e){super.renderToScreen=e,this.clearPass.renderToScreen=e}get overrideMaterial(){let e=this.overrideMaterialManager;return null!==e?e.material:null}set overrideMaterial(e){let t=this.overrideMaterialManager;null!==e?null!==t?t.setMaterial(e):this.overrideMaterialManager=new F(e):null!==t&&(t.dispose(),this.overrideMaterialManager=null)}getOverrideMaterial(){return this.overrideMaterial}setOverrideMaterial(e){this.overrideMaterial=e}get clear(){return this.clearPass.enabled}set clear(e){this.clearPass.enabled=e}getSelection(){return this.selection}setSelection(e){this.selection=e}isBackgroundDisabled(){return this.ignoreBackground}setBackgroundDisabled(e){this.ignoreBackground=e}isShadowMapDisabled(){return this.skipShadowMapUpdate}setShadowMapDisabled(e){this.skipShadowMapUpdate=e}getClearPass(){return this.clearPass}render(e,t,i,r,n){let s=this.scene,a=this.camera,o=this.selection,l=a.layers.mask,u=s.background,h=e.shadowMap.autoUpdate,c=this.renderToScreen?null:t;null!==o&&a.layers.set(o.getLayer()),this.skipShadowMapUpdate&&(e.shadowMap.autoUpdate=!1),(this.ignoreBackground||null!==this.clearPass.overrideClearColor)&&(s.background=null),this.clearPass.enabled&&this.clearPass.render(e,t),e.setRenderTarget(c),null!==this.overrideMaterialManager?this.overrideMaterialManager.render(e,s,a):e.render(s,a),a.layers.mask=l,s.background=u,e.shadowMap.autoUpdate=h}},P=class extends U{constructor({normalBuffer:e=null,resolutionScale:t=.5,width:i=C.AUTO_SIZE,height:n=C.AUTO_SIZE,resolutionX:s=i,resolutionY:a=n}={}){super("DepthDownsamplingPass");let o=new m;o.normalBuffer=e,this.fullscreenMaterial=o,this.needsDepthTexture=!0,this.needsSwap=!1,this.renderTarget=new r.WebGLRenderTarget(1,1,{minFilter:r.NearestFilter,magFilter:r.NearestFilter,depthBuffer:!1,type:r.FloatType}),this.renderTarget.texture.name="DepthDownsamplingPass.Target",this.renderTarget.texture.generateMipmaps=!1;let l=this.resolution=new C(this,s,a,t);l.addEventListener("change",e=>this.setSize(l.baseWidth,l.baseHeight))}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}getResolution(){return this.resolution}setDepthTexture(e,t=r.BasicDepthPacking){this.fullscreenMaterial.depthBuffer=e,this.fullscreenMaterial.depthPacking=t}render(e,t,i,r,n){e.setRenderTarget(this.renderToScreen?null:this.renderTarget),e.render(this.scene,this.camera)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t),this.renderTarget.setSize(i.width,i.height),this.fullscreenMaterial.setSize(e,t)}initialize(e,t,i){let r=e.getContext(),n=r.getExtension("EXT_color_buffer_float")||r.getExtension("EXT_color_buffer_half_float");if(!n)throw Error("Rendering to float texture is not supported.")}};function I(e,t,i){for(let r of t){let t="$1"+e+r.charAt(0).toUpperCase()+r.slice(1),n=RegExp("([^\\.])(\\b"+r+"\\b)","g");for(let e of i.entries())null!==e[1]&&i.set(e[0],e[1].replace(n,t))}}new Float32Array([255/256/16777216,255/256/65536,255/256/256,255/256]);var L=class extends U{constructor(e,...t){super("EffectPass"),this.fullscreenMaterial=new x(null,null,null,e),this.listener=e=>this.handleEvent(e),this.effects=[],this.setEffects(t),this.skipRendering=!1,this.minTime=1,this.maxTime=Number.POSITIVE_INFINITY,this.timeScale=1}set mainScene(e){for(let t of this.effects)t.mainScene=e}set mainCamera(e){for(let t of(this.fullscreenMaterial.copyCameraSettings(e),this.effects))t.mainCamera=e}get encodeOutput(){return this.fullscreenMaterial.encodeOutput}set encodeOutput(e){this.fullscreenMaterial.encodeOutput=e}get dithering(){return this.fullscreenMaterial.dithering}set dithering(e){let t=this.fullscreenMaterial;t.dithering=e,t.needsUpdate=!0}setEffects(e){for(let e of this.effects)e.removeEventListener("change",this.listener);for(let t of(this.effects=e.sort((e,t)=>t.attributes-e.attributes),this.effects))t.addEventListener("change",this.listener)}updateMaterial(){let e=new X,t=0;for(let i of this.effects)if(i.blendMode.blendFunction===a.DST)e.attributes|=i.getAttributes()&o.DEPTH;else if((e.attributes&i.getAttributes()&o.CONVOLUTION)!=0)throw Error(`Convolution effects cannot be merged (${i.name})`);else!function(e,t,i){var n,s,a,u,h;let c=t.getFragmentShader(),d=t.getVertexShader(),f=void 0!==c&&/mainImage/.test(c),v=void 0!==c&&/mainUv/.test(c);if(i.attributes|=t.getAttributes(),void 0===c)throw Error(`Missing fragment shader (${t.name})`);if(v&&(i.attributes&o.CONVOLUTION)!=0)throw Error(`Effects that transform UVs are incompatible with convolution effects (${t.name})`);if(f||v){let p=/\w+\s+(\w+)\([\w\s,]*\)\s*{/g,m=i.shaderParts,g=null!=(n=m.get(l.FRAGMENT_HEAD))?n:"",x=null!=(s=m.get(l.FRAGMENT_MAIN_UV))?s:"",y=null!=(a=m.get(l.FRAGMENT_MAIN_IMAGE))?a:"",S=null!=(u=m.get(l.VERTEX_HEAD))?u:"",w=null!=(h=m.get(l.VERTEX_MAIN_SUPPORT))?h:"",T=new Set,E=new Set;if(v&&(x+=`	${e}MainUv(UV);
`,i.uvTransformation=!0),null!==d&&/mainSupport/.test(d)){let t=/mainSupport *\([\w\s]*?uv\s*?\)/.test(d);for(let r of(w+=`	${e}MainSupport(`+(t?"vUv);\n":");\n"),d.matchAll(/(?:varying\s+\w+\s+([\S\s]*?);)/g)))for(let e of r[1].split(/\s*,\s*/))i.varyings.add(e),T.add(e),E.add(e);for(let e of d.matchAll(p))E.add(e[1])}for(let e of c.matchAll(p))E.add(e[1]);for(let e of t.defines.keys())E.add(e.replace(/\([\w\s,]*\)/g,""));for(let e of t.uniforms.keys())E.add(e);E.delete("while"),E.delete("for"),E.delete("if"),t.uniforms.forEach((t,r)=>i.uniforms.set(e+r.charAt(0).toUpperCase()+r.slice(1),t)),t.defines.forEach((t,r)=>i.defines.set(e+r.charAt(0).toUpperCase()+r.slice(1),t));let U=new Map([["fragment",c],["vertex",d]]);I(e,E,i.defines),I(e,E,U),c=U.get("fragment"),d=U.get("vertex");let A=t.blendMode;if(i.blendModes.set(A.blendFunction,A),f){null!==t.inputColorSpace&&t.inputColorSpace!==i.colorSpace&&(y+=t.inputColorSpace===r.sRGBEncoding?"color0 = LinearTosRGB(color0);\n	":"color0 = sRGBToLinear(color0);\n	"),null!==t.outputColorSpace?i.colorSpace=t.outputColorSpace:null!==t.inputColorSpace&&(i.colorSpace=t.inputColorSpace),y+=`${e}MainImage(color0, UV, `,(i.attributes&o.DEPTH)!=0&&/MainImage *\([\w\s,]*?depth[\w\s,]*?\)/.test(c)&&(y+="depth, ",i.readDepth=!0),y+="color1);\n	";let n=e+"BlendOpacity";i.uniforms.set(n,A.opacity),y+=`color0 = blend${A.blendFunction}(color0, color1, ${n});

	`,g+=`uniform float ${n};

`}if(g+=c+"\n",null!==d&&(S+=d+"\n"),m.set(l.FRAGMENT_HEAD,g),m.set(l.FRAGMENT_MAIN_UV,x),m.set(l.FRAGMENT_MAIN_IMAGE,y),m.set(l.VERTEX_HEAD,S),m.set(l.VERTEX_MAIN_SUPPORT,w),null!==t.extensions)for(let e of t.extensions)i.extensions.add(e)}else throw Error(`Could not find mainImage or mainUv function (${t.name})`)}("e"+t++,i,e);let i=e.shaderParts.get(l.FRAGMENT_HEAD),n=e.shaderParts.get(l.FRAGMENT_MAIN_IMAGE),s=e.shaderParts.get(l.FRAGMENT_MAIN_UV),u=/\bblend\b/g;for(let t of e.blendModes.values())i+=t.getShaderCode().replace(u,`blend${t.blendFunction}`)+"\n";(e.attributes&o.DEPTH)!=0?(e.readDepth&&(n="float depth = readDepth(UV);\n\n	"+n),this.needsDepthTexture=null===this.getDepthTexture()):this.needsDepthTexture=!1,e.colorSpace===r.sRGBEncoding&&(n+="color0 = sRGBToLinear(color0);\n	"),e.uvTransformation?(s="vec2 transformedUv = vUv;\n"+s,e.defines.set("UV","transformedUv")):e.defines.set("UV","vUv"),e.shaderParts.set(l.FRAGMENT_HEAD,i),e.shaderParts.set(l.FRAGMENT_MAIN_IMAGE,n),e.shaderParts.set(l.FRAGMENT_MAIN_UV,s),e.shaderParts.forEach((e,t,i)=>i.set(t,null==e?void 0:e.trim().replace(/^#/,"\n#"))),this.skipRendering=0===t,this.needsSwap=!this.skipRendering,this.fullscreenMaterial.setShaderData(e)}recompile(){this.updateMaterial()}getDepthTexture(){return this.fullscreenMaterial.depthBuffer}setDepthTexture(e,t=r.BasicDepthPacking){for(let i of(this.fullscreenMaterial.depthBuffer=e,this.fullscreenMaterial.depthPacking=t,this.effects))i.setDepthTexture(e,t)}render(e,t,i,r,n){for(let i of this.effects)i.update(e,t,r);if(!this.skipRendering||this.renderToScreen){let n=this.fullscreenMaterial;n.inputBuffer=t.texture,n.time+=r*this.timeScale,e.setRenderTarget(this.renderToScreen?null:i),e.render(this.scene,this.camera)}}setSize(e,t){for(let i of(this.fullscreenMaterial.setSize(e,t),this.effects))i.setSize(e,t)}initialize(e,t,i){for(let r of(this.renderer=e,this.effects))r.initialize(e,t,i);this.updateMaterial(),void 0!==i&&i!==r.UnsignedByteType&&(this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1")}dispose(){for(let e of(super.dispose(),this.effects))e.removeEventListener("change",this.listener),e.dispose()}handleEvent(e){"change"===e.type&&this.recompile()}},z=class extends U{constructor({kernelSize:e=u.MEDIUM,resolutionScale:t=.5,width:i=C.AUTO_SIZE,height:n=C.AUTO_SIZE,resolutionX:s=i,resolutionY:a=n}={}){super("KawaseBlurPass"),this.renderTargetA=new r.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTargetA.texture.name="Blur.Target.A",this.renderTargetB=this.renderTargetA.clone(),this.renderTargetB.texture.name="Blur.Target.B";let o=this.resolution=new C(this,s,a,t);o.addEventListener("change",e=>this.setSize(o.baseWidth,o.baseHeight)),this._blurMaterial=new v,this._blurMaterial.kernelSize=e,this.copyMaterial=new p}getResolution(){return this.resolution}get blurMaterial(){return this._blurMaterial}set blurMaterial(e){this._blurMaterial=e}get dithering(){return this.copyMaterial.dithering}set dithering(e){this.copyMaterial.dithering=e}get kernelSize(){return this.blurMaterial.kernelSize}set kernelSize(e){this.blurMaterial.kernelSize=e}get width(){return this.resolution.width}set width(e){this.resolution.preferredWidth=e}get height(){return this.resolution.height}set height(e){this.resolution.preferredHeight=e}get scale(){return this.blurMaterial.scale}set scale(e){this.blurMaterial.scale=e}getScale(){return this.blurMaterial.scale}setScale(e){this.blurMaterial.scale=e}getKernelSize(){return this.kernelSize}setKernelSize(e){this.kernelSize=e}getResolutionScale(){return this.resolution.scale}setResolutionScale(e){this.resolution.scale=e}render(e,t,i,r,n){let s=this.scene,a=this.camera,o=this.renderTargetA,l=this.renderTargetB,u=this.blurMaterial,h=u.kernelSequence,c=t;this.fullscreenMaterial=u;for(let t=0,i=h.length;t<i;++t){let i=(1&t)==0?o:l;u.kernel=h[t],u.inputBuffer=c.texture,e.setRenderTarget(i),e.render(s,a),c=i}this.fullscreenMaterial=this.copyMaterial,this.copyMaterial.inputBuffer=c.texture,e.setRenderTarget(this.renderToScreen?null:i),e.render(s,a)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t);let r=i.width,n=i.height;this.renderTargetA.setSize(r,n),this.renderTargetB.setSize(r,n),this.blurMaterial.setSize(e,t)}initialize(e,t,i){void 0!==i&&(this.renderTargetA.texture.type=i,this.renderTargetB.texture.type=i,i!==r.UnsignedByteType?(this.blurMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1",this.copyMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1"):e.outputEncoding===r.sRGBEncoding&&(this.renderTargetA.texture.encoding=r.sRGBEncoding,this.renderTargetB.texture.encoding=r.sRGBEncoding))}static get AUTO_SIZE(){return C.AUTO_SIZE}},N=class extends U{constructor({renderTarget:e,luminanceRange:t,colorOutput:i,resolutionScale:n=1,width:s=C.AUTO_SIZE,height:a=C.AUTO_SIZE,resolutionX:o=s,resolutionY:l=a}={}){super("LuminancePass"),this.fullscreenMaterial=new y(i,t),this.needsSwap=!1,this.renderTarget=e,void 0===this.renderTarget&&(this.renderTarget=new r.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="LuminancePass.Target");let u=this.resolution=new C(this,o,l,n);u.addEventListener("change",e=>this.setSize(u.baseWidth,u.baseHeight))}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}getResolution(){return this.resolution}render(e,t,i,r,n){let s=this.fullscreenMaterial;s.inputBuffer=t.texture,e.setRenderTarget(this.renderToScreen?null:this.renderTarget),e.render(this.scene,this.camera)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t),this.renderTarget.setSize(i.width,i.height)}initialize(e,t,i){void 0!==i&&i!==r.UnsignedByteType&&(this.renderTarget.texture.type=i,this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1")}},G=class extends U{constructor(e,t){super("MaskPass",e,t),this.needsSwap=!1,this.clearPass=new b(!1,!1,!0),this.inverse=!1}set mainScene(e){this.scene=e}set mainCamera(e){this.camera=e}get inverted(){return this.inverse}set inverted(e){this.inverse=e}get clear(){return this.clearPass.enabled}set clear(e){this.clearPass.enabled=e}getClearPass(){return this.clearPass}isInverted(){return this.inverted}setInverted(e){this.inverted=e}render(e,t,i,r,n){let s=e.getContext(),a=e.state.buffers,o=this.scene,l=this.camera,u=this.clearPass,h=this.inverted?0:1;a.color.setMask(!1),a.depth.setMask(!1),a.color.setLocked(!0),a.depth.setLocked(!0),a.stencil.setTest(!0),a.stencil.setOp(s.REPLACE,s.REPLACE,s.REPLACE),a.stencil.setFunc(s.ALWAYS,h,4294967295),a.stencil.setClear(1-h),a.stencil.setLocked(!0),this.clearPass.enabled&&(this.renderToScreen?u.render(e,null):(u.render(e,t),u.render(e,i))),this.renderToScreen?(e.setRenderTarget(null),e.render(o,l)):(e.setRenderTarget(t),e.render(o,l),e.setRenderTarget(i),e.render(o,l)),a.color.setLocked(!1),a.depth.setLocked(!1),a.stencil.setLocked(!1),a.stencil.setFunc(s.EQUAL,1,4294967295),a.stencil.setOp(s.KEEP,s.KEEP,s.KEEP),a.stencil.setLocked(!0)}},O=class extends U{constructor(){super("MipmapBlurPass"),this.needsSwap=!1,this.renderTarget=new r.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="Upsampling.Mipmap0",this.downsamplingMipmaps=[],this.upsamplingMipmaps=[],this.downsamplingMaterial=new g,this.upsamplingMaterial=new w,this.resolution=new r.Vector2}get texture(){return this.renderTarget.texture}get levels(){return this.downsamplingMipmaps.length}set levels(e){if(this.levels!==e){let t=this.renderTarget;this.dispose(),this.downsamplingMipmaps=[],this.upsamplingMipmaps=[];for(let i=0;i<e;++i){let e=t.clone();e.texture.name="Downsampling.Mipmap"+i,this.downsamplingMipmaps.push(e)}this.upsamplingMipmaps.push(t);for(let i=1,r=e-1;i<r;++i){let e=t.clone();e.texture.name="Upsampling.Mipmap"+i,this.upsamplingMipmaps.push(e)}this.setSize(this.resolution.x,this.resolution.y)}}get radius(){return this.upsamplingMaterial.radius}set radius(e){this.upsamplingMaterial.radius=e}render(e,t,i,r,n){let{scene:s,camera:a}=this,{downsamplingMaterial:o,upsamplingMaterial:l}=this,{downsamplingMipmaps:u,upsamplingMipmaps:h}=this,c=t;this.fullscreenMaterial=o;for(let t=0,i=u.length;t<i;++t){let i=u[t];o.setSize(c.width,c.height),o.inputBuffer=c.texture,e.setRenderTarget(i),e.render(s,a),c=i}this.fullscreenMaterial=l;for(let t=h.length-1;t>=0;--t){let i=h[t];l.setSize(c.width,c.height),l.inputBuffer=c.texture,l.supportBuffer=u[t].texture,e.setRenderTarget(i),e.render(s,a),c=i}}setSize(e,t){let i=this.resolution;i.set(e,t);let r=i.width,n=i.height;for(let e=0,t=this.downsamplingMipmaps.length;e<t;++e)r=Math.round(.5*r),n=Math.round(.5*n),this.downsamplingMipmaps[e].setSize(r,n),e<this.upsamplingMipmaps.length&&this.upsamplingMipmaps[e].setSize(r,n)}initialize(e,t,i){if(void 0!==i){let t=this.downsamplingMipmaps.concat(this.upsamplingMipmaps);for(let e of t)e.texture.type=i;if(i!==r.UnsignedByteType)this.downsamplingMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1",this.upsamplingMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1";else if(e.outputEncoding===r.sRGBEncoding)for(let e of t)e.texture.encoding=r.sRGBEncoding}}dispose(){for(let e of(super.dispose(),this.downsamplingMipmaps.concat(this.upsamplingMipmaps)))e.dispose()}},H=class extends U{constructor(e,t,{renderTarget:i,resolutionScale:n=1,width:s=C.AUTO_SIZE,height:a=C.AUTO_SIZE,resolutionX:o=s,resolutionY:l=a}={}){super("NormalPass"),this.needsSwap=!1,this.renderPass=new _(e,t,new r.MeshNormalMaterial);let u=this.renderPass;u.ignoreBackground=!0,u.skipShadowMapUpdate=!0;let h=u.getClearPass();h.overrideClearColor=new r.Color(7829503),h.overrideClearAlpha=1,this.renderTarget=i,void 0===this.renderTarget&&(this.renderTarget=new r.WebGLRenderTarget(1,1,{minFilter:r.NearestFilter,magFilter:r.NearestFilter}),this.renderTarget.texture.name="NormalPass.Target");let c=this.resolution=new C(this,o,l,n);c.addEventListener("change",e=>this.setSize(c.baseWidth,c.baseHeight))}set mainScene(e){this.renderPass.mainScene=e}set mainCamera(e){this.renderPass.mainCamera=e}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}getResolution(){return this.resolution}getResolutionScale(){return this.resolution.scale}setResolutionScale(e){this.resolution.scale=e}render(e,t,i,r,n){let s=this.renderToScreen?null:this.renderTarget;this.renderPass.render(e,s,s)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t),this.renderTarget.setSize(i.width,i.height)}},k=class extends z{constructor({offset:e=0,rotation:t=0,focusArea:i=.4,feather:r=.3,kernelSize:n=u.MEDIUM,resolutionScale:s=.5,resolutionX:a=C.AUTO_SIZE,resolutionY:o=C.AUTO_SIZE}={}){super({kernelSize:n,resolutionScale:s,resolutionX:a,resolutionY:o}),this.blurMaterial=new S({kernelSize:n,offset:e,rotation:t,focusArea:i,feather:r})}},V=class{constructor(){this.previousTime=0,this.currentTime=0,this._delta=0,this._elapsed=0,this._fixedDelta=1e3/60,this.timescale=1,this.useFixedDelta=!1,this._autoReset=!1}get autoReset(){return this._autoReset}set autoReset(e){"undefined"!=typeof document&&void 0!==document.hidden&&(e?document.addEventListener("visibilitychange",this):document.removeEventListener("visibilitychange",this),this._autoReset=e)}get delta(){return .001*this._delta}get fixedDelta(){return .001*this._fixedDelta}set fixedDelta(e){this._fixedDelta=1e3*e}get elapsed(){return .001*this._elapsed}update(e){this.useFixedDelta?this._delta=this.fixedDelta:(this.previousTime=this.currentTime,this.currentTime=void 0!==e?e:performance.now(),this._delta=this.currentTime-this.previousTime),this._delta*=this.timescale,this._elapsed+=this._delta}reset(){this._delta=0,this._elapsed=0,this.currentTime=performance.now()}handleEvent(e){document.hidden||(this.currentTime=performance.now())}dispose(){this.autoReset=!1}},W=class{constructor(e=null,{depthBuffer:t=!0,stencilBuffer:i=!1,multisampling:r=0,frameBufferType:n}={}){this.renderer=null,this.inputBuffer=this.createBuffer(t,i,n,r),this.outputBuffer=this.inputBuffer.clone(),this.copyPass=new A,this.depthTexture=null,this.passes=[],this.timer=new V,this.autoRenderToScreen=!0,this.setRenderer(e)}get multisampling(){return this.inputBuffer.samples||0}set multisampling(e){let t=this.inputBuffer,i=this.multisampling;i>0&&e>0?(this.inputBuffer.samples=e,this.outputBuffer.samples=e,this.inputBuffer.dispose(),this.outputBuffer.dispose()):i!==e&&(this.inputBuffer.dispose(),this.outputBuffer.dispose(),this.inputBuffer=this.createBuffer(t.depthBuffer,t.stencilBuffer,t.texture.type,e),this.inputBuffer.depthTexture=this.depthTexture,this.outputBuffer=this.inputBuffer.clone())}getTimer(){return this.timer}getRenderer(){return this.renderer}setRenderer(e){if(this.renderer=e,null!==e){let t=e.getSize(new r.Vector2),i=e.getContext().getContextAttributes().alpha,n=this.inputBuffer.texture.type;for(let s of(n===r.UnsignedByteType&&e.outputEncoding===r.sRGBEncoding&&(this.inputBuffer.texture.encoding=r.sRGBEncoding,this.outputBuffer.texture.encoding=r.sRGBEncoding,this.inputBuffer.dispose(),this.outputBuffer.dispose()),e.autoClear=!1,this.setSize(t.width,t.height),this.passes))s.initialize(e,i,n)}}replaceRenderer(e,t=!0){let i=this.renderer,r=i.domElement.parentNode;return this.setRenderer(e),t&&null!==r&&(r.removeChild(i.domElement),r.appendChild(e.domElement)),i}createDepthTexture(){let e=this.depthTexture=new r.DepthTexture;return this.inputBuffer.depthTexture=e,this.inputBuffer.dispose(),this.inputBuffer.stencilBuffer?(e.format=r.DepthStencilFormat,e.type=r.UnsignedInt248Type):e.type=r.UnsignedIntType,e}deleteDepthTexture(){if(null!==this.depthTexture)for(let e of(this.depthTexture.dispose(),this.depthTexture=null,this.inputBuffer.depthTexture=null,this.inputBuffer.dispose(),this.passes))e.setDepthTexture(null)}createBuffer(e,t,i,n){let s=this.renderer,a=null===s?new r.Vector2:s.getDrawingBufferSize(new r.Vector2),o={minFilter:r.LinearFilter,magFilter:r.LinearFilter,stencilBuffer:t,depthBuffer:e,type:i},l=new r.WebGLRenderTarget(a.width,a.height,o);return n>0&&(l.ignoreDepthForMultisampleCopy=!1,l.samples=n),i===r.UnsignedByteType&&null!==s&&s.outputEncoding===r.sRGBEncoding&&(l.texture.encoding=r.sRGBEncoding),l.texture.name="EffectComposer.Buffer",l.texture.generateMipmaps=!1,l}setMainScene(e){for(let t of this.passes)t.mainScene=e}setMainCamera(e){for(let t of this.passes)t.mainCamera=e}addPass(e,t){let i=this.passes,n=this.renderer,s=n.getDrawingBufferSize(new r.Vector2),a=n.getContext().getContextAttributes().alpha,o=this.inputBuffer.texture.type;if(e.setRenderer(n),e.setSize(s.width,s.height),e.initialize(n,a,o),this.autoRenderToScreen&&(i.length>0&&(i[i.length-1].renderToScreen=!1),e.renderToScreen&&(this.autoRenderToScreen=!1)),void 0!==t?i.splice(t,0,e):i.push(e),this.autoRenderToScreen&&(i[i.length-1].renderToScreen=!0),e.needsDepthTexture||null!==this.depthTexture){if(null===this.depthTexture){let t=this.createDepthTexture();for(e of i)e.setDepthTexture(t)}else e.setDepthTexture(this.depthTexture)}}removePass(e){let t=this.passes,i=t.indexOf(e),r=-1!==i&&t.splice(i,1).length>0;if(r){if(null!==this.depthTexture){let i=t.reduce((e,t)=>e||t.needsDepthTexture,!1);i||(e.getDepthTexture()===this.depthTexture&&e.setDepthTexture(null),this.deleteDepthTexture())}this.autoRenderToScreen&&i===t.length&&(e.renderToScreen=!1,t.length>0&&(t[t.length-1].renderToScreen=!0))}}removeAllPasses(){let e=this.passes;this.deleteDepthTexture(),e.length>0&&(this.autoRenderToScreen&&(e[e.length-1].renderToScreen=!1),this.passes=[])}render(e){let t,i,r;let n=this.renderer,s=this.copyPass,a=this.inputBuffer,o=this.outputBuffer,l=!1;for(let u of(void 0===e&&(this.timer.update(),e=this.timer.delta),this.passes))u.enabled&&(u.render(n,a,o,e,l),u.needsSwap&&(l&&(s.renderToScreen=u.renderToScreen,t=n.getContext(),(i=n.state.buffers.stencil).setFunc(t.NOTEQUAL,1,4294967295),s.render(n,a,o,e,l),i.setFunc(t.EQUAL,1,4294967295)),r=a,a=o,o=r),u instanceof G?l=!0:u instanceof R&&(l=!1))}setSize(e,t,i){let n=this.renderer,s=n.getSize(new r.Vector2);(void 0===e||void 0===t)&&(e=s.width,t=s.height),(s.width!==e||s.height!==t)&&n.setSize(e,t,i);let a=n.getDrawingBufferSize(new r.Vector2);for(let e of(this.inputBuffer.setSize(a.width,a.height),this.outputBuffer.setSize(a.width,a.height),this.passes))e.setSize(a.width,a.height)}reset(){let e=this.timer.autoReset;this.dispose(),this.autoRenderToScreen=!0,this.timer.autoReset=e}dispose(){for(let e of this.passes)e.dispose();this.passes=[],null!==this.inputBuffer&&this.inputBuffer.dispose(),null!==this.outputBuffer&&this.outputBuffer.dispose(),this.deleteDepthTexture(),this.copyPass.dispose(),this.timer.dispose()}},X=class{constructor(){this.shaderParts=new Map([[l.FRAGMENT_HEAD,null],[l.FRAGMENT_MAIN_UV,null],[l.FRAGMENT_MAIN_IMAGE,null],[l.VERTEX_HEAD,null],[l.VERTEX_MAIN_SUPPORT,null]]),this.defines=new Map,this.uniforms=new Map,this.blendModes=new Map,this.extensions=new Set,this.attributes=o.NONE,this.varyings=new Set,this.uvTransformation=!1,this.readDepth=!1,this.colorSpace=r.LinearEncoding}};Set;var Z=new Map([[a.ADD,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x+y,opacity);}"],[a.ALPHA,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y,min(y.a,opacity));}"],[a.AVERAGE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,(x+y)*0.5,opacity);}"],[a.COLOR,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(yHSL.rg,xHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}"],[a.COLOR_BURN,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(step(0.0,y)*(1.0-min(vec4(1.0),(1.0-x)/y)),vec4(1.0),step(1.0,x));return mix(x,z,opacity);}"],[a.COLOR_DODGE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=step(0.0,x)*mix(min(vec4(1.0),x/max(1.0-y,1e-9)),vec4(1.0),step(1.0,y));return mix(x,z,opacity);}"],[a.DARKEN,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,min(x,y),opacity);}"],[a.DIFFERENCE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,abs(x-y),opacity);}"],[a.DIVIDE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x/max(y,1e-12),opacity);}"],[a.DST,null],[a.EXCLUSION,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,(x+y-2.0*x*y),opacity);}"],[a.HARD_LIGHT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 a=min(x,1.0),b=min(y,1.0);vec4 z=mix(2.0*a*b,1.0-2.0*(1.0-a)*(1.0-b),step(0.5,y));return mix(x,z,opacity);}"],[a.HARD_MIX,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,step(1.0,x+y),opacity);}"],[a.HUE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(yHSL.r,xHSL.gb));return vec4(mix(x.rgb,z,opacity),y.a);}"],[a.INVERT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,1.0-y,opacity);}"],[a.INVERT_RGB,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y*(1.0-x),opacity);}"],[a.LIGHTEN,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,max(x,y),opacity);}"],[a.LINEAR_BURN,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,clamp(y+x-1.0,0.0,1.0),opacity);}"],[a.LINEAR_DODGE,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,min(x+y,1.0),opacity);}"],[a.LINEAR_LIGHT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,clamp(2.0*y+x-1.0,0.0,1.0),opacity);}"],[a.LUMINOSITY,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(xHSL.rg,yHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}"],[a.MULTIPLY,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x*y,opacity);}"],[a.NEGATION,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,1.0-abs(1.0-x-y),opacity);}"],[a.NORMAL,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y,opacity);}"],[a.OVERLAY,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(2.0*y*x,1.0-2.0*(1.0-y)*(1.0-x),step(0.5,x));return mix(x,z,opacity);}"],[a.PIN_LIGHT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 y2=2.0*y;vec4 z=mix(mix(y2,x,step(0.5*x,y)),max(vec4(0.0),y2-1.0),step(x,(y2-1.0)));return mix(x,z,opacity);}"],[a.REFLECT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(min(x*x/max(1.0-y,1e-12),1.0),y,step(1.0,y));return mix(x,z,opacity);}"],[a.SATURATION,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(xHSL.r,yHSL.g,xHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}"],[a.SCREEN,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x+y-min(x*y,1.0),opacity);}"],[a.SOFT_LIGHT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 y2=2.0*y;vec4 w=step(0.5,y);vec4 z=mix(x-(1.0-y2)*x*(1.0-x),mix(x+(y2-1.0)*(sqrt(x)-x),x+(y2-1.0)*x*((16.0*x-12.0)*x+3.0),w*(1.0-step(0.25,x))),w);return mix(x,z,opacity);}"],[a.SRC,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return y;}"],[a.SUBTRACT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,max(x+y-1.0,0.0),opacity);}"],[a.VIVID_LIGHT,"vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(max(1.0-min((1.0-x)/(2.0*y),1.0),0.0),min(x/(2.0*(1.0-y)),1.0),step(0.5,y));return mix(x,z,opacity);}"]]),j=class extends r.EventDispatcher{constructor(e,t=1){super(),this._blendFunction=e,this.opacity=new r.Uniform(t)}getOpacity(){return this.opacity.value}setOpacity(e){this.opacity.value=e}get blendFunction(){return this._blendFunction}set blendFunction(e){this._blendFunction=e,this.dispatchEvent({type:"change"})}getBlendFunction(){return this.blendFunction}setBlendFunction(e){this.blendFunction=e}getShaderCode(){return Z.get(this.blendFunction)}},q=class extends r.EventDispatcher{constructor(e,t,{attributes:i=o.NONE,blendFunction:n=a.NORMAL,defines:s=new Map,uniforms:l=new Map,extensions:u=null,vertexShader:h=null}={}){super(),this.name=e,this.renderer=null,this.attributes=i,this.fragmentShader=t,this.vertexShader=h,this.defines=s,this.uniforms=l,this.extensions=u,this.blendMode=new j(n),this.blendMode.addEventListener("change",e=>this.setChanged()),this._inputColorSpace=r.LinearEncoding,this._outputColorSpace=null}get inputColorSpace(){return this._inputColorSpace}set inputColorSpace(e){this._inputColorSpace=e,this.setChanged()}get outputColorSpace(){return this._outputColorSpace}set outputColorSpace(e){this._outputColorSpace=e,this.setChanged()}set mainScene(e){}set mainCamera(e){}getName(){return this.name}setRenderer(e){this.renderer=e}getDefines(){return this.defines}getUniforms(){return this.uniforms}getExtensions(){return this.extensions}getBlendMode(){return this.blendMode}getAttributes(){return this.attributes}setAttributes(e){this.attributes=e,this.setChanged()}getFragmentShader(){return this.fragmentShader}setFragmentShader(e){this.fragmentShader=e,this.setChanged()}getVertexShader(){return this.vertexShader}setVertexShader(e){this.vertexShader=e,this.setChanged()}setChanged(){this.dispatchEvent({type:"change"})}setDepthTexture(e,t=r.BasicDepthPacking){}update(e,t,i){}setSize(e,t){}initialize(e,t,i){}dispose(){for(let e of Object.keys(this)){let t=this[e],i=t instanceof r.WebGLRenderTarget||t instanceof r.Material||t instanceof r.Texture||t instanceof U;i&&this[e].dispose()}}},K=class extends q{constructor({blendFunction:e=a.SCREEN,luminanceThreshold:t=.9,luminanceSmoothing:i=.025,mipmapBlur:n=!1,intensity:s=1,radius:o=.85,levels:l=8,kernelSize:h=u.LARGE,resolutionScale:c=.5,width:d=C.AUTO_SIZE,height:f=C.AUTO_SIZE,resolutionX:v=d,resolutionY:p=f}={}){super("BloomEffect","#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D map;\n#else\nuniform lowp sampler2D map;\n#endif\nuniform float intensity;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){outputColor=texture2D(map,uv)*intensity;}",{blendFunction:e,uniforms:new Map([["map",new r.Uniform(null)],["intensity",new r.Uniform(s)]])}),this.renderTarget=new r.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="Bloom.Target",this.blurPass=new z({kernelSize:h}),this.luminancePass=new N({colorOutput:!0}),this.luminanceMaterial.threshold=t,this.luminanceMaterial.smoothing=i,this.mipmapBlurPass=new O,this.mipmapBlurPass.enabled=n,this.mipmapBlurPass.radius=o,this.mipmapBlurPass.levels=l,this.uniforms.get("map").value=n?this.mipmapBlurPass.texture:this.renderTarget.texture;let m=this.resolution=new C(this,v,p,c);m.addEventListener("change",e=>this.setSize(m.baseWidth,m.baseHeight))}get texture(){return this.mipmapBlurPass.enabled?this.mipmapBlurPass.texture:this.renderTarget.texture}getTexture(){return this.texture}getResolution(){return this.resolution}getBlurPass(){return this.blurPass}getLuminancePass(){return this.luminancePass}get luminanceMaterial(){return this.luminancePass.fullscreenMaterial}getLuminanceMaterial(){return this.luminancePass.fullscreenMaterial}get width(){return this.resolution.width}set width(e){this.resolution.preferredWidth=e}get height(){return this.resolution.height}set height(e){this.resolution.preferredHeight=e}get dithering(){return this.blurPass.dithering}set dithering(e){this.blurPass.dithering=e}get kernelSize(){return this.blurPass.kernelSize}set kernelSize(e){this.blurPass.kernelSize=e}get distinction(){return console.warn(this.name,"distinction was removed"),1}set distinction(e){console.warn(this.name,"distinction was removed")}get intensity(){return this.uniforms.get("intensity").value}set intensity(e){this.uniforms.get("intensity").value=e}getIntensity(){return this.intensity}setIntensity(e){this.intensity=e}getResolutionScale(){return this.resolution.scale}setResolutionScale(e){this.resolution.scale=e}update(e,t,i){let r=this.renderTarget,n=this.luminancePass;n.enabled?(n.render(e,t),this.mipmapBlurPass.enabled?this.mipmapBlurPass.render(e,n.renderTarget):this.blurPass.render(e,n.renderTarget,r)):this.mipmapBlurPass.enabled?this.mipmapBlurPass.render(e,t):this.blurPass.render(e,t,r)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t),this.renderTarget.setSize(i.width,i.height),this.blurPass.resolution.copy(i),this.luminancePass.setSize(e,t),this.mipmapBlurPass.setSize(e,t)}initialize(e,t,i){this.blurPass.initialize(e,t,i),this.luminancePass.initialize(e,t,i),this.mipmapBlurPass.initialize(e,t,i),void 0!==i&&(this.renderTarget.texture.type=i,e.outputEncoding===r.sRGBEncoding&&(this.renderTarget.texture.encoding=r.sRGBEncoding))}},Y=class extends q{constructor({blendFunction:e=a.SRC,brightness:t=0,contrast:i=0}={}){super("BrightnessContrastEffect","uniform float brightness;uniform float contrast;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 color=inputColor.rgb+vec3(brightness-0.5);if(contrast>0.0){color/=vec3(1.0-contrast);}else{color*=vec3(1.0+contrast);}outputColor=vec4(color+vec3(0.5),inputColor.a);}",{blendFunction:e,uniforms:new Map([["brightness",new r.Uniform(t)],["contrast",new r.Uniform(i)]])}),this.inputColorSpace=r.sRGBEncoding}get brightness(){return this.uniforms.get("brightness").value}set brightness(e){this.uniforms.get("brightness").value=e}getBrightness(e){return this.brightness}setBrightness(e){this.brightness=e}get contrast(){return this.uniforms.get("contrast").value}set contrast(e){this.uniforms.get("contrast").value=e}getContrast(e){return this.contrast}setContrast(e){this.contrast=e}},Q=class extends q{constructor({blendFunction:e,bits:t=16}={}){super("ColorDepthEffect","uniform float factor;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){outputColor=vec4(floor(inputColor.rgb*factor+0.5)/factor,inputColor.a);}",{blendFunction:e,uniforms:new Map([["factor",new r.Uniform(1)]])}),this.bits=0,this.bitDepth=t}get bitDepth(){return this.bits}set bitDepth(e){this.bits=e,this.uniforms.get("factor").value=Math.pow(2,e/3)}getBitDepth(){return this.bitDepth}setBitDepth(e){this.bitDepth=e}},$=class extends q{constructor({blendFunction:e=a.SRC,inverted:t=!1}={}){super("DepthEffect","void mainImage(const in vec4 inputColor,const in vec2 uv,const in float depth,out vec4 outputColor){\n#ifdef INVERTED\nvec3 color=vec3(1.0-depth);\n#else\nvec3 color=vec3(depth);\n#endif\noutputColor=vec4(color,inputColor.a);}",{blendFunction:e,attributes:o.DEPTH}),this.inverted=t}get inverted(){return this.defines.has("INVERTED")}set inverted(e){this.inverted!==e&&(e?this.defines.set("INVERTED","1"):this.defines.delete("INVERTED"),this.setChanged())}isInverted(){return this.inverted}setInverted(e){this.inverted=e}},J=class extends q{constructor({blendFunction:e,angle:t=.5*Math.PI,scale:i=1}={}){super("DotScreenEffect","uniform vec2 angle;uniform float scale;float pattern(const in vec2 uv){vec2 point=scale*vec2(dot(angle.yx,vec2(uv.x,-uv.y)),dot(angle,uv));return(sin(point.x)*sin(point.y))*4.0;}void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 color=vec3(inputColor.rgb*10.0-5.0+pattern(uv*resolution));outputColor=vec4(color,inputColor.a);}",{blendFunction:e,uniforms:new Map([["angle",new r.Uniform(new r.Vector2)],["scale",new r.Uniform(i)]])}),this.angle=t}get angle(){return Math.acos(this.uniforms.get("angle").value.y)}set angle(e){this.uniforms.get("angle").value.set(Math.sin(e),Math.cos(e))}getAngle(){return this.angle}setAngle(e){this.angle=e}get scale(){return this.uniforms.get("scale").value}set scale(e){this.uniforms.get("scale").value=e}};new r.Vector3,new r.Matrix4;var ee=class extends q{constructor({blendFunction:e=a.SRC,hue:t=0,saturation:i=0}={}){super("HueSaturationEffect","uniform vec3 hue;uniform float saturation;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 color=vec3(dot(inputColor.rgb,hue.xyz),dot(inputColor.rgb,hue.zxy),dot(inputColor.rgb,hue.yzx));float average=(color.r+color.g+color.b)/3.0;vec3 diff=average-color;if(saturation>0.0){color+=diff*(1.0-1.0/(1.001-saturation));}else{color+=diff*-saturation;}outputColor=vec4(min(color,1.0),inputColor.a);}",{blendFunction:e,uniforms:new Map([["hue",new r.Uniform(new r.Vector3)],["saturation",new r.Uniform(i)]])}),this.hue=t}get saturation(){return this.uniforms.get("saturation").value}set saturation(e){this.uniforms.get("saturation").value=e}getSaturation(){return this.saturation}setSaturation(e){this.saturation=e}get hue(){let e=this.uniforms.get("hue").value;return Math.acos((3*e.x-1)/2)}set hue(e){let t=Math.sin(e),i=Math.cos(e);this.uniforms.get("hue").value.set((2*i+1)/3,(-Math.sqrt(3)*t-i+1)/3,(Math.sqrt(3)*t-i+1)/3)}getHue(){return this.hue}setHue(e){this.hue=e}};function et(e,t,i){let r=document.createElement("canvas"),n=r.getContext("2d");if(r.width=e,r.height=t,i instanceof Image)n.drawImage(i,0,0);else{let r=n.createImageData(e,t);r.data.set(i),n.putImageData(r,0,0)}return r}var ei=class{constructor(e=0,t=0,i=null){this.width=e,this.height=t,this.data=i}toCanvas(){return"undefined"==typeof document?null:et(this.width,this.height,this.data)}static from(e){let t;let{width:i,height:r}=e;if(e instanceof Image){let n=et(i,r,e);if(null!==n){let e=n.getContext("2d");t=e.getImageData(0,0,i,r).data}}else t=e.data;return new ei(i,r,t)}},er=new r.Color,en=class extends null{constructor(e,t){super(e,t,t,t),this.type=FloatType5,this.format=RGBAFormat4,this.encoding=LinearEncoding4,this.minFilter=LinearFilter3,this.magFilter=LinearFilter3,this.wrapS=ClampToEdgeWrapping,this.wrapT=ClampToEdgeWrapping,this.wrapR=ClampToEdgeWrapping,this.unpackAlignment=1,this.needsUpdate=!0,this.domainMin=new Vector33(0,0,0),this.domainMax=new Vector33(1,1,1)}get isLookupTexture3D(){return!0}scaleUp(e,t=!0){let i=this.image;return e<=i.width?Promise.reject(Error("The target size must be greater than the current size")):new Promise((r,n)=>{let s=URL.createObjectURL(new Blob(['"use strict";(()=>{var O=Math.pow;var _={SCALE_UP:"lut.scaleup"};var k=[new Float32Array(3),new Float32Array(3)],n=[new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array(3)],Z=[[new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,0,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([1,0,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([0,1,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([0,1,1]),new Float32Array([1,1,1])]];function d(a,t,r,m){let i=r[0]-t[0],e=r[1]-t[1],y=r[2]-t[2],h=a[0]-t[0],A=a[1]-t[1],w=a[2]-t[2],c=e*w-y*A,l=y*h-i*w,x=i*A-e*h,u=Math.sqrt(c*c+l*l+x*x),b=u*.5,s=c/u,F=l/u,f=x/u,p=-(a[0]*s+a[1]*F+a[2]*f),M=m[0]*s+m[1]*F+m[2]*f;return Math.abs(M+p)*b/3}function V(a,t,r,m,i,e){let y=(r+m*t+i*t*t)*4;e[0]=a[y+0],e[1]=a[y+1],e[2]=a[y+2]}function j(a,t,r,m,i,e){let y=r*(t-1),h=m*(t-1),A=i*(t-1),w=Math.floor(y),c=Math.floor(h),l=Math.floor(A),x=Math.ceil(y),u=Math.ceil(h),b=Math.ceil(A),s=y-w,F=h-c,f=A-l;if(w===y&&c===h&&l===A)V(a,t,y,h,A,e);else{let p;s>=F&&F>=f?p=Z[0]:s>=f&&f>=F?p=Z[1]:f>=s&&s>=F?p=Z[2]:F>=s&&s>=f?p=Z[3]:F>=f&&f>=s?p=Z[4]:f>=F&&F>=s&&(p=Z[5]);let[M,g,X,Y]=p,P=k[0];P[0]=s,P[1]=F,P[2]=f;let o=k[1],L=x-w,S=u-c,U=b-l;o[0]=L*M[0]+w,o[1]=S*M[1]+c,o[2]=U*M[2]+l,V(a,t,o[0],o[1],o[2],n[0]),o[0]=L*g[0]+w,o[1]=S*g[1]+c,o[2]=U*g[2]+l,V(a,t,o[0],o[1],o[2],n[1]),o[0]=L*X[0]+w,o[1]=S*X[1]+c,o[2]=U*X[2]+l,V(a,t,o[0],o[1],o[2],n[2]),o[0]=L*Y[0]+w,o[1]=S*Y[1]+c,o[2]=U*Y[2]+l,V(a,t,o[0],o[1],o[2],n[3]);let T=d(g,X,Y,P)*6,q=d(M,X,Y,P)*6,C=d(M,g,Y,P)*6,E=d(M,g,X,P)*6;n[0][0]*=T,n[0][1]*=T,n[0][2]*=T,n[1][0]*=q,n[1][1]*=q,n[1][2]*=q,n[2][0]*=C,n[2][1]*=C,n[2][2]*=C,n[3][0]*=E,n[3][1]*=E,n[3][2]*=E,e[0]=n[0][0]+n[1][0]+n[2][0]+n[3][0],e[1]=n[0][1]+n[1][1]+n[2][1]+n[3][1],e[2]=n[0][2]+n[1][2]+n[2][2]+n[3][2]}}var v=class{static expand(t,r){let m=Math.cbrt(t.length/4),i=new Float32Array(3),e=new t.constructor(O(r,3)*4),y=t instanceof Uint8Array?255:1,h=O(r,2),A=1/(r-1);for(let w=0;w<r;++w)for(let c=0;c<r;++c)for(let l=0;l<r;++l){let x=l*A,u=c*A,b=w*A,s=Math.round(l+c*r+w*h)*4;j(t,m,x,u,b,i),e[s+0]=i[0],e[s+1]=i[1],e[s+2]=i[2],e[s+3]=y}return e}};self.addEventListener("message",a=>{let t=a.data,r=t.data;switch(t.operation){case _.SCALE_UP:r=v.expand(r,t.size);break}postMessage(r,[r.buffer]),close()});})();\n'],{type:"text/javascript"})),a=new Worker(s);a.addEventListener("error",e=>n(e.error)),a.addEventListener("message",t=>{let i=new en(t.data,e);i.encoding=this.encoding,i.type=this.type,i.name=this.name,URL.revokeObjectURL(s),r(i)});let o=t?[i.data.buffer]:[];a.postMessage({operation:h.SCALE_UP,data:i.data,size:e},o)})}applyLUT(e){let t=this.image,i=e.image,r=Math.min(t.width,t.height,t.depth),n=Math.min(i.width,i.height,i.depth);if(r!==n)console.error("Size mismatch");else if(e.type!==FloatType5||this.type!==FloatType5)console.error("Both LUTs must be FloatType textures");else if(e.format!==RGBAFormat4||this.format!==RGBAFormat4)console.error("Both LUTs must be RGBA textures");else{let e=t.data,n=i.data,s=r**2,a=r-1;for(let t=0,i=r**3;t<i;++t){let i=4*t,o=e[i+0]*a,l=e[i+1]*a,u=e[i+2]*a,h=4*Math.round(o+l*r+u*s);e[i+0]=n[h+0],e[i+1]=n[h+1],e[i+2]=n[h+2]}this.needsUpdate=!0}return this}convertToUint8(){if(this.type===FloatType5){let e=this.image.data,t=new Uint8Array(e.length);for(let i=0,r=e.length;i<r;++i)t[i]=255*e[i]+.5;this.image.data=t,this.type=UnsignedByteType15,this.needsUpdate=!0}return this}convertToFloat(){if(this.type===UnsignedByteType15){let e=this.image.data,t=new Float32Array(e.length);for(let i=0,r=e.length;i<r;++i)t[i]=e[i]/255;this.image.data=t,this.type=FloatType5,this.needsUpdate=!0}return this}convertToRGBA(){return console.warn("LookupTexture","convertToRGBA() is deprecated, LUTs are now RGBA by default"),this}convertLinearToSRGB(){let e=this.image.data;if(this.type===FloatType5){for(let t=0,i=e.length;t<i;t+=4)er.fromArray(e,t).convertLinearToSRGB().toArray(e,t);this.encoding=sRGBEncoding13,this.needsUpdate=!0}else console.error("Color space conversion requires FloatType data");return this}convertSRGBToLinear(){let e=this.image.data;if(this.type===FloatType5){for(let t=0,i=e.length;t<i;t+=4)er.fromArray(e,t).convertSRGBToLinear().toArray(e,t);this.encoding=LinearEncoding4,this.needsUpdate=!0}else console.error("Color space conversion requires FloatType data");return this}toDataTexture(){let e=this.image.width,t=this.image.height*this.image.depth,i=new DataTexture2(this.image.data,e,t);return i.name=this.name,i.type=this.type,i.format=this.format,i.encoding=this.encoding,i.minFilter=LinearFilter3,i.magFilter=LinearFilter3,i.wrapS=this.wrapS,i.wrapT=this.wrapT,i.generateMipmaps=!1,i.needsUpdate=!0,i}static from(e){let t;let i=e.image,{width:r,height:n}=i,s=Math.min(r,n);if(i instanceof Image){let e=ei.from(i),a=e.data;if(r>n){t=new Uint8Array(a.length);for(let e=0;e<s;++e)for(let i=0;i<s;++i)for(let r=0;r<s;++r){let n=(r+e*s+i*s*s)*4,o=(r+i*s+e*s*s)*4;t[o+0]=a[n+0],t[o+1]=a[n+1],t[o+2]=a[n+2],t[o+3]=a[n+3]}}else t=new Uint8Array(a.buffer)}else t=i.data.slice();let a=new en(t,s);return a.encoding=e.encoding,a.type=e.type,a.name=e.name,a}static createNeutral(e){let t=new Float32Array(e**3*4),i=e**2,r=1/(e-1);for(let n=0;n<e;++n)for(let s=0;s<e;++s)for(let a=0;a<e;++a){let o=(n+s*e+a*i)*4;t[o+0]=n*r,t[o+1]=s*r,t[o+2]=a*r,t[o+3]=1}let n=new en(t,e);return n.name="neutral",n}};function es(e,t,i,r){var n;return(n=e+(t-e)*.75)+(i+(r-i)*.75-n)*.875}new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1]),new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,0,1]),new Float32Array([1,1,1]),new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([1,0,1]),new Float32Array([1,1,1]),new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1]),new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([0,1,1]),new Float32Array([1,1,1]),new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([0,1,1]),new Float32Array([1,1,1]),new Float32Array(2),new Float32Array(2),new Float32Array([0,-.25,.25,-.125,.125,-.375,.375]),new Float32Array([0,0]),new Float32Array([.25,-.25]),new Float32Array([-.25,.25]),new Float32Array([.125,-.125]),new Float32Array([-.125,.125]),new Uint8Array([0,0]),new Uint8Array([3,0]),new Uint8Array([0,3]),new Uint8Array([3,3]),new Uint8Array([1,0]),new Uint8Array([4,0]),new Uint8Array([1,3]),new Uint8Array([4,3]),new Uint8Array([0,1]),new Uint8Array([3,1]),new Uint8Array([0,4]),new Uint8Array([3,4]),new Uint8Array([1,1]),new Uint8Array([4,1]),new Uint8Array([1,4]),new Uint8Array([4,4]),new Uint8Array([0,0]),new Uint8Array([1,0]),new Uint8Array([0,2]),new Uint8Array([1,2]),new Uint8Array([2,0]),new Uint8Array([3,0]),new Uint8Array([2,2]),new Uint8Array([3,2]),new Uint8Array([0,1]),new Uint8Array([1,1]),new Uint8Array([0,3]),new Uint8Array([1,3]),new Uint8Array([2,1]),new Uint8Array([3,1]),new Uint8Array([2,3]),new Uint8Array([3,3]),es(0,0,0,0),new Float32Array([0,0,0,0]),es(0,0,0,1),new Float32Array([0,0,0,1]),es(0,0,1,0),new Float32Array([0,0,1,0]),es(0,0,1,1),new Float32Array([0,0,1,1]),es(0,1,0,0),new Float32Array([0,1,0,0]),es(0,1,0,1),new Float32Array([0,1,0,1]),es(0,1,1,0),new Float32Array([0,1,1,0]),es(0,1,1,1),new Float32Array([0,1,1,1]),es(1,0,0,0),new Float32Array([1,0,0,0]),es(1,0,0,1),new Float32Array([1,0,0,1]),es(1,0,1,0),new Float32Array([1,0,1,0]),es(1,0,1,1),new Float32Array([1,0,1,1]),es(1,1,0,0),new Float32Array([1,1,0,0]),es(1,1,0,1),new Float32Array([1,1,0,1]),es(1,1,1,0),new Float32Array([1,1,1,0]),es(1,1,1,1),new Float32Array([1,1,1,1]);var ea=class extends q{constructor({blendFunction:e=a.SCREEN,premultiply:t=!1}={}){super("NoiseEffect","void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 noise=vec3(rand(uv*time));\n#ifdef PREMULTIPLY\noutputColor=vec4(min(inputColor.rgb*noise,vec3(1.0)),inputColor.a);\n#else\noutputColor=vec4(noise,inputColor.a);\n#endif\n}",{blendFunction:e}),this.premultiply=t}get premultiply(){return this.defines.has("PREMULTIPLY")}set premultiply(e){this.premultiply!==e&&(e?this.defines.set("PREMULTIPLY","1"):this.defines.delete("PREMULTIPLY"),this.setChanged())}isPremultiplied(){return this.premultiply}setPremultiplied(e){this.premultiply=e}},eo=class extends q{constructor({blendFunction:e=a.OVERLAY,density:t=1.25,scrollSpeed:i=0}={}){super("ScanlineEffect","uniform float count;\n#ifdef SCROLL\nuniform float scrollSpeed;\n#endif\nvoid mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){float y=uv.y;\n#ifdef SCROLL\ny+=time*scrollSpeed;\n#endif\nvec2 sl=vec2(sin(y*count),cos(y*count));outputColor=vec4(sl.xyx,inputColor.a);}",{blendFunction:e,uniforms:new Map([["count",new r.Uniform(0)],["scrollSpeed",new r.Uniform(0)]])}),this.resolution=new r.Vector2,this.d=t,this.scrollSpeed=i}get density(){return this.d}set density(e){this.d=e,this.setSize(this.resolution.width,this.resolution.height)}getDensity(){return this.density}setDensity(e){this.density=e}get scrollSpeed(){return this.uniforms.get("scrollSpeed").value}set scrollSpeed(e){this.uniforms.get("scrollSpeed").value=e,0===e?this.defines.delete("SCROLL")&&this.setChanged():this.defines.has("SCROLL")||(this.defines.set("SCROLL","1"),this.setChanged())}setSize(e,t){this.resolution.set(e,t),this.uniforms.get("count").value=Math.round(t*this.density)}},el=.5*Math.PI,eu=new r.Vector3,eh=new r.Vector3,ec=class extends q{constructor(e,t=new r.Vector3,{speed:i=2,maxRadius:n=1,waveSize:s=.2,amplitude:a=.05}={}){super("ShockWaveEffect","uniform bool active;uniform vec2 center;uniform float waveSize;uniform float radius;uniform float maxRadius;uniform float amplitude;varying float vSize;void mainUv(inout vec2 uv){if(active){vec2 aspectCorrection=vec2(aspect,1.0);vec2 difference=uv*aspectCorrection-center*aspectCorrection;float distance=sqrt(dot(difference,difference))*vSize;if(distance>radius){if(distance<radius+waveSize){float angle=(distance-radius)*PI2/waveSize;float cosSin=(1.0-cos(angle))*0.5;float extent=maxRadius+waveSize;float decay=max(extent-distance*distance,0.0)/extent;uv-=((cosSin*amplitude*difference)/distance)*decay;}}}}",{vertexShader:"uniform float size;uniform float cameraDistance;varying float vSize;void mainSupport(){vSize=(0.1*cameraDistance)/size;}",uniforms:new Map([["active",new r.Uniform(!1)],["center",new r.Uniform(new r.Vector2(.5,.5))],["cameraDistance",new r.Uniform(1)],["size",new r.Uniform(1)],["radius",new r.Uniform(-s)],["maxRadius",new r.Uniform(n)],["waveSize",new r.Uniform(s)],["amplitude",new r.Uniform(a)]])}),this.position=t,this.speed=i,this.camera=e,this.screenPosition=this.uniforms.get("center").value,this.time=0,this.active=!1}set mainCamera(e){this.camera=e}get amplitude(){return this.uniforms.get("amplitude").value}set amplitude(e){this.uniforms.get("amplitude").value=e}get waveSize(){return this.uniforms.get("waveSize").value}set waveSize(e){this.uniforms.get("waveSize").value=e}get maxRadius(){return this.uniforms.get("maxRadius").value}set maxRadius(e){this.uniforms.get("maxRadius").value=e}get epicenter(){return this.position}set epicenter(e){this.position=e}getPosition(){return this.position}setPosition(e){this.position=e}getSpeed(){return this.speed}setSpeed(e){this.speed=e}explode(){this.time=0,this.active=!0,this.uniforms.get("active").value=!0}update(e,t,i){let r=this.position,n=this.camera,s=this.uniforms,a=s.get("active");if(this.active){let e=s.get("waveSize").value;n.getWorldDirection(eu),eh.copy(n.position).sub(r),a.value=eu.angleTo(eh)>el,a.value&&(s.get("cameraDistance").value=n.position.distanceTo(r),eu.copy(r).project(n),this.screenPosition.set((eu.x+1)*.5,(eu.y+1)*.5)),this.time+=i*this.speed;let t=this.time-e;s.get("radius").value=t,t>=(s.get("maxRadius").value+e)*2&&(this.active=!1,a.value=!1)}}},ed=class extends q{constructor({blendFunction:e,intensity:t=1}={}){super("SepiaEffect","uniform vec3 weightsR;uniform vec3 weightsG;uniform vec3 weightsB;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 color=vec3(dot(inputColor.rgb,weightsR),dot(inputColor.rgb,weightsG),dot(inputColor.rgb,weightsB));outputColor=vec4(color,inputColor.a);}",{blendFunction:e,uniforms:new Map([["weightsR",new r.Uniform(new r.Vector3(.393,.769,.189))],["weightsG",new r.Uniform(new r.Vector3(.349,.686,.168))],["weightsB",new r.Uniform(new r.Vector3(.272,.534,.131))]])})}get intensity(){return this.blendMode.opacity.value}set intensity(e){this.blendMode.opacity.value=e}getIntensity(){return this.intensity}setIntensity(e){this.intensity=e}get weightsR(){return this.uniforms.get("weightsR").value}get weightsG(){return this.uniforms.get("weightsG").value}get weightsB(){return this.uniforms.get("weightsB").value}},ef=class extends q{constructor({blendFunction:e,offset:t=0,rotation:i=0,focusArea:n=.4,feather:s=.3,kernelSize:a=u.MEDIUM,resolutionScale:o=.5,resolutionX:l=C.AUTO_SIZE,resolutionY:h=C.AUTO_SIZE}={}){super("TiltShiftEffect","#ifdef FRAMEBUFFER_PRECISION_HIGH\nuniform mediump sampler2D map;\n#else\nuniform lowp sampler2D map;\n#endif\nuniform vec2 maskParams;varying vec2 vUv2;float linearGradientMask(const in float x){return step(maskParams.x,x)-step(maskParams.y,x);}void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){float mask=linearGradientMask(vUv2.y);vec4 texel=texture2D(map,uv);outputColor=mix(texel,inputColor,mask);}",{vertexShader:"uniform vec2 rotation;varying vec2 vUv2;void mainSupport(const in vec2 uv){vUv2=(uv-0.5)*2.0*vec2(aspect,1.0);vUv2=vec2(dot(rotation,vUv2),dot(rotation,vec2(vUv2.y,-vUv2.x)));}",blendFunction:e,uniforms:new Map([["rotation",new r.Uniform(new r.Vector2)],["maskParams",new r.Uniform(new r.Vector2)],["map",new r.Uniform(null)]])}),this._offset=t,this._focusArea=n,this._feather=s,this.renderTarget=new r.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="TiltShift.Target",this.uniforms.get("map").value=this.renderTarget.texture,this.blurPass=new k({kernelSize:a,resolutionScale:o,resolutionX:l,resolutionY:h,offset:t,rotation:i,focusArea:n,feather:s});let c=this.resolution=new C(this,l,h,o);c.addEventListener("change",e=>this.setSize(c.baseWidth,c.baseHeight)),this.rotation=i,this.updateParams()}updateParams(){let e=this.uniforms.get("maskParams").value,t=Math.max(this.focusArea-this.feather,0);e.set(this.offset-t,this.offset+t)}get rotation(){return Math.acos(this.uniforms.get("rotation").value.x)}set rotation(e){this.uniforms.get("rotation").value.set(Math.cos(e),Math.sin(e)),this.blurPass.blurMaterial.rotation=e}get offset(){return this._offset}set offset(e){this._offset=e,this.blurPass.blurMaterial.offset=e,this.updateParams()}get focusArea(){return this._focusArea}set focusArea(e){this._focusArea=e,this.blurPass.blurMaterial.focusArea=e,this.updateParams()}get feather(){return this._feather}set feather(e){this._feather=e,this.blurPass.blurMaterial.feather=e,this.updateParams()}get bias(){return 0}set bias(e){}update(e,t,i){this.blurPass.render(e,t,this.renderTarget)}setSize(e,t){let i=this.resolution;i.setBaseSize(e,t),this.renderTarget.setSize(i.width,i.height),this.blurPass.resolution.copy(i)}initialize(e,t,i){this.blurPass.initialize(e,t,i),void 0!==i&&(this.renderTarget.texture.type=i,e.outputEncoding===r.sRGBEncoding&&(this.renderTarget.texture.encoding=r.sRGBEncoding))}},ev=class extends q{constructor({blendFunction:e=a.SRC,adaptive:t=!0,mode:i=t?c.REINHARD2_ADAPTIVE:c.REINHARD2,resolution:n=256,maxLuminance:s=16,whitePoint:o=s,middleGrey:l=.6,minLuminance:u=.01,averageLuminance:h=1,adaptationRate:d=1}={}){super("ToneMappingEffect","#include <tonemapping_pars_fragment>\n#if THREE_REVISION < 143\n#define luminance(v) linearToRelativeLuminance(v)\n#endif\nuniform lowp sampler2D luminanceBuffer;uniform float whitePoint;uniform float middleGrey;\n#if TONE_MAPPING_MODE != 2\nuniform float averageLuminance;\n#endif\nvec3 Reinhard2ToneMapping(vec3 color){color*=toneMappingExposure;float l=luminance(color);\n#if TONE_MAPPING_MODE == 2\nfloat lumAvg=unpackRGBAToFloat(texture2D(luminanceBuffer,vec2(0.5)));\n#else\nfloat lumAvg=averageLuminance;\n#endif\nfloat lumScaled=(l*middleGrey)/max(lumAvg,1e-6);float lumCompressed=lumScaled*(1.0+lumScaled/(whitePoint*whitePoint));lumCompressed/=(1.0+lumScaled);return clamp(lumCompressed*color,0.0,1.0);}void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){\n#if TONE_MAPPING_MODE == 1 || TONE_MAPPING_MODE == 2\noutputColor=vec4(Reinhard2ToneMapping(inputColor.rgb),inputColor.a);\n#else\noutputColor=vec4(toneMapping(inputColor.rgb),inputColor.a);\n#endif\n}",{blendFunction:e,uniforms:new Map([["luminanceBuffer",new r.Uniform(null)],["maxLuminance",new r.Uniform(s)],["whitePoint",new r.Uniform(o)],["middleGrey",new r.Uniform(l)],["averageLuminance",new r.Uniform(h)]])}),this.renderTargetLuminance=new r.WebGLRenderTarget(1,1,{minFilter:r.LinearMipmapLinearFilter,depthBuffer:!1}),this.renderTargetLuminance.texture.generateMipmaps=!0,this.renderTargetLuminance.texture.name="Luminance",this.luminancePass=new N({renderTarget:this.renderTargetLuminance}),this.adaptiveLuminancePass=new M(this.luminancePass.texture,{minLuminance:u,adaptationRate:d}),this.uniforms.get("luminanceBuffer").value=this.adaptiveLuminancePass.texture,this.resolution=n,this.mode=i}get mode(){return Number(this.defines.get("TONE_MAPPING_MODE"))}set mode(e){if(this.mode!==e){switch(this.defines.clear(),this.defines.set("TONE_MAPPING_MODE",e.toFixed(0)),e){case c.REINHARD:this.defines.set("toneMapping(texel)","ReinhardToneMapping(texel)");break;case c.OPTIMIZED_CINEON:this.defines.set("toneMapping(texel)","OptimizedCineonToneMapping(texel)");break;case c.ACES_FILMIC:this.defines.set("toneMapping(texel)","ACESFilmicToneMapping(texel)");break;default:this.defines.set("toneMapping(texel)","texel")}this.adaptiveLuminancePass.enabled=e===c.REINHARD2_ADAPTIVE,this.setChanged()}}getMode(){return this.mode}setMode(e){this.mode=e}get whitePoint(){return this.uniforms.get("whitePoint").value}set whitePoint(e){this.uniforms.get("whitePoint").value=e}get middleGrey(){return this.uniforms.get("middleGrey").value}set middleGrey(e){this.uniforms.get("middleGrey").value=e}get averageLuminance(){return this.uniforms.get("averageLuminance").value}set averageLuminance(e){this.uniforms.get("averageLuminance").value=e}get adaptiveLuminanceMaterial(){return this.adaptiveLuminancePass.fullscreenMaterial}getAdaptiveLuminanceMaterial(){return this.adaptiveLuminanceMaterial}get resolution(){return this.luminancePass.resolution.width}set resolution(e){let t=Math.max(0,Math.ceil(Math.log2(e))),i=Math.pow(2,t);this.luminancePass.resolution.setPreferredSize(i,i),this.adaptiveLuminanceMaterial.mipLevel1x1=t}getResolution(){return this.resolution}setResolution(e){this.resolution=e}get adaptive(){return this.mode===c.REINHARD2_ADAPTIVE}set adaptive(e){this.mode=e?c.REINHARD2_ADAPTIVE:c.REINHARD2}get adaptationRate(){return this.adaptiveLuminanceMaterial.adaptationRate}set adaptationRate(e){this.adaptiveLuminanceMaterial.adaptationRate=e}get distinction(){return console.warn(this.name,"distinction was removed."),1}set distinction(e){console.warn(this.name,"distinction was removed.")}update(e,t,i){this.adaptiveLuminancePass.enabled&&(this.luminancePass.render(e,t),this.adaptiveLuminancePass.render(e,null,null,i))}initialize(e,t,i){this.adaptiveLuminancePass.initialize(e,t,i)}},ep=class extends q{constructor({blendFunction:e,technique:t=d.DEFAULT,eskil:i=!1,offset:n=.5,darkness:s=.5}={}){super("VignetteEffect","uniform float offset;uniform float darkness;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){const vec2 center=vec2(0.5);vec3 color=inputColor.rgb;\n#if VIGNETTE_TECHNIQUE == 0\nfloat d=distance(uv,center);color*=smoothstep(0.8,offset*0.799,d*(darkness+offset));\n#else\nvec2 coord=(uv-center)*vec2(offset);color=mix(color,vec3(1.0-darkness),dot(coord,coord));\n#endif\noutputColor=vec4(color,inputColor.a);}",{blendFunction:e,defines:new Map([["VIGNETTE_TECHNIQUE",t.toFixed(0)]]),uniforms:new Map([["offset",new r.Uniform(n)],["darkness",new r.Uniform(s)]])})}get technique(){return Number(this.defines.get("VIGNETTE_TECHNIQUE"))}set technique(e){this.technique!==e&&(this.defines.set("VIGNETTE_TECHNIQUE",e.toFixed(0)),this.setChanged())}get eskil(){return this.technique===d.ESKIL}set eskil(e){this.technique=e?d.ESKIL:d.DEFAULT}getTechnique(){return this.technique}setTechnique(e){this.technique=e}get offset(){return this.uniforms.get("offset").value}set offset(e){this.uniforms.get("offset").value=e}getOffset(){return this.offset}setOffset(e){this.offset=e}get darkness(){return this.uniforms.get("darkness").value}set darkness(e){this.uniforms.get("darkness").value=e}getDarkness(){return this.darkness}setDarkness(e){this.darkness=e}}}}]);