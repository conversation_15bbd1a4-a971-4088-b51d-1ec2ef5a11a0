{"pageProps": {"page": {"id": "ZAxBmhAAABWUXl6k", "uid": "the-yes", "url": "/project/the-yes", "type": "project", "href": "https://design-is-funny.cdn.prismic.io/api/v2/documents/search?ref=Z0hw5BEAAB8At7Hg&q=%5B%5B%3Ad+%3D+at%28document.id%2C+%22ZAxBmhAAABWUXl6k%22%29+%5D%5D", "tags": [], "first_publication_date": "2023-03-11T08:53:50+0000", "last_publication_date": "2023-05-04T08:10:08+0000", "slugs": ["the-yes"], "linked_documents": [], "lang": "en-gb", "alternate_languages": [], "data": {"title": [{"type": "heading1", "text": "The Yes", "spans": []}], "hyphenated_title": [], "type": "Brand / Mobile App Design", "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/c9f99eaa-9fc3-432b-b33d-886c7bf7663d_Cover.png?auto=compress,format&rect=0,50,1600,800&w=3200&h=1600", "id": "ZCLIGxAAACIAwAdl", "edit": {"x": 0, "y": -100, "zoom": 2, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/c9f99eaa-9fc3-432b-b33d-886c7bf7663d_Cover.png?auto=compress,format&rect=0,29,1600,840&w=1200&h=630", "id": "ZCLIGxAAACIAwAdl", "edit": {"x": 0, "y": -22, "zoom": 0.75, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/c9f99eaa-9fc3-432b-b33d-886c7bf7663d_Cover.png?auto=compress,format&rect=350,0,900,900&w=3200&h=3200", "id": "ZCLIGxAAACIAwAdl", "edit": {"x": -1244, "y": 0, "zoom": 3.5555555555555554, "background": "transparent"}}}, "skills": [{"item": "UX"}, {"item": "Visual"}, {"item": "Motion"}, {"item": "Brand"}], "slices": [{"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "Project overview", "spans": []}], "first_column": [{"type": "paragraph", "text": "The Yes app uses a simple approach to create a personalized online shopping experience. It starts with a quiz to learn a person's retail preferences and offers items from their favorite brands.", "spans": []}], "second_column": [{"type": "paragraph", "text": "The app becomes smarter with each use and also takes into account a person's dislikes to create a unique shopping experience.", "spans": []}]}, "id": "paragraph$2162e17c-350c-49a7-917a-3717a5a99ae2", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"video_1080p": {"link_type": "Media", "kind": "file", "id": "ZFNoWREAACAAmTT0", "url": "https://design-is-funny.cdn.prismic.io/design-is-funny/9c692d37-2105-49d5-bdcb-1d48cc81d40c_Main+Composition.mp4", "name": "Main Composition.mp4", "size": "9379174"}, "video_720p": {"link_type": "Media"}, "video_540p": {"link_type": "Media"}, "video_360p": {"link_type": "Media"}}, "id": "video$9a3abbdc-4900-4c9f-9f4a-0ad511a9141d", "slice_type": "video", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "The Beta Brand", "spans": []}], "first_column": [{"type": "paragraph", "text": "The team of “The Yes” had already made significant progress before seeking our assistance. They tested and improved the app's functionality and flow after a beta launch to family and friends.", "spans": []}], "second_column": [{"type": "paragraph", "text": "They needed MetaLab help to add the bring some refinement to the brand and add some finishing touches to the whole aesthetic that would bring joy to the shopping experience.", "spans": []}]}, "id": "paragraph$8497dda8-42d5-4e4c-997f-fc324544848f", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/6ee2cf71-3f37-4785-83bc-0e46cdd4ba81_018.jpg?auto=compress,format", "id": "ZCMGPBAAACMAwPj7", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$d940828d-0a38-4672-9387-03f8d6a40bf9", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/8614e16d-9fbb-454e-bf11-6e350dc273df_paper.jpg?auto=compress,format", "id": "ZCMGOhAAAB8AwPj2", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$1560c3dd-b0c6-4b14-bd92-1dd324adbad5", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"title": [{"type": "heading2", "text": "The Design", "spans": []}], "first_column": [{"type": "paragraph", "text": "Before launch, The Yes app was almost ready, but some important features needed improvement. However, making big changes could delay the launch, so we had to find a way to balance innovation with time constraints. ", "spans": []}], "second_column": [{"type": "paragraph", "text": "We wanted to showcase The Yes app's branding without overwhelming users. We used animations and transitions to create subtle moments of delight and even incorporated the brand's typography into the calls-to-action. Our goal was to maintain consistency and highlight the brand's core value while also improving the app's conversion rates.", "spans": []}]}, "id": "paragraph$4d8f036e-8cfb-4554-bae6-a9353bfacd25", "slice_type": "paragraph", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/68c01775-13d7-454d-86ad-601517a407b5_01.jpg?auto=compress,format", "id": "ZCMIRBAAAB8AwPyn", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$733a2279-5625-404b-ac1c-7e62488e913d", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/34acd455-5846-4047-964a-a35284395e42_02.jpg?auto=compress,format", "id": "ZCMIRRAAACEAwPyq", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$fd480d34-2de4-422a-8d5a-68953b7bd9e2", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/60fea0f8-d3e3-4cf1-9509-e20c50efe640_03.jpg?auto=compress,format", "id": "ZCMIRhAAACEAwPys", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "#fff"}}}, "id": "image$43176259-af3e-4f4c-9244-5b214fe79af7", "slice_type": "image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{}], "primary": {"image": {"dimensions": {"width": 1920, "height": 1440}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/b985f974-3464-45ed-9329-bd343b91aade_parallax+1.png?auto=compress,format", "id": "ZCapfhAAACIAxvD-", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, "id": "parallax_image$71f5c308-16b7-4f09-90c5-1001b87412dd", "slice_type": "parallax_image", "slice_label": null}, {"variation": "default", "version": "sktwi1xtmkfgx8626", "items": [{"image": {"dimensions": {"width": 1080, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/fb0ddb02-518a-429f-891b-e62ed9994731_Frame+4.png?auto=compress,format", "id": "ZCMmHhAAACIAwS-E", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, {"image": {"dimensions": {"width": 1080, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/8144c43b-4624-4811-b2f8-59392612c294_Frame+5.png?auto=compress,format", "id": "ZCMmHhAAACIAwS-F", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, {"image": {"dimensions": {"width": 1080, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/2f095ad9-b812-4f46-b9d7-170cb9ebb78b_Frame+6.png?auto=compress,format", "id": "ZCMmHRAAACEAwS-B", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, {"image": {"dimensions": {"width": 1080, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/4a4529cd-0971-4535-856f-b180f117b983_Frame+7.png?auto=compress,format", "id": "ZCMmHhAAAB8AwS-D", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}, {"image": {"dimensions": {"width": 1080, "height": 1080}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/010e93ad-0e9d-48d2-9070-6ad24e5d1e76_Frame+8.png?auto=compress,format", "id": "ZCMmHhAAACIAwS-G", "edit": {"x": 0, "y": 0, "zoom": 1, "background": "transparent"}}}], "primary": {}, "id": "infinite_image_carousel$aa8ad0a5-b499-4d18-99de-2828c11f8589", "slice_type": "infinite_image_carousel", "slice_label": null}], "meta_description": []}}, "nextProject": {"id": "ZAxBZRAAACkAXl5M", "type": "project", "tags": [], "lang": "en-gb", "slug": "google-ac", "first_publication_date": "2023-03-11T08:52:56+0000", "last_publication_date": "2023-05-04T07:15:54+0000", "uid": "google-arts-culture", "url": "/project/google-arts-culture", "data": {"title": [{"type": "heading1", "text": "Google A&C", "spans": []}], "image": {"dimensions": {"width": 3200, "height": 1600}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=0,60,1920,960&w=3200&h=1600", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": 0, "y": -100, "zoom": 1.6666666666666667, "background": "transparent"}, "social": {"dimensions": {"width": 1200, "height": 630}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=0,35,1920,1008&w=1200&h=630", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": 0, "y": -22, "zoom": 0.625, "background": "transparent"}}, "square": {"dimensions": {"width": 3200, "height": 3200}, "alt": null, "copyright": null, "url": "https://images.prismic.io/design-is-funny/898ff732-3a60-4d61-a654-f80e21a404b1_Cover.png?auto=compress,format&rect=420,0,1080,1080&w=3200&h=3200", "id": "ZCVUmxAAACIAxLg7", "edit": {"x": -1244, "y": 0, "zoom": 2.962962962962963, "background": "transparent"}}}}, "link_type": "Document", "isBroken": false}}, "__N_SSG": true}